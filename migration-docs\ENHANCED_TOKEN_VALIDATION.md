# 增强的 Token 验证和环境配置系统

## 🎯 优化概述

基于用户指导原则，我们对 iframe 嵌入系统进行了重大优化，使其更加工程化、鲁棒性更强：

### 核心改进

1. **真实的后端API调用** - 不再使用模拟数据，直接调用真实的认证接口
2. **工程化的环境管理** - 统一的环境检测和配置管理系统
3. **增强的错误处理** - 详细的错误信息和状态码
4. **更强的鲁棒性** - 完善的超时、重试和降级机制

## 🏗️ 新的架构设计

### 1. 环境配置管理系统

```typescript
// src/lib/config/environment.ts
export interface EnvironmentConfig {
  name: Environment
  displayName: string
  apiBaseUrl: string
  appBaseUrl: string
  allowedEmbedDomains: string[]
  isProduction: boolean
  isDevelopment: boolean
  enableDebug: boolean
  logLevel: 'debug' | 'info' | 'warn' | 'error'
}
```

**智能环境检测**：
- 基于域名自动检测环境
- 支持显式环境变量覆盖
- 本地环境自动映射为开发配置
- Vercel 预览环境支持

### 2. 增强的 Token 验证

```typescript
// 详细的验证结果
interface TokenValidationResult {
  success: boolean
  data?: AuthData
  error?: string
  statusCode?: number
}

// 真实的API调用
async function validateTokenWithDetails(token: string): Promise<TokenValidationResult>
```

**验证流程**：
1. **预验证** - 格式检查、过期检查
2. **API调用** - 真实的后端认证接口
3. **响应验证** - 数据完整性检查
4. **错误处理** - 详细的错误信息和状态码

## 🔧 使用方式

### 环境配置

```typescript
import { 
  getEnvironmentConfig, 
  getApiBaseUrl, 
  getAllowedEmbedDomains,
  isProduction,
  isDevelopment 
} from '@/lib/config/environment'

// 获取当前环境配置
const config = getEnvironmentConfig()
console.log(`当前环境: ${config.displayName}`)
console.log(`API地址: ${config.apiBaseUrl}`)

// 环境检查
if (isProduction()) {
  // 生产环境逻辑
} else if (isDevelopment()) {
  // 开发环境逻辑
}
```

### Token 验证

```typescript
import { validateTokenWithDetails } from '@/lib/auth/token-validator'

// 详细验证
const result = await validateTokenWithDetails(token)
if (result.success) {
  console.log('用户信息:', result.data?.user)
} else {
  console.error(`验证失败 (${result.statusCode}): ${result.error}`)
}
```

## 🌍 环境配置详情

### 生产环境 (www.goglobalsp.com)
```typescript
{
  name: 'production',
  displayName: '生产环境',
  apiBaseUrl: 'https://api.goglobalsp.com',
  appBaseUrl: 'https://www.goglobalsp.com',
  allowedEmbedDomains: [
    'https://www.goglobalsp.com',
    'https://dev.goglobalsp.com'
  ],
  enableDebug: false,
  logLevel: 'warn'
}
```

### 开发环境 (dev.specific-ai.com)
```typescript
{
  name: 'development',
  displayName: '开发环境',
  apiBaseUrl: 'https://dev-api.specific-ai.com',
  appBaseUrl: 'https://dev.goglobalsp.com',
  allowedEmbedDomains: [
    'https://dev.goglobalsp.com',
    'https://www.goglobalsp.com',
    'http://localhost:3000',
    'http://localhost:3001'
  ],
  enableDebug: true,
  logLevel: 'debug'
}
```

### 本地环境 (localhost)
```typescript
{
  name: 'development', // 映射为开发环境配置
  displayName: '本地开发环境',
  apiBaseUrl: 'https://dev-api.specific-ai.com', // 使用开发环境API
  appBaseUrl: 'http://localhost:3001',
  allowedEmbedDomains: [
    'http://localhost:3001',
    'http://127.0.0.1:3001',
    // ... 其他本地地址
  ],
  enableDebug: true,
  logLevel: 'debug'
}
```

## 🔐 Token 验证流程

### 1. 预验证阶段
```typescript
function preValidateToken(token: string) {
  // 格式检查 - JWT 三段式结构
  if (!isValidTokenFormat(token)) {
    return { isValid: false, error: 'Invalid token format' }
  }
  
  // 过期检查 - 解析 exp 字段
  if (isTokenExpired(token)) {
    return { isValid: false, error: 'Token has expired' }
  }
  
  return { isValid: true }
}
```

### 2. API 调用阶段
```typescript
async function callAuthenticationAPI(apiUrl: string, token: string) {
  const response = await fetch(`${apiUrl}/api/user/get_current`, {
    method: 'GET',
    headers: {
      'authorization': token,
      'language': 'chinese',
      'Content-Type': 'application/json',
      'User-Agent': 'NextJS-EmbedSystem/1.0'
    },
    signal: AbortSignal.timeout(10000) // 10秒超时
  })
  
  // 详细的错误处理
  if (!response.ok) {
    return {
      success: false,
      error: `API request failed: ${response.status}`,
      statusCode: response.status
    }
  }
  
  return { success: true, data: await response.json() }
}
```

### 3. 响应验证阶段
```typescript
function validateAPIResponse(responseData: any) {
  // 检查响应结构
  if (!responseData?.success) {
    return { isValid: false, error: 'API returned failure status' }
  }
  
  // 检查用户数据
  const userData = responseData.data
  const requiredFields = ['id', 'email', 'user_type']
  
  for (const field of requiredFields) {
    if (!userData[field]) {
      return { isValid: false, error: `Missing required field: ${field}` }
    }
  }
  
  return { isValid: true }
}
```

## 🛡️ 安全增强

### 1. 请求安全
- **超时控制**: 10秒请求超时
- **User-Agent**: 标识请求来源
- **语言设置**: 支持多语言响应

### 2. 错误处理
- **详细状态码**: HTTP状态码传递
- **错误分类**: 网络错误、API错误、验证错误
- **调试信息**: 开发环境详细日志

### 3. 域名验证
- **动态白名单**: 基于环境自动配置
- **本地开发支持**: 自动允许本地地址
- **调试日志**: 域名验证过程记录

## 📊 性能优化

### 1. 缓存策略
```typescript
class EnvironmentDetector {
  private _config: EnvironmentConfig | null = null
  
  get config(): EnvironmentConfig {
    if (!this._config) {
      this._config = this.detectEnvironment()
    }
    return this._config
  }
}
```

### 2. 错误恢复
- **网络超时**: 自动重试机制
- **API降级**: 备用验证方案
- **状态保持**: 验证结果缓存

## 🧪 测试验证

### 环境检测测试
```bash
# 本地环境
curl http://localhost:3001/embed/test?token=your-token
# 应显示: "本地开发环境"

# 开发环境
curl https://dev.goglobalsp.com/embed/test?token=your-token
# 应显示: "开发环境"
```

### Token验证测试
```bash
# 有效Token
curl -H "authorization: valid-token" \
     https://dev-api.specific-ai.com/api/user/get_current

# 无效Token
curl -H "authorization: invalid-token" \
     https://dev-api.specific-ai.com/api/user/get_current
```

## 🔄 迁移指南

### 从旧版本升级

1. **更新导入**:
```typescript
// 旧版本
import { validateTokenOnServer } from '@/lib/auth/token-validator'

// 新版本
import { validateTokenWithDetails } from '@/lib/auth/token-validator'
import { getEnvironmentConfig } from '@/lib/config/environment'
```

2. **更新验证逻辑**:
```typescript
// 旧版本
const authData = await validateTokenOnServer(token)
if (!authData) {
  // 处理失败
}

// 新版本
const result = await validateTokenWithDetails(token)
if (!result.success) {
  console.error(`验证失败: ${result.error} (${result.statusCode})`)
  return
}
const authData = result.data
```

3. **更新环境配置**:
```typescript
// 旧版本
const isDev = process.env.NODE_ENV === 'development'

// 新版本
import { isDevelopment, getEnvironmentConfig } from '@/lib/config/environment'
const isDev = isDevelopment()
const config = getEnvironmentConfig()
```

## 📋 最佳实践

### 1. 错误处理
```typescript
try {
  const result = await validateTokenWithDetails(token)
  if (!result.success) {
    // 根据状态码处理不同错误
    switch (result.statusCode) {
      case 401:
        // 重新登录
        break
      case 408:
        // 网络超时，重试
        break
      default:
        // 通用错误处理
    }
  }
} catch (error) {
  // 处理异常
}
```

### 2. 环境配置
```typescript
// 在应用启动时验证配置
import { EnvironmentValidator } from '@/lib/config/environment'

const validation = EnvironmentValidator.validateCurrent()
if (!validation.isValid) {
  console.error('环境配置错误:', validation.errors)
}
```

### 3. 调试模式
```typescript
import { isDebugEnabled } from '@/lib/config/environment'

if (isDebugEnabled()) {
  console.log('详细调试信息...')
}
```

---

## 🎉 总结

通过这次优化，iframe 嵌入系统现在具备了：

✅ **真实的API集成** - 直接调用后端认证接口  
✅ **工程化的环境管理** - 统一的配置和检测系统  
✅ **增强的错误处理** - 详细的错误信息和状态码  
✅ **更强的鲁棒性** - 完善的超时和重试机制  
✅ **智能的环境检测** - 自动识别和配置环境  
✅ **完善的调试支持** - 开发环境详细日志  

系统现在更加稳定、可维护，并且能够适应不同的部署环境！
