/**
 * 错误报告工具
 * 收集错误信息和用户环境数据，发送到技术团队webhook
 */

export interface ErrorReportData {
  // 错误信息
  error: {
    message: string
    stack?: string | undefined
    digest?: string | undefined
    timestamp: string
    userAgent: string
    url: string
  }
  // 用户环境
  environment: {
    browser: string
    browserVersion: string
    os: string
    screen: string
    language: string
    timezone: string
    cookieEnabled: boolean
    onlineStatus: boolean
  }
  // 应用状态
  application: {
    version: string
    buildId?: string | undefined
    nodeEnv: string
    userSession?: {
      hasAuth: boolean
      userId?: string
      lastActivity?: string
    } | undefined
  }
}

export enum ReportStatus {
  IDLE = 'idle',
  SENDING = 'sending',
  SUCCESS = 'success',
  FAILED = 'failed',
}

/**
 * 收集用户环境信息
 */
function collectEnvironmentInfo(): ErrorReportData['environment'] {
  // 检查是否在客户端环境
  if (typeof window === 'undefined') {
    return {
      browser: 'Unknown',
      browserVersion: 'Unknown',
      os: 'Unknown',
      screen: 'Unknown',
      language: 'Unknown',
      timezone: 'Unknown',
      cookieEnabled: false,
      onlineStatus: false,
    }
  }

  const ua = navigator.userAgent
  
  // 浏览器检测
  let browser = 'Unknown'
  let browserVersion = 'Unknown'
  
  if (ua.includes('Chrome')) {
    browser = 'Chrome'
    browserVersion = ua.match(/Chrome\/(\d+)/)?.[1] || 'Unknown'
  } else if (ua.includes('Firefox')) {
    browser = 'Firefox'
    browserVersion = ua.match(/Firefox\/(\d+)/)?.[1] || 'Unknown'
  } else if (ua.includes('Safari')) {
    browser = 'Safari'
    browserVersion = ua.match(/Version\/(\d+)/)?.[1] || 'Unknown'
  } else if (ua.includes('Edge')) {
    browser = 'Edge'
    browserVersion = ua.match(/Edge\/(\d+)/)?.[1] || 'Unknown'
  }
  
  // 操作系统检测
  let os = 'Unknown'
  if (ua.includes('Windows')) os = 'Windows'
  else if (ua.includes('Mac')) os = 'macOS'
  else if (ua.includes('Linux')) os = 'Linux'
  else if (ua.includes('Android')) os = 'Android'
  else if (ua.includes('iOS')) os = 'iOS'
  
  return {
    browser,
    browserVersion,
    os,
    screen: `${screen.width}x${screen.height}`,
    language: navigator.language,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    cookieEnabled: navigator.cookieEnabled,
    onlineStatus: navigator.onLine,
  }
}

/**
 * 收集应用状态信息
 */
function collectApplicationInfo(): ErrorReportData['application'] {
  // 尝试从localStorage获取用户会话信息
  let userSession: ErrorReportData['application']['userSession'] | undefined
  
  // 检查是否在客户端环境
  if (typeof window !== 'undefined') {
    try {
      const authData = localStorage.getItem('auth-storage')
      if (authData) {
        const parsed = JSON.parse(authData)
        userSession = {
          hasAuth: !!parsed.state?.isAuthenticated,
          userId: parsed.state?.user?.id,
          lastActivity: parsed.state?.lastActivity,
        }
      }
    } catch (error) {
      // 忽略localStorage错误
    }
  }
  
  return {
    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
    buildId: process.env.NEXT_PUBLIC_BUILD_ID || undefined,
    nodeEnv: process.env.NODE_ENV || 'development',
    userSession,
  }
}

/**
 * 发送错误报告到webhook
 */
export async function sendErrorReport(
  error: Error & { digest?: string },
  additionalContext?: Record<string, any>
): Promise<{ status: ReportStatus; message: string }> {
  try {
    // 检查是否在客户端环境
    if (typeof window === 'undefined') {
      return {
        status: ReportStatus.FAILED,
        message: '错误报告只能在客户端环境发送',
      }
    }

    const reportData: ErrorReportData = {
      error: {
        message: error.message,
        stack: error.stack || undefined,
        digest: error.digest || undefined,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
      },
      environment: collectEnvironmentInfo(),
      application: collectApplicationInfo(),
    }

    // 添加额外的上下文信息
    if (additionalContext) {
      (reportData as any).additionalContext = additionalContext
    }

    // Webhook URL配置
    const webhookUrl = process.env.NEXT_PUBLIC_ERROR_WEBHOOK_URL || 
                       'https://hooks.slack.com/workflows/your-webhook-url' // 默认webhook

    // 发送错误报告
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: `🚨 生产环境错误报告`,
        blocks: [
          {
            type: 'header',
            text: {
              type: 'plain_text',
              text: '🚨 应用错误报告',
            },
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*错误消息:*\n${reportData.error.message}`,
              },
              {
                type: 'mrkdwn',
                text: `*发生时间:*\n${reportData.error.timestamp}`,
              },
              {
                type: 'mrkdwn',
                text: `*用户环境:*\n${reportData.environment.browser} ${reportData.environment.browserVersion} | ${reportData.environment.os}`,
              },
              {
                type: 'mrkdwn',
                text: `*页面URL:*\n${reportData.error.url}`,
              },
            ],
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*错误堆栈:*\n\`\`\`${reportData.error.stack?.slice(0, 500) || 'No stack trace'}\`\`\``,
            },
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*完整报告数据:*\n\`\`\`${JSON.stringify(reportData, null, 2).slice(0, 1000)}\`\`\``,
            },
          },
        ],
      }),
    })

    if (response.ok) {
      return {
        status: ReportStatus.SUCCESS,
        message: '错误报告已成功发送给技术团队，我们会尽快处理！',
      }
    } else {
      throw new Error(`HTTP ${response.status}`)
    }
  } catch (error) {
    console.error('发送错误报告失败:', error)
    return {
      status: ReportStatus.FAILED,
      message: '发送错误报告失败，请稍后重试或直接联系技术支持。',
    }
  }
}

/**
 * 格式化错误信息供显示
 */
export function formatErrorForDisplay(error: Error & { digest?: string }): string {
  const lines = [
    `错误消息: ${error.message}`,
    `错误ID: ${error.digest || 'N/A'}`,
    `发生时间: ${new Date().toLocaleString('zh-CN')}`,
    `页面URL: ${window.location.href}`,
    `用户代理: ${navigator.userAgent}`,
  ]
  
  if (error.stack) {
    lines.push(`错误堆栈: ${error.stack.slice(0, 200)}...`)
  }
  
  return lines.join('\n')
} 