/**
 * VerticalPipelineDashboard i18n 测试用例
 * 测试组件在不同语言环境下的文案显示
 */

import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { NextIntlClientProvider } from 'next-intl'
import { VerticalPipelineDashboard } from '@/components/vertical-pipeline-dashboard'

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useSearchParams: () => ({
    get: (key: string) => {
      if (key === 'title') return '政府采购项目'
      return null
    }
  })
}))

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    span: ({ children, ...props }: any) => <span {...props}>{children}</span>
  },
  AnimatePresence: ({ children }: any) => <>{children}</>
}))

// 测试用的翻译消息
const zhMessages = {
  embed: {
    pipeline: {
      status: {
        preparing: '准备启动',
        completed: '流水线执行完成',
        running: '正在为{userName}生成[{title}]的详细背景信息，当前正在执行{currentStep}，请耐心等待或检查操作',
        defaultUser: '用户',
        defaultTitle: '招投标项目'
      }
    }
  }
}

const enMessages = {
  embed: {
    pipeline: {
      status: {
        preparing: 'Ready to start',
        completed: 'Pipeline execution completed',
        running: 'Generating detailed background information for {userName} on [{title}], currently executing {currentStep}, please wait patiently or check the operation',
        defaultUser: 'User',
        defaultTitle: 'Bidding Project'
      }
    }
  }
}

const jaMessages = {
  embed: {
    pipeline: {
      status: {
        preparing: '開始準備中',
        completed: 'パイプライン実行完了',
        running: '{userName}様の[{title}]の詳細な背景情報を生成中です。現在{currentStep}を実行中です。しばらくお待ちいただくか、操作をご確認ください',
        defaultUser: 'ユーザー',
        defaultTitle: '入札プロジェクト'
      }
    }
  }
}

// 测试用的用户数据
const mockUser = {
  user_name: '张三',
  email: '<EMAIL>'
}

const mockUserWithoutName = {
  email: '<EMAIL>'
}

// 渲染组件的辅助函数
const renderWithIntl = (locale: string, messages: any, user?: any) => {
  return render(
    <NextIntlClientProvider locale={locale} messages={messages}>
      <VerticalPipelineDashboard user={user} />
    </NextIntlClientProvider>
  )
}

describe('VerticalPipelineDashboard i18n Tests', () => {
  beforeEach(() => {
    // 清理所有定时器
    jest.clearAllTimers()
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.runOnlyPendingTimers()
    jest.useRealTimers()
  })

  describe('中文 (zh) 语言测试', () => {
    test('应该显示中文的准备启动状态', () => {
      renderWithIntl('zh', zhMessages, mockUser)
      
      expect(screen.getByText('准备启动')).toBeInTheDocument()
    })

    test('应该显示中文的用户名和项目标题', async () => {
      renderWithIntl('zh', zhMessages, mockUser)
      
      // 等待组件开始流水线
      jest.advanceTimersByTime(2000)
      
      await waitFor(() => {
        const runningText = screen.getByText(/正在为张三生成\[政府采购项目\]的详细背景信息/)
        expect(runningText).toBeInTheDocument()
      })
    })

    test('用户信息缺失时应该显示默认用户名', async () => {
      renderWithIntl('zh', zhMessages)
      
      jest.advanceTimersByTime(2000)
      
      await waitFor(() => {
        const runningText = screen.getByText(/正在为用户生成/)
        expect(runningText).toBeInTheDocument()
      })
    })

    test('只有email时应该提取用户名', async () => {
      renderWithIntl('zh', zhMessages, mockUserWithoutName)
      
      jest.advanceTimersByTime(2000)
      
      await waitFor(() => {
        const runningText = screen.getByText(/正在为test生成/)
        expect(runningText).toBeInTheDocument()
      })
    })
  })

  describe('英文 (en) 语言测试', () => {
    test('应该显示英文的准备启动状态', () => {
      renderWithIntl('en', enMessages, mockUser)
      
      expect(screen.getByText('Ready to start')).toBeInTheDocument()
    })

    test('应该显示英文的运行状态信息', async () => {
      renderWithIntl('en', enMessages, mockUser)
      
      jest.advanceTimersByTime(2000)
      
      await waitFor(() => {
        const runningText = screen.getByText(/Generating detailed background information for 张三 on \[政府采购项目\]/)
        expect(runningText).toBeInTheDocument()
      })
    })

    test('用户信息缺失时应该显示英文默认用户名', async () => {
      renderWithIntl('en', enMessages)
      
      jest.advanceTimersByTime(2000)
      
      await waitFor(() => {
        const runningText = screen.getByText(/Generating detailed background information for User/)
        expect(runningText).toBeInTheDocument()
      })
    })
  })

  describe('日文 (ja) 语言测试', () => {
    test('应该显示日文的准备启动状态', () => {
      renderWithIntl('ja', jaMessages, mockUser)
      
      expect(screen.getByText('開始準備中')).toBeInTheDocument()
    })

    test('应该显示日文的运行状态信息', async () => {
      renderWithIntl('ja', jaMessages, mockUser)
      
      jest.advanceTimersByTime(2000)
      
      await waitFor(() => {
        const runningText = screen.getByText(/張三様の\[政府采购项目\]の詳細な背景情報を生成中です/)
        expect(runningText).toBeInTheDocument()
      })
    })

    test('用户信息缺失时应该显示日文默认用户名', async () => {
      renderWithIntl('ja', jaMessages)
      
      jest.advanceTimersByTime(2000)
      
      await waitFor(() => {
        const runningText = screen.getByText(/ユーザー様の/)
        expect(runningText).toBeInTheDocument()
      })
    })
  })

  describe('URL参数测试', () => {
    test('应该使用URL参数中的title', async () => {
      // Mock不同的URL参数
      jest.doMock('next/navigation', () => ({
        useSearchParams: () => ({
          get: (key: string) => {
            if (key === 'title') return '特殊项目名称'
            return null
          }
        })
      }))

      renderWithIntl('zh', zhMessages, mockUser)
      
      jest.advanceTimersByTime(2000)
      
      await waitFor(() => {
        const runningText = screen.getByText(/正在为张三生成\[特殊项目名称\]的详细背景信息/)
        expect(runningText).toBeInTheDocument()
      })
    })

    test('URL参数缺失时应该使用默认title', async () => {
      // Mock没有title参数的情况
      jest.doMock('next/navigation', () => ({
        useSearchParams: () => ({
          get: () => null
        })
      }))

      renderWithIntl('zh', zhMessages, mockUser)
      
      jest.advanceTimersByTime(2000)
      
      await waitFor(() => {
        const runningText = screen.getByText(/正在为张三生成\[招投标项目\]的详细背景信息/)
        expect(runningText).toBeInTheDocument()
      })
    })
  })

  describe('流水线状态变化测试', () => {
    test('应该正确显示流水线完成状态', async () => {
      renderWithIntl('zh', zhMessages, mockUser)
      
      // 等待流水线开始
      jest.advanceTimersByTime(2000)
      
      // 等待流水线完成
      jest.advanceTimersByTime(30000)
      
      await waitFor(() => {
        expect(screen.getByText('流水线执行完成')).toBeInTheDocument()
      })
    })

    test('应该在不同步骤中显示正确的当前步骤', async () => {
      renderWithIntl('zh', zhMessages, mockUser)
      
      jest.advanceTimersByTime(2000)
      
      // 检查第一个步骤
      await waitFor(() => {
        const runningText = screen.getByText(/当前正在执行获取招投标数据/)
        expect(runningText).toBeInTheDocument()
      })
      
      // 等待下一个步骤
      jest.advanceTimersByTime(5000)
      
      await waitFor(() => {
        const runningText = screen.getByText(/当前正在执行生成招标摘要/)
        expect(runningText).toBeInTheDocument()
      })
    })
  })
})
