/**
 * 全局Fetch拦截器测试
 */

import { 
  installFetchInterceptor, 
  uninstallFetchInterceptor, 
  isInterceptorActive,
  getInterceptorStatus,
  manualIntercept
} from '../fetch-interceptor'

// Mock window.location
const mockLocation = {
  pathname: '/en/dashboard',
  href: 'http://localhost:3000/en/dashboard',
  origin: 'http://localhost:3000'
}

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
}

// Mock tokenManager
const mockTokenManager = {
  getToken: jest.fn(() => 'mock-jwt-token'),
  setToken: jest.fn(),
  removeTokens: jest.fn()
}

// Mock fetch
const mockFetch = jest.fn()

// Setup mocks
Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true
})

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
  writable: true
})

Object.defineProperty(globalThis, 'fetch', {
  value: mockFetch,
  writable: true,
  configurable: true
})

// Mock the token module
jest.mock('@/lib/token', () => ({
  tokenManager: mockTokenManager
}))

describe('全局Fetch拦截器测试', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue(null)
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true, data: {} })
    })
    
    // 确保拦截器未安装
    uninstallFetchInterceptor()
  })

  afterEach(() => {
    // 清理拦截器
    uninstallFetchInterceptor()
  })

  describe('拦截器安装和卸载', () => {
    it('应该能够安装拦截器', () => {
      expect(isInterceptorActive()).toBe(false)
      
      installFetchInterceptor()
      
      expect(isInterceptorActive()).toBe(true)
    })

    it('应该能够卸载拦截器', () => {
      installFetchInterceptor()
      expect(isInterceptorActive()).toBe(true)
      
      uninstallFetchInterceptor()
      
      expect(isInterceptorActive()).toBe(false)
    })

    it('应该防止重复安装', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
      
      installFetchInterceptor()
      installFetchInterceptor() // 重复安装
      
      expect(consoleSpy).toHaveBeenCalledWith('Fetch拦截器已经安装，跳过重复安装')
      consoleSpy.mockRestore()
    })
  })

  describe('API请求拦截', () => {
    beforeEach(() => {
      installFetchInterceptor()
      mockLocation.pathname = '/en/dashboard'
    })

    it('应该拦截API请求并添加语言头', async () => {
      await fetch('/api/test')
      
      expect(mockFetch).toHaveBeenCalledWith('/api/test', {
        headers: {
          'Content-Type': 'application/json',
          'language': 'english',
          'authorization': 'mock-jwt-token'
        }
      })
    })

    it('应该拦截绝对URL的API请求', async () => {
      await fetch('http://localhost:3000/api/test')
      
      expect(mockFetch).toHaveBeenCalledWith('http://localhost:3000/api/test', {
        headers: {
          'Content-Type': 'application/json',
          'language': 'english',
          'authorization': 'mock-jwt-token'
        }
      })
    })

    it('应该跳过非API请求', async () => {
      await fetch('/static/image.png')
      
      expect(mockFetch).toHaveBeenCalledWith('/static/image.png', undefined)
    })

    it('应该处理skipAuth标记', async () => {
      await fetch('/api/login', {
        method: 'POST',
        headers: {
          'x-skip-auth': 'true'
        }
      })
      
      expect(mockFetch).toHaveBeenCalledWith('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'language': 'english'
          // 注意：没有authorization头
        }
      })
    })

    it('应该保留现有的请求头', async () => {
      await fetch('/api/test', {
        headers: {
          'Custom-Header': 'custom-value'
        }
      })
      
      expect(mockFetch).toHaveBeenCalledWith('/api/test', {
        headers: {
          'Content-Type': 'application/json',
          'Custom-Header': 'custom-value',
          'language': 'english',
          'authorization': 'mock-jwt-token'
        }
      })
    })
  })

  describe('语言检测', () => {
    beforeEach(() => {
      installFetchInterceptor()
    })

    it('应该从URL路径检测中文', async () => {
      mockLocation.pathname = '/zh/dashboard'
      
      await fetch('/api/test')
      
      expect(mockFetch).toHaveBeenCalledWith('/api/test', 
        expect.objectContaining({
          headers: expect.objectContaining({
            'language': 'chinese'
          })
        })
      )
    })

    it('应该从URL路径检测日文', async () => {
      mockLocation.pathname = '/ja/dashboard'
      
      await fetch('/api/test')
      
      expect(mockFetch).toHaveBeenCalledWith('/api/test', 
        expect.objectContaining({
          headers: expect.objectContaining({
            'language': 'japanese'
          })
        })
      )
    })

    it('应该从localStorage获取语言（当URL无语言前缀时）', async () => {
      mockLocation.pathname = '/dashboard'
      mockLocalStorage.getItem.mockReturnValue('english')
      
      await fetch('/api/test')
      
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('user-language')
      expect(mockFetch).toHaveBeenCalledWith('/api/test', 
        expect.objectContaining({
          headers: expect.objectContaining({
            'language': 'english'
          })
        })
      )
    })

    it('应该使用默认中文（当无任何语言设置时）', async () => {
      mockLocation.pathname = '/dashboard'
      mockLocalStorage.getItem.mockReturnValue(null)
      
      await fetch('/api/test')
      
      expect(mockFetch).toHaveBeenCalledWith('/api/test', 
        expect.objectContaining({
          headers: expect.objectContaining({
            'language': 'chinese'
          })
        })
      )
    })
  })

  describe('手动拦截功能', () => {
    it('应该能够手动应用拦截器', () => {
      mockLocation.pathname = '/en/dashboard'
      
      const result = manualIntercept('/api/test', {
        method: 'POST'
      })
      
      expect(result).toEqual({
        url: '/api/test',
        options: {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'language': 'english',
            'authorization': 'mock-jwt-token'
          }
        }
      })
    })
  })

  describe('状态检查', () => {
    it('应该返回正确的拦截器状态', () => {
      mockLocation.pathname = '/en/dashboard'
      
      const status = getInterceptorStatus()
      
      expect(status).toEqual({
        installed: false, // 因为在beforeEach中卸载了
        currentLanguage: 'english',
        hasToken: true,
        originalFetchExists: true
      })
    })
  })
})
