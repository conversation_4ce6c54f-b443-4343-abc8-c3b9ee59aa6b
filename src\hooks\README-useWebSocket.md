# useWebSocket Hook 使用文档

## 概述

`useWebSocket` 是一个基于 **Socket.IO** 的功能完整的 React Hook，提供了企业级的实时通信连接管理功能，包括：

- 🔄 **自动重连机制** - 网络断开时自动尝试重连，支持多种传输方式回退
- 💓 **内置心跳检测** - Socket.IO 内置心跳机制，可选自定义心跳
- 📊 **状态管理** - 完整的连接状态跟踪
- 🔧 **错误处理** - 完善的错误捕获和处理
- 🎛️ **灵活配置** - 支持命名空间、传输方式等多种配置选项
- 🚀 **传输回退** - 自动从 WebSocket 回退到 HTTP long-polling

## 基本用法

```typescript
import { useWebSocket, WebSocketStatus } from '@/hooks/use-websocket'

function MyComponent() {
  const {
    status,
    lastMessage,
    error,
    connect,
    disconnect,
    send,
    isConnected,
    reconnectAttempts
  } = useWebSocket(
    {
      url: 'http://localhost:8080', // Socket.IO 服务器地址
      namespace: '/api/v1', // 可选命名空间
      autoConnect: true,
      debug: true,
    },
    {
      onConnect: () => console.log('连接成功'),
      onDisconnect: (reason) => console.log('连接断开:', reason),
      onMessage: (event, data) => console.log('收到消息:', event, data),
      onError: (error) => console.error('连接错误:', error),
    }
  )

  return (
    <div>
      <p>连接状态: {status}</p>
      <p>重连次数: {reconnectAttempts}</p>
      {error && <p>错误: {error.message}</p>}
      
      <button onClick={connect} disabled={isConnected}>
        连接
      </button>
      <button onClick={disconnect} disabled={!isConnected}>
        断开
      </button>
      <button 
        onClick={() => send('test_message', { data: 'hello' })}
        disabled={!isConnected}
      >
        发送消息
      </button>
    </div>
  )
}
```

## 配置选项

### WebSocketConfig

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `url` | `string` | **必需** | WebSocket 服务器地址 |
| `protocols` | `string \| string[]` | `[]` | WebSocket 子协议 |
| `heartbeatInterval` | `number` | `30000` | 心跳间隔（毫秒） |
| `heartbeatMessage` | `string` | `{"type":"ping"}` | 心跳消息内容 |
| `reconnectAttempts` | `number` | `5` | 最大重连次数 |
| `reconnectInterval` | `number` | `3000` | 重连间隔（毫秒） |
| `timeout` | `number` | `10000` | 连接超时时间（毫秒） |
| `autoConnect` | `boolean` | `true` | 是否自动连接 |
| `debug` | `boolean` | `false` | 是否开启调试模式 |

### WebSocketCallbacks

| 回调函数 | 参数 | 说明 |
|----------|------|------|
| `onConnect` | `(event: Event) => void` | 连接成功时触发 |
| `onDisconnect` | `(event: CloseEvent) => void` | 连接断开时触发 |
| `onError` | `(error: Error) => void` | 发生错误时触发 |
| `onMessage` | `(message: WebSocketMessage) => void` | 收到消息时触发 |
| `onReconnect` | `(attempt: number) => void` | 重连成功时触发 |
| `onReconnectFailed` | `() => void` | 重连失败时触发 |

## 返回值

### UseWebSocketReturn

| 属性 | 类型 | 说明 |
|------|------|------|
| `socket` | `WebSocket \| null` | 原生 WebSocket 实例 |
| `status` | `WebSocketStatus` | 当前连接状态 |
| `lastMessage` | `WebSocketMessage \| null` | 最后收到的消息 |
| `error` | `Error \| null` | 最后发生的错误 |
| `connect` | `() => void` | 手动连接函数 |
| `disconnect` | `() => void` | 手动断开函数 |
| `send` | `(data: any) => void` | 发送消息函数 |
| `isConnected` | `boolean` | 是否已连接 |
| `reconnectAttempts` | `number` | 当前重连次数 |

## 连接状态

```typescript
enum WebSocketStatus {
  DISCONNECTED = 'disconnected',  // 已断开
  CONNECTING = 'connecting',      // 连接中
  CONNECTED = 'connected',        // 已连接
  RECONNECTING = 'reconnecting',  // 重连中
  ERROR = 'error',               // 错误状态
}
```

## 消息格式

```typescript
interface WebSocketMessage {
  type: string        // 消息类型
  data?: any         // 消息数据
  timestamp: number  // 时间戳
}
```

## 高级用法

### 手动控制连接

```typescript
const { connect, disconnect, isConnected } = useWebSocket(
  {
    url: 'ws://localhost:8080',
    autoConnect: false, // 禁用自动连接
  }
)

// 在需要时手动连接
useEffect(() => {
  if (someCondition) {
    connect()
  }
}, [someCondition, connect])
```

### 发送特定格式消息

```typescript
const { send, isConnected } = useWebSocket(config, callbacks)

const sendPipelineCommand = useCallback((command: string, data: any) => {
  if (isConnected) {
    send({
      type: 'pipeline_command',
      command,
      data,
      timestamp: Date.now()
    })
  }
}, [send, isConnected])
```

### 处理不同类型的消息

```typescript
const callbacks = {
  onMessage: (message: WebSocketMessage) => {
    switch (message.type) {
      case 'step_created':
        handleStepCreated(message.data)
        break
      case 'tool_status_updated':
        handleToolStatusUpdate(message.data)
        break
      case 'error':
        handleError(message.data)
        break
      default:
        console.log('未知消息类型:', message.type)
    }
  }
}
```

## 最佳实践

1. **使用调试模式** - 开发时开启 `debug: true` 以查看详细日志
2. **合理设置重连参数** - 根据网络环境调整重连次数和间隔
3. **处理错误回调** - 始终实现 `onError` 回调处理异常情况
4. **消息格式统一** - 使用一致的消息格式便于处理
5. **清理副作用** - Hook 会自动清理连接，无需手动处理

## 故障排除

### 连接失败
- 检查 WebSocket 服务器是否运行
- 确认 URL 格式正确
- 检查网络防火墙设置

### 频繁重连
- 检查服务器稳定性
- 调整心跳间隔
- 检查网络连接质量

### 消息丢失
- 确认连接状态后再发送
- 实现消息队列机制
- 添加消息确认机制 