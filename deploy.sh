#!/bin/bash

set -e

# 解析命令行参数
ENVIRONMENT="dev"  # 默认为开发环境
PRIVATE_REGISTRY="43.153.104.17:5000"
IMAGE_NAME="nextjs-web-ui"
TAG="latest"

if [ "$1" == "--pd" ] || [ "$1" == "-pd" ]; then
    ENVIRONMENT="pd"
    echo "部署生产环境配置"
elif [ "$1" == "--dev" ] || [ "$1" == "-dev" ]; then
    ENVIRONMENT="dev"
    echo "部署开发环境配置"
fi

# 完整镜像名
FULL_IMAGE_NAME="${PRIVATE_REGISTRY}/${IMAGE_NAME}:${TAG}"

echo "--------------------------------------------------"
echo "部署 ${IMAGE_NAME} 服务"
echo "环境: ${ENVIRONMENT}"
echo "镜像: ${FULL_IMAGE_NAME}"
echo "--------------------------------------------------"

# 停止并删除现有容器（如果存在）
CONTAINER_NAME="${IMAGE_NAME}-${ENVIRONMENT}"
if docker ps -a --format "{{.Names}}" | grep -E "^${CONTAINER_NAME}$" >/dev/null; then
    echo "停止现有容器: ${CONTAINER_NAME}"
    docker stop "${CONTAINER_NAME}" || true
    echo "删除现有容器: ${CONTAINER_NAME}"
    docker rm "${CONTAINER_NAME}" || true
fi

# 拉取最新镜像
echo "拉取最新镜像: ${FULL_IMAGE_NAME}"
docker pull "${FULL_IMAGE_NAME}"

# 根据环境设置环境变量和端口
echo "配置环境变量: ${ENVIRONMENT}"
if [ "${ENVIRONMENT}" == "dev" ]; then
    # 开发环境配置
    echo "使用开发环境配置"
    ENV_VARS=(
        "-e NODE_ENV=production"
        "-e NEXT_PUBLIC_APP_ENV=dev"
        "-e NEXT_PUBLIC_BASE_URL=https://dev.goglobalsp.com"
        "-e NEXT_PUBLIC_EMBED_ENABLED=true"
        "-e NEXT_PUBLIC_DEBUG=true"
        "-e ALLOWED_EMBED_DOMAINS=https://www.goglobalsp.com,https://dev.goglobalsp.com"
        "-e EMBED_SECURITY_ENABLED=true"
        "-e INTERNAL_API_BASE=http://localhost:8136"
        "-e INTERNAL_SOCKET_URL=http://localhost:8137"
    )
    PORT="8230"
elif [ "${ENVIRONMENT}" == "pd" ]; then
    # 生产环境配置
    echo "使用生产环境配置"
    ENV_VARS=(
        "-e NODE_ENV=production"
        "-e NEXT_PUBLIC_APP_ENV=production"
        "-e NEXT_PUBLIC_BASE_URL=https://dev.goglobalsp.com"
        "-e NEXT_PUBLIC_EMBED_ENABLED=true"
        "-e NEXT_PUBLIC_DEBUG=false"
        "-e ALLOWED_EMBED_DOMAINS=https://www.goglobalsp.com,https://dev.goglobalsp.com"
        "-e EMBED_SECURITY_ENABLED=true"
        "-e INTERNAL_API_BASE=http://localhost:8136"
        "-e INTERNAL_SOCKET_URL=http://localhost:8137"
    )
    PORT="8230"
else
    echo "❌ 无效的环境: ${ENVIRONMENT}"
    exit 1
fi

# 运行容器，传递环境变量
echo "启动容器: ${CONTAINER_NAME}"
docker run -d \
    --name "${CONTAINER_NAME}" \
    --restart unless-stopped \
    -p "127.0.0.1:${PORT}:8230" \
    ${ENV_VARS[@]} \
    -e DEPLOY_ENV="${ENVIRONMENT}" \
    -e DOCKER_ENV="${ENVIRONMENT}" \
    "${FULL_IMAGE_NAME}"

# 等待服务启动
echo "等待服务启动..."
sleep 15

# 检查容器状态
if docker ps --format "{{.Names}}" | grep -E "^${CONTAINER_NAME}$" >/dev/null; then
    echo "✅ 容器启动成功: ${CONTAINER_NAME}"
    
    # 显示容器信息
    echo ""
    echo "容器信息:"
    docker ps --filter "name=${CONTAINER_NAME}" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    echo "📋 服务访问信息:"
    echo "  本地访问: http://localhost:${PORT}"
    echo "  embed页面: http://localhost:${PORT}/embed/dashboard"
    
else
    echo "❌ 容器启动失败: ${CONTAINER_NAME}"
    echo "查看容器日志:"
    docker logs "${CONTAINER_NAME}" || true
    exit 1
fi

echo "--------------------------------------------------" 