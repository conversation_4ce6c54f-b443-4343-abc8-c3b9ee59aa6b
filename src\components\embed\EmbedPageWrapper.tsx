/**
 * Embed Page Wrapper
 * 嵌入页面的包装组件，为iframe环境提供统一的认证和容器逻辑
 */

'use client'

import React from 'react'
import IframeContainer from './IframeContainer'

interface EmbedPageWrapperProps {
  children: React.ReactNode
  enableAuth?: boolean
  showLoading?: boolean
}

export default function EmbedPageWrapper({
  children,
  enableAuth = true,
  showLoading = true
}: EmbedPageWrapperProps) {
  return (
    <IframeContainer
      className='flex flex-col overflow-hidden'
      enableAuth={enableAuth}
      showLoading={showLoading}
    >
      {children}
    </IframeContainer>
  )
}
