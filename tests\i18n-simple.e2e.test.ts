/**
 * 简化的 i18n 功能测试
 * 测试基本的国际化功能是否正常工作
 */

import { test, expect } from '@playwright/test'

test.describe('i18n Simple Tests', () => {
  test('应该正确显示中文翻译', async ({ page }) => {
    await page.goto('/test-i18n-simple')
    
    // 等待页面加载
    await page.waitForLoadState('networkidle')
    
    // 检查页面标题
    await expect(page.locator('h1')).toContainText('i18n 简单测试')
    
    // 检查基本翻译
    await expect(page.locator('text=preparing:')).toBeVisible()
    await expect(page.locator('text=准备启动')).toBeVisible()
    
    await expect(page.locator('text=completed:')).toBeVisible()
    await expect(page.locator('text=流水线执行完成')).toBeVisible()
    
    await expect(page.locator('text=defaultUser:')).toBeVisible()
    await expect(page.locator('text=用户')).toBeVisible()
    
    await expect(page.locator('text=defaultTitle:')).toBeVisible()
    await expect(page.locator('text=招投标项目')).toBeVisible()
    
    // 检查参数化翻译
    await expect(page.locator('text=running (with parameters):')).toBeVisible()
    await expect(page.locator('text=正在为张三生成[政府采购项目]的详细背景信息，当前正在执行获取招投标数据，请耐心等待或检查操作')).toBeVisible()
  })

  test('参数化翻译应该正确替换变量', async ({ page }) => {
    await page.goto('/test-i18n-simple')
    
    // 等待页面加载
    await page.waitForLoadState('networkidle')
    
    // 检查参数化翻译的各个部分
    const runningText = page.locator('text=/正在为.*生成.*的详细背景信息/')
    await expect(runningText).toBeVisible()
    
    // 验证用户名参数
    await expect(page.locator('text=/正在为张三生成/')).toBeVisible()
    
    // 验证标题参数
    await expect(page.locator('text=/\\[政府采购项目\\]/')).toBeVisible()
    
    // 验证步骤参数
    await expect(page.locator('text=/当前正在执行获取招投标数据/')).toBeVisible()
    
    // 验证完整的提示文案
    await expect(page.locator('text=/请耐心等待或检查操作/')).toBeVisible()
  })
})
