'use client'

import { NextIntlClientProvider } from 'next-intl'
import { useTranslations } from 'next-intl'

// 中文翻译消息
const zhMessages = {
  embed: {
    pipeline: {
      status: {
        preparing: '准备启动',
        completed: '流水线执行完成',
        running: '正在为{userName}生成[{title}]的详细背景信息，当前正在执行{currentStep}，请耐心等待或检查操作',
        defaultUser: '用户',
        defaultTitle: '招投标项目'
      }
    }
  }
}

function TestComponent() {
  const t = useTranslations('embed.pipeline.status')
  
  const userName = '张三'
  const title = '政府采购项目'
  const currentStep = '获取招投标数据'
  
  return (
    <div className="p-8 space-y-4">
      <h1 className="text-2xl font-bold">i18n 简单测试</h1>
      
      <div className="space-y-2">
        <h2 className="text-lg font-semibold">测试翻译文案：</h2>
        
        <div className="p-4 bg-gray-100 rounded">
          <p><strong>preparing:</strong> {t('preparing')}</p>
        </div>
        
        <div className="p-4 bg-gray-100 rounded">
          <p><strong>completed:</strong> {t('completed')}</p>
        </div>
        
        <div className="p-4 bg-gray-100 rounded">
          <p><strong>defaultUser:</strong> {t('defaultUser')}</p>
        </div>
        
        <div className="p-4 bg-gray-100 rounded">
          <p><strong>defaultTitle:</strong> {t('defaultTitle')}</p>
        </div>
        
        <div className="p-4 bg-blue-100 rounded">
          <p><strong>running (with parameters):</strong></p>
          <p>{t('running', {
            userName: userName,
            title: title,
            currentStep: currentStep
          })}</p>
        </div>
      </div>
    </div>
  )
}

export default function TestI18nSimplePage() {
  return (
    <NextIntlClientProvider locale="zh" messages={zhMessages}>
      <TestComponent />
    </NextIntlClientProvider>
  )
}
