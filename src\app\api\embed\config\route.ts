/**
 * 嵌入配置API
 * 返回嵌入系统的配置信息
 */

import { NextRequest, NextResponse } from 'next/server'
import { domainValidator } from '@/lib/embed/domain-validator'

// 配置路由段 - 静态缓存配置数据
export const dynamic = 'force-static'

export async function GET(request: NextRequest) {
  try {
    // 验证请求来源
    const referer = request.headers.get('referer')
    if (!domainValidator.isRefererAllowed(referer)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized domain' },
        { status: 403 }
      )
    }

    const config = {
      success: true,
      data: {
        allowedDomains: domainValidator.getAllowedDomains(),
        availablePages: [
          {
            path: '/zh/embed/dashboard',
            title: '仪表板',
            description: '数据仪表板页面',
            requiredPermissions: ['read']
          },
          {
            path: '/en/embed/dashboard',
            title: 'Dashboard',
            description: 'Data dashboard page',
            requiredPermissions: ['read']
          },
          {
            path: '/zh/embed/report',
            title: '报告',
            description: '报告查看页面',
            requiredPermissions: ['read']
          },
          {
            path: '/en/embed/report',
            title: 'Report',
            description: 'Report viewing page',
            requiredPermissions: ['read']
          }
        ],
        communication: {
          supportedMessages: [
            'RESIZE_REQUEST',
            'RESIZE_RESPONSE',
            'NAVIGATION_REQUEST',
            'PAGE_LOADED',
            'PING',
            'PONG'
          ]
        },
        security: {
          tokenRequired: true,
          domainValidation: true,
          secureHeaders: true
        }
      }
    }

    return NextResponse.json(config)
  } catch (error) {
    console.error('Config API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
