'use client'

import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, RefreshCw, Home, Bug, Send, CheckCircle, XCircle, Clock } from 'lucide-react'
import { sendErrorReport, ReportStatus } from '@/lib/error-reporter'
import { toast } from 'sonner'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string } | null
  reset: () => void
}) {
  const [reportStatus, setReportStatus] = useState<ReportStatus>(ReportStatus.IDLE)
  const [reportMessage, setReportMessage] = useState<string>('')

  useEffect(() => {
    // 记录错误到监控服务
    console.error('Application error:', error)
  }, [error])

  // 发送错误报告
  const handleSendErrorReport = async () => {
    if (reportStatus === ReportStatus.SENDING) return

    setReportStatus(ReportStatus.SENDING)
    setReportMessage('')
    
    // 显示发送中的提示
    toast.loading('正在发送错误报告...', { id: 'error-report' })

    try {
      if (!error) return
      const result = await sendErrorReport(error, {
        userAction: 'manual_report',
        pageContext: 'error_page',
        timestamp: new Date().toISOString(),
      })

      setReportStatus(result.status)
      setReportMessage(result.message)

      if (result.status === ReportStatus.SUCCESS) {
        toast.success(result.message, { id: 'error-report' })
      } else {
        toast.error(result.message, { id: 'error-report' })
      }
    } catch (error) {
      setReportStatus(ReportStatus.FAILED)
      setReportMessage('发送错误报告时出现异常，请稍后重试。')
      toast.error('发送失败，请稍后重试', { id: 'error-report' })
    }
  }

  // 获取报告状态图标
  const getReportStatusIcon = () => {
    switch (reportStatus) {
      case ReportStatus.SENDING:
        return <Clock className="mr-2 h-4 w-4 animate-spin" />
      case ReportStatus.SUCCESS:
        return <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
      case ReportStatus.FAILED:
        return <XCircle className="mr-2 h-4 w-4 text-red-600" />
      default:
        return <Send className="mr-2 h-4 w-4" />
    }
  }

  // 获取报告按钮文本
  const getReportButtonText = () => {
    switch (reportStatus) {
      case ReportStatus.SENDING:
        return '发送中...'
      case ReportStatus.SUCCESS:
        return '报告已发送'
      case ReportStatus.FAILED:
        return '重新发送'
      default:
        return '发送错误报告'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full text-center">
        {/* 错误图标和标题 */}
        <div className="mb-8">
          <div className="mx-auto w-24 h-24 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-6">
            <AlertTriangle className="h-12 w-12 text-red-600 dark:text-red-400" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            出现了一些问题
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            应用程序遇到了意外错误，我们正在努力解决
          </p>
        </div>

        {/* 错误详情卡片 */}
        <Card className="mb-8 text-left">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bug className="h-5 w-5" />
              <span>错误详情</span>
            </CardTitle>
            <CardDescription>
              以下信息可能有助于技术支持团队诊断问题
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error && <div className="space-y-3">
              <div>
                <span className="font-medium text-gray-600 dark:text-gray-300">错误消息:</span>
                <p className="text-sm text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-800 p-2 rounded mt-1 font-mono">
                  {error.message || '未知错误'}
                </p>
              </div>
              
              {error.digest && (
                <div>
                  <span className="font-medium text-gray-600 dark:text-gray-300">错误ID:</span>
                  <p className="text-sm text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-800 p-2 rounded mt-1 font-mono">
                    {error.digest}
                  </p>
                </div>
              )}
              
              <div>
                <span className="font-medium text-gray-600 dark:text-gray-300">发生时间:</span>
                <p className="text-sm text-gray-900 dark:text-white">
                  {new Date().toLocaleString('zh-CN')}
                </p>
              </div>
            </div>
            }
          </CardContent>
        </Card>

        {/* 操作按钮 */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
          <Button onClick={reset} size="lg">
            <RefreshCw className="mr-2 h-4 w-4" />
            重试
          </Button>
          
          <Button variant="outline" size="lg" onClick={() => window.location.href = '/'}>
            <Home className="mr-2 h-4 w-4" />
            返回首页
          </Button>
          
          <Button 
            variant="outline" 
            size="lg" 
            onClick={() => window.location.reload()}
          >
            刷新页面
          </Button>
        </div>

        {/* 故障排除建议 */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>故障排除建议</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-left space-y-2 text-gray-600 dark:text-gray-300">
              <li className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                <span>刷新页面或稍后重试</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                <span>检查网络连接是否正常</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                <span>清除浏览器缓存和Cookie</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                <span>尝试使用其他浏览器访问</span>
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* 联系支持 */}
        <div className="p-6 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <h3 className="text-lg font-semibold text-yellow-900 dark:text-yellow-100 mb-2">
            问题仍然存在？
          </h3>
          <p className="text-yellow-700 dark:text-yellow-300 mb-4">
            如果错误持续出现，请联系我们的技术支持团队。请提供上述错误详情以便快速诊断。
          </p>
          <div className="flex flex-col sm:flex-row gap-2 justify-center">
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleSendErrorReport}
              disabled={reportStatus === ReportStatus.SENDING}
              className={reportStatus === ReportStatus.SUCCESS ? 'text-green-600 border-green-600' : ''}
            >
              {getReportStatusIcon()}
              {getReportButtonText()}
            </Button>
          </div>
          
          {/* 报告状态反馈 */}
          {reportMessage && (
            <div className={`mt-3 p-3 rounded-lg text-sm ${
              reportStatus === ReportStatus.SUCCESS 
                ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300' 
                : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
            }`}>
              {reportMessage}
            </div>
          )}
        </div>

        {/* 页脚信息 */}
        <div className="mt-8 text-sm text-gray-500 dark:text-gray-400">
          <p>错误代码: 500 | 服务器内部错误</p>
          <p className="mt-1">
            我们已自动记录此错误，技术团队将尽快处理
          </p>
        </div>
      </div>
    </div>
  )
}
