declare module 'markdown-it-abbr' {
    import { PluginSimple } from 'markdown-it'
    const abbr: PluginSimple
    export default abbr
  }
  
  declare module 'markdown-it-anchor' {
    import { PluginWithOptions } from 'markdown-it'
  
    export interface AnchorOptions {
      level?: number | number[]
      slugify?(str: string): string
      permalink?: boolean
      permalinkBefore?: boolean
      permalinkSymbol?: string
      permalinkClass?: string
      permalinkAttrs?: Record<string, string>
      callback?(token: any, anchorInfo: { slug: string; title: string }): void
    }
  
    const anchor: PluginWithOptions<AnchorOptions>
    export default anchor
  }
  
  declare module 'markdown-it-container' {
    import { PluginWithOptions } from 'markdown-it'
    const container: PluginWithOptions<any>
    export default container
  }
  
  declare module 'markdown-it-deflist' {
    import { PluginSimple } from 'markdown-it'
    const deflist: PluginSimple
    export default deflist
  }
  
  declare module 'markdown-it-emoji' {
    import { PluginWithOptions } from 'markdown-it'
  
    export interface EmojiOptions {
      defs?: Record<string, string>
      shortcuts?: Record<string, string | string[]>
      enabled?: string[]
    }
  
    export const emoji: PluginWithOptions<EmojiOptions>
    export const full: PluginWithOptions<EmojiOptions>
  }
  
  declare module 'markdown-it-footnote' {
    import { PluginSimple } from 'markdown-it'
    const footnote: PluginSimple
    export default footnote
  }
  
  declare module 'markdown-it-ins' {
    import { PluginSimple } from 'markdown-it'
    const ins: PluginSimple
    export default ins
  }
  
  declare module 'markdown-it-mark' {
    import { PluginSimple } from 'markdown-it'
    const mark: PluginSimple
    export default mark
  }
  
  declare module 'markdown-it-sub' {
    import { PluginSimple } from 'markdown-it'
    const sub: PluginSimple
    export default sub
  }
  
  declare module 'markdown-it-sup' {
    import { PluginSimple } from 'markdown-it'
    const sup: PluginSimple
    export default sup
  }
  
  declare module 'markdown-it-task-lists' {
    import { PluginWithOptions } from 'markdown-it'
    interface TaskListOptions {
      enabled?: boolean
      label?: boolean
      labelAfter?: boolean
    }
  
    const taskLists: PluginWithOptions<TaskListOptions>
    export default taskLists
  }
  