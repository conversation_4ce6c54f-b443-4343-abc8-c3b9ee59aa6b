/**
 * Iframe优化组件
 * 解决Next.js在iframe中的JavaScript加载和渲染问题
 * 🚨 移除自动重新加载机制，避免无限循环
 */

'use client'

import React, { useEffect, useState, useRef } from 'react'

interface IframeOptimizerProps {
  children: React.ReactNode
  fallbackContent?: React.ReactNode
}

export default function IframeOptimizer({ 
  children, 
  fallbackContent 
}: IframeOptimizerProps) {
  const [isReady, setIsReady] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const mountedRef = useRef(false)
  const timeoutRef = useRef<NodeJS.Timeout>()
  const errorCountRef = useRef(0)

  useEffect(() => {
    mountedRef.current = true

    // 检查是否在iframe中
    const isInIframe = window !== window.parent

    console.log('🔍 IframeOptimizer初始化:', {
      isInIframe,
      userAgent: navigator.userAgent,
      location: window.location.href,
      timestamp: new Date().toISOString()
    })

    // 🔧 更保守的超时设置，只在真正无法加载时才显示错误
    timeoutRef.current = setTimeout(() => {
      if (mountedRef.current && !isReady && errorCountRef.current === 0) {
        console.warn('⚠️ 组件加载超时，但不自动重新加载')
        // 不再自动设置错误状态，让组件继续尝试加载
        setIsReady(true) // 强制显示内容
      }
    }, 15000) // 增加到15秒

    // 检查DOM是否准备就绪
    const checkReady = () => {
      if (document.readyState === 'complete') {
        console.log('✅ DOM加载完成')
        if (mountedRef.current) {
          setIsReady(true)
        }
      } else {
        console.log('⏳ 等待DOM加载完成...')
        setTimeout(checkReady, 100)
      }
    }

    // 立即检查或等待DOM加载
    if (document.readyState === 'complete') {
      checkReady()
    } else {
      document.addEventListener('DOMContentLoaded', checkReady)
      window.addEventListener('load', checkReady)
    }

    // 🔧 更保守的错误处理，只处理严重错误
    const handleError = (event: ErrorEvent) => {
      errorCountRef.current++
      
      console.error('🚨 JavaScript错误:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error,
        count: errorCountRef.current
      })

      // 🎯 只在连续多个严重错误时才触发错误状态，避免误判
      const isCriticalError = (
        event.message.includes('Script error') ||
        event.message.includes('Network error') ||
        (event.filename?.includes('.js') && event.message.includes('SyntaxError'))
      )

      if (isCriticalError && errorCountRef.current >= 3) {
        console.error('🚨 检测到连续严重错误，显示错误界面但不自动重新加载')
        if (mountedRef.current) {
          setHasError(true)
          setErrorMessage('页面加载遇到问题')
        }
      }
    }

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.warn('⚠️ 未处理的Promise拒绝:', event.reason)
      // 不再将Promise rejection视为严重错误
    }

    // 🔧 更宽松的资源错误处理
    const handleResourceError = (event: Event) => {
      const target = event.target as HTMLElement
      if (target?.tagName === 'SCRIPT') {
        console.warn('⚠️ Script加载失败:', (target as HTMLScriptElement).src)
        // 不再立即设置错误状态，让组件有更多尝试机会
      }
    }

    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    document.addEventListener('error', handleResourceError, true)

    return () => {
      mountedRef.current = false
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
      document.removeEventListener('error', handleResourceError, true)
      document.removeEventListener('DOMContentLoaded', checkReady)
      window.removeEventListener('load', checkReady)
    }
  }, []) // 🎯 移除 isReady 依赖，避免重复执行

  // 通知父窗口状态
  useEffect(() => {
    if (window.parent && window.parent !== window) {
      if (hasError) {
        window.parent.postMessage({
          type: 'IFRAME_ERROR',
          data: {
            message: errorMessage,
            timestamp: new Date().toISOString(),
            url: window.location.href
          }
        }, '*')
      } else if (isReady) {
        window.parent.postMessage({
          type: 'IFRAME_READY',
          data: {
            timestamp: new Date().toISOString(),
            url: window.location.href
          }
        }, '*')
      }
    }
  }, [isReady, hasError, errorMessage])

  // 🎯 错误状态不再自动重新加载，只显示信息
  if (hasError) {
    return (
      <div style={{
        padding: '20px',
        textAlign: 'center',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        background: '#fff',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        {fallbackContent || (
          <div style={{
            background: '#fff3cd',
            color: '#856404',
            padding: '30px',
            borderRadius: '8px',
            border: '1px solid #ffeaa7',
            maxWidth: '500px'
          }}>
            <h3 style={{ margin: '0 0 15px 0', fontSize: '18px' }}>
              ⚠️ 加载出现问题
            </h3>
            <p style={{ margin: '0 0 15px 0', lineHeight: '1.5' }}>
              页面加载时遇到了一些问题，但这通常是暂时的。请稍等片刻或手动刷新页面。
            </p>
            <div style={{ 
              background: '#ffeaa7', 
              padding: '10px', 
              borderRadius: '4px',
              fontSize: '12px',
              fontFamily: 'monospace'
            }}>
              <strong>技术信息:</strong><br />
              URL: {window.location.href}<br />
              时间: {new Date().toLocaleString()}<br />
              错误: {errorMessage}
            </div>
            <div style={{ marginTop: '15px', fontSize: '14px', color: '#856404' }}>
              💡 提示：如果问题持续存在，请联系技术支持
            </div>
          </div>
        )}
      </div>
    )
  }

  // 🎯 加载状态更简洁，避免过度检查
  if (!isReady) {
    return (
      <div style={{
        padding: '20px',
        textAlign: 'center',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        background: '#fff',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          background: '#e7f3ff',
          color: '#0066cc',
          padding: '30px',
          borderRadius: '8px',
          border: '1px solid #b3d9ff'
        }}>
          <div style={{ fontSize: '18px', marginBottom: '10px' }}>🔧 正在加载...</div>
          <div style={{ color: '#0066cc' }}>系统正在初始化，请稍候</div>
        </div>
      </div>
    )
  }

  return (
    <React.Fragment>
      {children}
    </React.Fragment>
  )
}
