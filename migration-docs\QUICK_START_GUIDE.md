# Next.js iframe 嵌入系统快速启动指南

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
npm install

# 复制环境变量配置
cp .env.example .env.local

# 编辑环境变量（根据实际情况修改）
# 重点配置：
# - API_BASE_URL: 后端API地址
# - ALLOWED_EMBED_DOMAINS: 允许嵌入的域名
```

### 2. 启动开发服务器

```bash
# 启动开发服务器
npm run dev

# 服务器将在 http://localhost:3000 启动
```

### 3. 测试嵌入页面

访问以下URL测试嵌入页面（需要有效的JWT token）：

```
http://localhost:3001/embed/dashboard?token=your-jwt-token
http://localhost:3001/embed/reports?token=your-jwt-token
http://localhost:3001/embed/workspace?token=your-jwt-token
```

> 注意：如果端口3000被占用，Next.js会自动使用3001端口

### 4. 运行测试

```bash
# 测试嵌入系统
npm run test:embed

# 检查TypeScript类型
npm run type-check

# 检查代码质量
npm run lint
```

## 🔧 配置说明

### 关键环境变量

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `API_BASE_URL` | 后端API地址 | `http://localhost:8080` |
| `ALLOWED_EMBED_DOMAINS` | 允许嵌入的域名列表 | `https://www.goglobalsp.com,https://dev.goglobalsp.com` |
| `NEXT_PUBLIC_APP_URL` | Next.js应用的公开URL | `http://localhost:3000` |

### 嵌入页面路径

- `/embed/dashboard` - 仪表板页面
- `/embed/reports` - 报告页面
- `/embed/workspace` - 工作区页面

## 🔐 认证配置

### Token 传递方式

嵌入页面支持以下方式传递认证token：

1. **URL参数**（推荐用于嵌入）：
   ```
   /embed/dashboard?token=your-jwt-token
   ```

2. **Cookie**：
   ```
   auth-token=your-jwt-token
   ```

3. **Authorization头**：
   ```
   Authorization: Bearer your-jwt-token
   ```

### 域名白名单

系统只允许以下域名嵌入页面：
- `https://www.goglobalsp.com`
- `https://dev.goglobalsp.com`

开发环境下自动允许本地域名（localhost、127.0.0.1等）。

## 🌐 Vue3 集成示例

### 基础使用

```vue
<template>
  <div class="embed-container">
    <iframe
      :src="embedUrl"
      frameborder="0"
      width="100%"
      :height="iframeHeight"
      @load="onLoad"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()
const iframeHeight = ref('600px')

const embedUrl = computed(() => {
  const baseUrl = 'http://localhost:3000'
  const token = authStore.token
  return `${baseUrl}/embed/dashboard?token=${encodeURIComponent(token)}`
})

const onLoad = () => {
  console.log('嵌入页面加载完成')
}
</script>
```

### 高级集成（带通信）

```vue
<template>
  <div class="embed-container">
    <iframe
      ref="embedFrame"
      :src="embedUrl"
      frameborder="0"
      width="100%"
      :height="iframeHeight"
      @load="onLoad"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const embedFrame = ref(null)
const iframeHeight = ref('600px')

// 处理iframe消息
const handleMessage = (event) => {
  if (event.origin !== 'http://localhost:3000') return
  
  switch (event.data.type) {
    case 'RESIZE_RESPONSE':
      iframeHeight.value = event.data.data.height + 'px'
      break
    case 'PAGE_LOADED':
      console.log('页面加载完成:', event.data.data.title)
      break
  }
}

onMounted(() => {
  window.addEventListener('message', handleMessage)
})

onUnmounted(() => {
  window.removeEventListener('message', handleMessage)
})

const onLoad = () => {
  // 请求调整高度
  embedFrame.value.contentWindow.postMessage({
    type: 'RESIZE_REQUEST'
  }, 'http://localhost:3000')
}
</script>
```

## 🐳 Docker 部署

### 构建镜像

```bash
# 构建Docker镜像
docker build -t nextjs-embed-app .

# 运行容器
docker run -d \
  --name nextjs-embed \
  -p 3000:3000 \
  -e API_BASE_URL=https://your-api-server.com \
  -e ALLOWED_EMBED_DOMAINS=https://www.goglobalsp.com,https://dev.goglobalsp.com \
  nextjs-embed-app
```

### 使用 Docker Compose

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 🔍 故障排除

### 常见问题

1. **iframe显示403错误**
   - 检查域名是否在白名单中
   - 确认环境变量 `ALLOWED_EMBED_DOMAINS` 配置正确

2. **Token认证失败**
   - 验证token格式是否为有效的JWT
   - 检查API服务器连接状态
   - 确认token未过期

3. **页面无法通信**
   - 检查PostMessage事件监听
   - 验证域名匹配
   - 查看浏览器控制台错误

### 调试工具

在浏览器控制台中使用：

```javascript
// 检查嵌入页面状态
window.EmbedDebugger?.checkAllEmbeds()

// 测试通信
window.EmbedDebugger?.testCommunication()

// 获取页面信息
window.EmbedDebugger?.getEmbedInfo()
```

## 📚 更多文档

- [完整实现指南](./EMBED_IMPLEMENTATION_GUIDE.md)
- [Vue3集成指南](./VUE3_INTEGRATION_GUIDE.md)
- [Docker部署指南](./DOCKER_DEPLOYMENT_GUIDE.md)
- [故障排除指南](./TROUBLESHOOTING_GUIDE.md)

## 🆘 获取帮助

如果遇到问题：

1. 查看 [故障排除指南](./TROUBLESHOOTING_GUIDE.md)
2. 检查浏览器控制台错误信息
3. 运行健康检查：`curl http://localhost:3000/api/embed/health`
4. 查看服务器日志

---

*快速启动指南帮助您在几分钟内启动和运行嵌入系统。*
