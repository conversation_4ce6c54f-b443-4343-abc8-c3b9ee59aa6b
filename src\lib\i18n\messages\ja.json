{"common": {"loading": "読み込み中...", "error": "エラー", "success": "成功", "cancel": "キャンセル", "confirm": "確認", "save": "保存", "delete": "削除", "edit": "編集", "back": "戻る", "next": "次へ", "previous": "前へ", "submit": "送信", "close": "閉じる", "search": "検索", "filter": "フィルター", "refresh": "更新", "retry": "再試行"}, "auth": {"login": {"title": "おかえりなさい", "subtitle": "Vibe Codingアカウントにサインイン", "username": "ユーザー名またはメール", "usernamePlaceholder": "ユーザー名またはメールを入力", "password": "パスワード", "passwordPlaceholder": "パスワードを入力", "forgotPassword": "パスワードを忘れましたか？", "loginButton": "ログイン", "loggingIn": "ログイン中...", "orLoginWith": "または次でログイン", "loginWithApple": "Appleでログイン", "loginWithGoogle": "Googleでログイン", "loginWithFacebook": "Facebookでログイン", "noAccount": "アカウントをお持ちでないですか？", "registerNow": "今すぐ登録", "brandTitle": "Vibe Coding Refactory", "brandSubtitle": "エンタープライズAIアプリケーションフロントエンドアーキテクチャ", "feature1": "✨ Vue3からNext.jsへの完全移行", "feature2": "🚀 モダンコンポーネントシステム", "feature3": "🔐 完全な認証・権限管理", "feature4": "🎨 エンタープライズUIデザインシステム", "termsText": "続行することで、当社の", "termsOfService": "利用規約", "and": "および", "privacyPolicy": "プライバシーポリシー", "period": "に同意したものとみなされます。"}, "validation": {"usernameRequired": "ユーザー名またはメールを入力してください", "passwordRequired": "パスワードを入力してください"}, "messages": {"loginSuccess": "ログイン成功", "welcomeBack": "おかえりなさい、{username}さん！", "loginFailed": "ログイン失敗", "loginError": "ログイン中にエラーが発生しました", "featureInDevelopment": "機能開発中", "forgotPasswordInDevelopment": "パスワード忘れ機能は開発中です", "registerInDevelopment": "登録機能は開発中です"}, "legacy": {"login": "ログイン", "logout": "ログアウト", "pleaseLogin": "まずログインしてください", "invalidCredentials": "ユーザー名またはパスワードが正しくありません", "accountLocked": "アカウントがロックされています。管理者にお問い合わせください", "userDeleted": "このユーザーは削除されています。管理者にお問い合わせください"}}, "navigation": {"chat": "チャット", "uiGallery": "UIギャラリー", "home": "ホーム", "dashboard": "ダッシュボード", "settings": "設定"}, "chat": {"title": "チャットインターフェース", "subtitle": "近日公開予定...", "welcome": "SpecificAIへようこそ！", "description": "AIアシスタントがお手伝いする準備ができています。", "placeholder": "メッセージを入力...", "send": "送信", "clear": "チャットをクリア", "typing": "入力中..."}, "ui": {"gallery": {"title": "UIコンポーネントギャラリー", "description": "スタイル検証のためのすべてのUIコンポーネントのテストとデバッグ", "buttons": "ボタンコンポーネント", "inputs": "入力コンポーネント", "cards": "カードコンポーネント", "markdown": "Markdownレンダラーデモ"}}, "uiGallery": {"title": "UIコンポーネントギャラリー", "subtitle": "プロジェクトで利用可能なコンポーネントの展示", "radixComponents": "Radi<PERSON> UIコンポーネント", "customComponents": "カスタムコンポーネント", "componentAvailable": "コンポーネント利用可能"}, "errors": {"unauthorized": "認証されていません。再度ログインしてください", "noPermission": "アクセス権限が不足しています", "userNotLogin": "ユーザーがログインしていません。まずログインしてください", "userDeleted": "このユーザーは削除されています。管理者にお問い合わせください", "networkError": "ネットワーク接続に失敗しました。ネットワーク設定を確認してください", "serverError": "サーバー内部エラー", "notFound": "要求されたリソースが見つかりません", "badRequest": "リクエストパラメータが正しくありません", "forbidden": "権限が不足しています", "timeout": "リクエストタイムアウト", "unknown": "未知のエラー"}, "language": {"switch": "言語切り替え", "chinese": "中文", "english": "English", "japanese": "日本語", "current": "現在の言語"}, "user": {"profile": "プロフィール", "account": "アカウント", "preferences": "設定", "avatar": "アバター", "name": "名前", "email": "メール", "role": "役割", "lastLogin": "最終ログイン", "memberSince": "登録日"}, "embed": {"initializing": "初期化中...", "loading": "ページを読み込み中", "pipeline": {"status": {"preparing": "開始準備中", "completed": "パイプライン実行完了", "running": "{userName}様の[{title}]の詳細な背景情報を生成中です。現在{currentStep}を実行中です。しばらくお待ちいただくか、操作をご確認ください", "defaultUser": "ユーザー", "defaultTitle": "入札プロジェクト"}}}}