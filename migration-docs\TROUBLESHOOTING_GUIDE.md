# Next.js iframe 嵌入系统故障排除指南

## 常见问题与解决方案

### 1. 认证相关问题

#### 问题：iframe 显示 "Token required" 错误

**症状：**
- iframe 显示 401 错误
- 控制台显示 "Token required" 消息
- 页面无法正常加载

**可能原因：**
1. Vue3 项目中的 token 为空或未正确传递
2. token 格式不正确
3. token 已过期

**解决方案：**

```javascript
// 1. 检查 Vue3 项目中的 token 状态
console.log('Current token:', authStore.token)
console.log('Token length:', authStore.token?.length)

// 2. 验证 token 格式（JWT 应该有三个部分）
const tokenParts = authStore.token?.split('.')
console.log('Token parts:', tokenParts?.length) // 应该是 3

// 3. 检查 token 是否过期
try {
  const payload = JSON.parse(atob(tokenParts[1]))
  const isExpired = Date.now() >= payload.exp * 1000
  console.log('Token expired:', isExpired)
  console.log('Token expires at:', new Date(payload.exp * 1000))
} catch (error) {
  console.error('Invalid token format:', error)
}

// 4. 刷新 token
if (isExpired) {
  await authStore.refreshToken()
}
```

#### 问题：token 验证失败

**症状：**
- token 存在但验证失败
- 服务器返回 "Token validation failed"

**解决方案：**

```javascript
// 检查 API 服务器连接
const testApiConnection = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/user/get_current`, {
      headers: {
        'authorization': authStore.token,
        'language': 'chinese'
      }
    })
    console.log('API response status:', response.status)
    const data = await response.json()
    console.log('API response:', data)
  } catch (error) {
    console.error('API connection failed:', error)
  }
}
```

### 2. 域名验证问题

#### 问题：iframe 显示 "Forbidden" 错误

**症状：**
- iframe 返回 403 错误
- 控制台显示域名相关错误

**解决方案：**

```javascript
// 1. 检查当前域名是否在白名单中
const currentOrigin = window.location.origin
console.log('Current origin:', currentOrigin)

const allowedDomains = [
  'https://www.goglobalsp.com',
  'https://dev.goglobalsp.com'
]
console.log('Is domain allowed:', allowedDomains.includes(currentOrigin))

// 2. 检查环境变量配置
console.log('ALLOWED_EMBED_DOMAINS:', process.env.ALLOWED_EMBED_DOMAINS)

// 3. 临时测试（仅开发环境）
if (process.env.NODE_ENV === 'development') {
  // 在 Next.js 项目中临时添加当前域名到白名单
  // 修改 src/lib/embed/domain-validator.ts
}
```

### 3. 通信问题

#### 问题：iframe 高度不自适应

**症状：**
- iframe 高度固定，不随内容变化
- 出现滚动条

**解决方案：**

```vue
<template>
  <EmbedFrame
    page="dashboard"
    :auto-resize="true"
    min-height="400px"
    max-height="2000px"
    @resize="onResize"
  />
</template>

<script setup>
const onResize = (height) => {
  console.log('New height:', height)
  // 检查高度是否合理
  if (height < 100 || height > 5000) {
    console.warn('Unusual height detected:', height)
  }
}

// 手动触发高度调整
const manualResize = () => {
  embedRef.value?.sendMessage({
    type: 'RESIZE_REQUEST'
  })
}
</script>
```

#### 问题：PostMessage 通信失败

**症状：**
- 父子页面无法通信
- 消息发送失败

**解决方案：**

```javascript
// 1. 检查消息监听器
window.addEventListener('message', (event) => {
  console.log('Received message:', {
    origin: event.origin,
    data: event.data,
    source: event.source
  })
})

// 2. 验证消息发送
const testMessage = () => {
  const iframe = document.querySelector('iframe')
  if (iframe?.contentWindow) {
    iframe.contentWindow.postMessage({
      type: 'TEST_MESSAGE',
      timestamp: Date.now()
    }, 'https://your-nextjs-app.com')
    console.log('Test message sent')
  } else {
    console.error('iframe not found or not ready')
  }
}

// 3. 检查跨域设置
console.log('Current origin:', window.location.origin)
console.log('Target origin:', 'https://your-nextjs-app.com')
```

### 4. 性能问题

#### 问题：iframe 加载缓慢

**症状：**
- 页面加载时间过长
- 用户体验差

**解决方案：**

```vue
<template>
  <EmbedFrame
    page="dashboard"
    :timeout="45000"
    @load="onLoad"
    @error="onError"
  />
</template>

<script setup>
import { ref, onMounted } from 'vue'

const loadStartTime = ref(0)

onMounted(() => {
  loadStartTime.value = Date.now()
})

const onLoad = () => {
  const loadTime = Date.now() - loadStartTime.value
  console.log(`Page loaded in ${loadTime}ms`)
  
  if (loadTime > 10000) {
    console.warn('Slow loading detected')
    // 可以发送性能监控数据
  }
}

// 预加载优化
const preloadEmbed = () => {
  const link = document.createElement('link')
  link.rel = 'prefetch'
  link.href = embedUrl.value
  document.head.appendChild(link)
}
</script>
```

### 5. 样式问题

#### 问题：iframe 内容显示异常

**症状：**
- 样式错乱
- 响应式布局失效

**解决方案：**

```css
/* 确保 iframe 容器样式正确 */
.embed-frame-container {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.embed-iframe {
  width: 100%;
  border: none;
  display: block;
}

/* 响应式处理 */
@media (max-width: 768px) {
  .embed-frame-container {
    margin: 0;
    padding: 0;
  }
}
```

## 调试工具

### 1. 开发者工具

```javascript
// 嵌入系统调试工具
window.EmbedDebugger = {
  // 检查所有嵌入页面状态
  checkAllEmbeds() {
    const iframes = document.querySelectorAll('iframe[src*="/embed/"]')
    console.log(`Found ${iframes.length} embed iframes`)
    
    iframes.forEach((iframe, index) => {
      console.log(`Iframe ${index}:`, {
        src: iframe.src,
        width: iframe.width,
        height: iframe.height,
        loaded: iframe.contentDocument !== null
      })
    })
  },
  
  // 测试通信
  testCommunication(iframeIndex = 0) {
    const iframes = document.querySelectorAll('iframe[src*="/embed/"]')
    const iframe = iframes[iframeIndex]
    
    if (iframe?.contentWindow) {
      iframe.contentWindow.postMessage({
        type: 'DEBUG_TEST',
        timestamp: Date.now()
      }, new URL(iframe.src).origin)
      console.log('Debug message sent to iframe', iframeIndex)
    }
  },
  
  // 获取嵌入页面信息
  getEmbedInfo(iframeIndex = 0) {
    const iframes = document.querySelectorAll('iframe[src*="/embed/"]')
    const iframe = iframes[iframeIndex]
    
    if (iframe) {
      const url = new URL(iframe.src)
      return {
        page: url.pathname.split('/').pop(),
        token: url.searchParams.get('token')?.substring(0, 20) + '...',
        origin: url.origin,
        dimensions: {
          width: iframe.offsetWidth,
          height: iframe.offsetHeight
        }
      }
    }
  }
}

// 在控制台中使用：
// EmbedDebugger.checkAllEmbeds()
// EmbedDebugger.testCommunication()
// EmbedDebugger.getEmbedInfo()
```

### 2. 性能监控

```javascript
// 性能监控工具
class EmbedPerformanceMonitor {
  constructor() {
    this.metrics = new Map()
  }
  
  startTiming(embedId) {
    this.metrics.set(embedId, {
      startTime: performance.now(),
      loadTime: null,
      errorCount: 0
    })
  }
  
  endTiming(embedId) {
    const metric = this.metrics.get(embedId)
    if (metric) {
      metric.loadTime = performance.now() - metric.startTime
      console.log(`Embed ${embedId} loaded in ${metric.loadTime.toFixed(2)}ms`)
    }
  }
  
  recordError(embedId) {
    const metric = this.metrics.get(embedId)
    if (metric) {
      metric.errorCount++
    }
  }
  
  getReport() {
    const report = {}
    this.metrics.forEach((metric, embedId) => {
      report[embedId] = {
        loadTime: metric.loadTime,
        errorCount: metric.errorCount,
        status: metric.loadTime ? 'loaded' : 'loading'
      }
    })
    return report
  }
}

// 使用示例
const monitor = new EmbedPerformanceMonitor()
monitor.startTiming('dashboard')
// ... 页面加载完成后
monitor.endTiming('dashboard')
console.log(monitor.getReport())
```

## 最佳实践

### 1. 错误处理

```vue
<template>
  <EmbedFrame
    :page="page"
    @error="handleError"
    @load="handleLoad"
    :retry-count="3"
  />
</template>

<script setup>
const handleError = (error) => {
  // 记录错误
  console.error('Embed error:', error)
  
  // 发送错误报告
  if (typeof gtag !== 'undefined') {
    gtag('event', 'embed_error', {
      error_message: error,
      page_name: props.page
    })
  }
  
  // 显示用户友好的错误信息
  ElMessage.error('页面加载失败，请稍后重试')
}
</script>
```

### 2. 安全考虑

```javascript
// 验证消息来源
const handleMessage = (event) => {
  // 严格验证来源
  const allowedOrigins = [
    'https://your-nextjs-app.com',
    'https://staging-nextjs-app.com'
  ]
  
  if (!allowedOrigins.includes(event.origin)) {
    console.warn('Unauthorized message origin:', event.origin)
    return
  }
  
  // 验证消息格式
  if (!event.data || typeof event.data.type !== 'string') {
    console.warn('Invalid message format:', event.data)
    return
  }
  
  // 处理消息
  processMessage(event.data)
}
```

### 3. 性能优化

```vue
<script setup>
// 懒加载嵌入页面
const shouldLoadEmbed = ref(false)

onMounted(() => {
  // 使用 Intersection Observer 实现懒加载
  const observer = new IntersectionObserver((entries) => {
    if (entries[0].isIntersecting) {
      shouldLoadEmbed.value = true
      observer.disconnect()
    }
  })
  
  observer.observe(embedContainer.value)
})
</script>

<template>
  <div ref="embedContainer">
    <EmbedFrame v-if="shouldLoadEmbed" :page="page" />
    <div v-else class="embed-placeholder">
      点击加载嵌入内容
    </div>
  </div>
</template>
```

---

*本故障排除指南提供了常见问题的解决方案、调试工具和最佳实践，帮助开发者快速定位和解决问题。*
