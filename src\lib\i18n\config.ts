/**
 * i18n国际化配置
 * 支持中文、英文、日文三种语言
 */

// 支持的语言列表
export const locales = ['zh', 'en', 'ja'] as const
export type Locale = (typeof locales)[number]

// 默认语言
export const defaultLocale: Locale = 'zh'

// 语言显示名称映射
export const localeNames: Record<Locale, string> = {
  zh: '中文',
  en: 'English',
  ja: '日本語',
}

// 语言代码映射（与后端用户语言字段对应）
export const localeCodeMap: Record<string, Locale> = {
  chinese: 'zh',
  english: 'en',
  japanese: 'ja',
}

// 反向映射（前端语言代码到后端语言字段）
export const backendLanguageMap: Record<Locale, string> = {
  zh: 'chinese',
  en: 'english',
  ja: 'japanese',
}

/**
 * 获取用户偏好语言
 * @param userLanguage 后端用户语言字段
 * @returns 前端语言代码
 */
export function getUserLocale(userLanguage?: string): Locale {
  if (userLanguage && localeCodeMap[userLanguage]) {
    return localeCodeMap[userLanguage]
  }
  return defaultLocale
}

/**
 * 转换为后端语言字段
 * @param locale 前端语言代码
 * @returns 后端语言字段
 */
export function getBackendLanguage(locale: Locale): string {
  return backendLanguageMap[locale]
} 