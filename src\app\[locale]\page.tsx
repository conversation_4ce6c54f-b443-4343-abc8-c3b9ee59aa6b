'use client'

import { ProtectedRoute } from '@/components/providers/auth-provider'
import { LanguageSwitcherWithText } from '@/components/language-switcher'
import { useTranslations } from 'next-intl'

/**
 * Chat 页面 - 应用主入口
 * 🌐 支持国际化的Chat界面
 */
export default function ChatPage() {
  const t = useTranslations('chat')
  
  return (
    <ProtectedRoute>
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          {/* 语言切换器 */}
          <div className="absolute top-4 right-4">
            <LanguageSwitcherWithText />
          </div>
          
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {t('title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t('subtitle')}
          </p>
          <div className="animate-pulse">
            <div className="w-8 h-8 bg-blue-500 rounded-full mx-auto"></div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
} 