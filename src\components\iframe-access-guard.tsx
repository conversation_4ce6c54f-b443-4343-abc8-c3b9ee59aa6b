/**
 * iframe访问守卫组件
 * 在React组件层面可靠地检测iframe环境并显示访问受限页面
 */

'use client'

import { useEffect, useState } from 'react'

interface IframeAccessGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  isAuthenticated?: boolean
}

/**
 * 可靠的iframe检测
 * 在客户端使用JavaScript检测，比中间件更准确
 */
function useIframeDetection() {
  const [isInIframe, setIsInIframe] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    try {
      // 最可靠的iframe检测方法
      const inIframe = window.self !== window.top
      setIsInIframe(inIframe)
    } catch (e) {
      // 如果访问window.top抛出异常，说明在跨域iframe中
      setIsInIframe(true)
    } finally {
      setIsLoading(false)
    }
  }, [])

  return { isInIframe, isLoading }
}

/**
 * 访问受限页面组件
 */
function AccessDeniedPage() {
  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      margin: 0,
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      background: '#f8f9fa'
    }}>
      <div style={{
        textAlign: 'center',
        padding: '40px',
        background: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        maxWidth: '400px'
      }}>
        <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔒</div>
        <div style={{ 
          fontSize: '24px', 
          fontWeight: '600', 
          marginBottom: '8px', 
          color: '#333' 
        }}>
          访问受限
        </div>
        <div style={{ color: '#666', lineHeight: '1.5' }}>
          此页面需要登录后才能访问。<br />
          请在主窗口中完成登录。
        </div>
      </div>
    </div>
  )
}

/**
 * iframe访问守卫组件
 * 
 * 使用方式：
 * ```tsx
 * <IframeAccessGuard requireAuth={true} isAuthenticated={!!user}>
 *   <YourProtectedComponent />
 * </IframeAccessGuard>
 * ```
 */
export default function IframeAccessGuard({
  children,
  requireAuth = true,
  isAuthenticated = false
}: IframeAccessGuardProps) {
  const { isInIframe, isLoading } = useIframeDetection()

  // 加载中显示空白
  if (isLoading) {
    return null
  }

  // 如果在iframe中且需要认证但未认证，显示访问受限页面
  if (isInIframe && requireAuth && !isAuthenticated) {
    return <AccessDeniedPage />
  }

  // 正常渲染子组件
  return <>{children}</>
}

/**
 * 高阶组件版本，用于包装页面组件
 */
export function withIframeAccessGuard<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    requireAuth?: boolean
    getIsAuthenticated?: (props: P) => boolean
  } = {}
) {
  const { requireAuth = true, getIsAuthenticated } = options

  return function IframeGuardedComponent(props: P) {
    const isAuthenticated = getIsAuthenticated ? getIsAuthenticated(props) : false

    return (
      <IframeAccessGuard 
        requireAuth={requireAuth} 
        isAuthenticated={isAuthenticated}
      >
        <Component {...props} />
      </IframeAccessGuard>
    )
  }
}
