import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

export interface UIState {
  // 侧边栏状态
  sidebarOpen: boolean
  sidebarCollapsed: boolean

  // 模态框状态
  modals: Record<string, boolean>

  // 加载状态
  globalLoading: boolean

  // 通知状态
  notifications: Array<{
    id: string
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    message?: string
    duration?: number
  }>

  // 方法
  setSidebarOpen: (open: boolean) => void
  setSidebarCollapsed: (collapsed: boolean) => void
  toggleSidebar: () => void

  openModal: (modalId: string) => void
  closeModal: (modalId: string) => void
  toggleModal: (modalId: string) => void

  setGlobalLoading: (loading: boolean) => void

  addNotification: (notification: Omit<UIState['notifications'][0], 'id'>) => void
  removeNotification: (id: string) => void
  clearNotifications: () => void
}

export const useUIStore = create<UIState>()(
  devtools(
    immer((set) => ({
      // 初始状态
      sidebarOpen: true,
      sidebarCollapsed: false,
      modals: {},
      globalLoading: false,
      notifications: [],

      // 侧边栏方法
      setSidebarOpen: (open: boolean) => set(state => {
        state.sidebarOpen = open
      }),

      setSidebarCollapsed: (collapsed: boolean) => set(state => {
        state.sidebarCollapsed = collapsed
      }),

      toggleSidebar: () => set(state => {
        state.sidebarOpen = !state.sidebarOpen
      }),

      // 模态框方法
      openModal: (modalId: string) => set(state => {
        state.modals[modalId] = true
      }),

      closeModal: (modalId: string) => set(state => {
        state.modals[modalId] = false
      }),

      toggleModal: (modalId: string) => set(state => {
        state.modals[modalId] = !state.modals[modalId]
      }),

      // 加载状态方法
      setGlobalLoading: (loading: boolean) => set(state => {
        state.globalLoading = loading
      }),

      // 通知方法
      addNotification: (notification) => set(state => {
        const id = Date.now().toString()
        state.notifications.push({ ...notification, id })
      }),

      removeNotification: (id: string) => set(state => {
        state.notifications = state.notifications.filter(n => n.id !== id)
      }),

      clearNotifications: () => set(state => {
        state.notifications = []
      }),
    })),
    { name: 'ui-store' }
  )
)
