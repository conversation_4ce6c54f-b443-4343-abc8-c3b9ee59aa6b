# 快速部署脚本使用指南

## 🚀 一键部署

```bash
# 生产环境部署
./quick_deploy.sh --pd

# 开发环境部署  
./quick_deploy.sh --dev
```

## 📁 脚本说明

| 脚本 | 功能 | 用法 |
|------|------|------|
| `quick_deploy.sh` | 一键完整部署 | `./quick_deploy.sh [--dev\|--pd]` |
| `build_push_image.sh` | 构建推送镜像 | `./build_push_image.sh [--dev\|--pd]` |
| `deploy.sh` | 部署服务 | `./deploy.sh [--dev\|--pd]` |

## 🔧 环境配置

- **开发环境**: `--dev` - 启用调试模式
- **生产环境**: `--pd` - 性能优化模式

## 🎯 安全特性

- ✅ 只暴露 `/embed/*` 路径给外部访问
- ✅ 内网IP地址白名单保护
- ✅ Referer头部验证
- ✅ 私有Docker仓库部署

## 📞 问题排查

```bash
# 查看容器日志
docker logs nextjs-web-ui-pd

# 查看容器状态
docker ps | grep nextjs-web-ui
```

**快速部署指南**: [migration-docs/IFRAME_DEPLOY_QUICK.md](migration-docs/IFRAME_DEPLOY_QUICK.md) ⭐  
**详细部署指南**: [migration-docs/IFRAME_DEPLOY.md](migration-docs/IFRAME_DEPLOY.md)
