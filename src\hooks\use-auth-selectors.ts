/**
 * 认证相关的选择器Hooks
 * 提供优化的状态选择和计算属性
 */

import { useMemo } from 'react'
import { useAuthStore } from '@/stores/auth-store'
import type { AuthState, ApiUser as User } from '@/stores/auth-store'

// 用户类型映射
const USER_TYPE_MAP = {
  1: '管理员',
  2: '高级用户',
  3: '普通用户',
  4: '访客',
} as const

/**
 * 认证状态选择器
 */
export const authSelectors = {
  // 用户头像名称（取用户名前两个字符）
  avatarName: (state: AuthState) => state.user?.user_name.slice(0, 2) || '',

  // 用户ID
  userId: (state: AuthState) => state.user?.id,

  // 用户角色显示名称
  userRole: (state: AuthState) =>
    state.user?.user_type ? USER_TYPE_MAP[state.user.user_type as keyof typeof USER_TYPE_MAP] : null,

  // 检查tokens是否有效
  isTokensValid: (state: AuthState) => {
    const config = state.user?.company?.config
    if (!config) return false
    return config.tokens === 'unlimited' || (typeof config.tokens === 'number' && config.tokens > 0)
  },

  // 公司ID
  companyId: (state: AuthState) => state.user?.company?.id,

  // 用户语言
  userLanguage: (state: AuthState) => state.user?.language || 'chinese',

  // 用户步骤状态
  userStep: (state: AuthState) => state.user?.step || 0,

  // 是否需要完成问卷
  needsQuestionnaire: (state: AuthState) => (state.user?.step || 0) < 5,
}

/**
 * 基础认证状态Hook
 */
export function useAuth() {
  const auth = useAuthStore()

  return useMemo(() => ({
    user: auth.user,
    isAuthenticated: auth.isAuthenticated,
    isLoading: auth.isLoading,
    error: auth.error,
    setUser: auth.setUser,
    clearAuth: auth.clearAuth,
    login: auth.login,
    logout: auth.logout,
    validateToken: auth.validateToken,
    checkAccess: auth.checkAccess,
  }), [auth])
}

/**
 * 认证计算属性Hook
 */
export function useAuthSelectors() {
  const avatarName = useAuthStore(authSelectors.avatarName)
  const userId = useAuthStore(authSelectors.userId)
  const userRole = useAuthStore(authSelectors.userRole)
  const isTokensValid = useAuthStore(authSelectors.isTokensValid)
  const companyId = useAuthStore(authSelectors.companyId)
  const userLanguage = useAuthStore(authSelectors.userLanguage)
  const userStep = useAuthStore(authSelectors.userStep)
  const needsQuestionnaire = useAuthStore(authSelectors.needsQuestionnaire)

  return {
    avatarName,
    userId,
    userRole,
    isTokensValid,
    companyId,
    userLanguage,
    userStep,
    needsQuestionnaire,
  }
}

/**
 * 权限检查Hook
 */
export function usePermissions() {
  const { 
    menuConfig, 
    featureConfig, 
    userRoles, 
    userPermissions,
    checkAccess,
    hasPermission,
    hasFeatureAccess,
    hasRoleAccess 
  } = useAuthStore()

  return useMemo(() => ({
    // 基础权限检查方法
    checkAccess,
    hasPermission,
    hasFeatureAccess,
    hasRoleAccess,
    
    // 权限数据
    menuConfig,
    featureConfig,
    userRoles,
    userPermissions,
    
    // 业务权限检查（保持与原系统一致）
    canAccessNotifyFeature: featureConfig.some(f => f.id === 'notify' && f.status),
    canAccessNormalFeature: featureConfig.some(f => f.id === 'normal' && f.status),
    canAccessBindingFeature: featureConfig.some(f => 
      (f.id === 'binding' || f.id === 'strategic') && f.status
    ),
    canAccessBusinessOpportunityFeature: featureConfig.some(f => 
      f.id === 'binding' && f.status
    ),
    canAccessStrategicFeature: featureConfig.some(f => 
      f.id === 'strategic' && f.status
    ),
    canAccessSpecialAnalysisFeature: featureConfig.some(f => 
      (f.id === 'binding' || f.id === 'strategic') && f.status
    ),
  }), [
    menuConfig, 
    featureConfig, 
    userRoles, 
    userPermissions,
    checkAccess,
    hasPermission,
    hasFeatureAccess,
    hasRoleAccess
  ])
}

/**
 * 用户信息Hook
 */
export function useUser(): User | null {
  return useAuthStore(state => state.user)
}

/**
 * 认证状态Hook
 */
export function useAuthStatus() {
  return useAuthStore(state => ({
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    error: state.error,
  }))
}

/**
 * 权限配置Hook
 */
export function useAuthConfig() {
  return useAuthStore(state => ({
    menuConfig: state.menuConfig,
    featureConfig: state.featureConfig,
    userRoles: state.userRoles,
    userPermissions: state.userPermissions,
  }))
}
