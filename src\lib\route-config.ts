/**
 * 路由配置管理系统
 * 定义所有路由的认证、权限和重定向规则
 * 🔧 已清理无用路由，仅保留实际存在的页面配置
 */

import type { RouteConfig } from './middleware/types'

/**
 * 路由配置列表
 * 按照优先级排序，精确匹配优先于模糊匹配
 * ✅ 仅包含 src/app 中实际存在的页面
 */
export const routeConfigs: RouteConfig[] = [
  // 登录页面（仅限游客访问）
  {
    path: '/login',
    requiresGuest: true,
    redirectIfUnauthorized: '/' // 已登录用户重定向到Chat页面
  },

  // UI组件展示页面（公开访问，无需认证）
  {
    path: '/ui-gallery',
    // 公开访问，无需认证检查
  },

  // Embed页面（独立认证系统，跳过中间件）
  {
    path: '/embed',
    // embed页面有独立的token认证系统，跳过标准守卫
  },

  // Chat主页面（需要认证）
  {
    path: '/',
    requiresAuth: true,
    redirectIfUnauthorized: '/login'
  },

  // API路由（跳过守卫）
  {
    path: '/api',
    // API路由不需要前端守卫处理
  }
]

/**
 * 路由到菜单的映射关系
 * 🔧 已清理，仅保留实际存在的页面
 */
export const routeToMenuMap: Record<string, string> = {
  '/': 'chat', // 根路径为Chat页面
  '/ui-gallery': 'ui-gallery', // UI组件展示页面
  // embed页面不需要菜单映射（独立系统）
  // login页面不需要菜单映射（认证页面）
}

/**
 * 菜单项类型定义
 */
interface MenuItem {
  menuId: string
  route: string
  label: string
  children: Array<{
    menuId: string
    route: string
    label: string
  }>
}

/**
 * 菜单项配置
 * 🔧 已清理，仅保留实际存在的页面
 */
export const menuItems: MenuItem[] = [
  {
    menuId: 'chat',
    route: '/',
    label: 'Chat',
    children: []
  },
  {
    menuId: 'ui-gallery',
    route: '/ui-gallery',
    label: 'UI Gallery',
    children: []
  }
  // 注意：login和embed页面不需要菜单项（特殊页面）
]

/**
 * 获取路由配置
 * @param pathname 路径名
 * @returns 路由配置或null
 */
export function getRouteConfig(pathname: string): RouteConfig | null {
  // 精确匹配
  let config = routeConfigs.find(config => config.path === pathname)

  if (!config) {
    // 前缀匹配（用于动态路由和子路径）
    config = routeConfigs.find(config => {
      // 处理动态路由（包含[参数]的路径）
      if (config.path.includes('[')) {
        const pattern = config.path.replace(/\[.*?\]/g, '[^/]+')
        const regex = new RegExp(`^${pattern}$`)
        return regex.test(pathname)
      }
      
      // 处理子路径匹配
      return pathname.startsWith(config.path + '/') && config.path !== '/'
    })
  }

  return config || null
}

/**
 * 检查路由是否在菜单中
 * @param pathname 路径名
 * @returns 是否在菜单中
 */
export function isRouteInMenu(pathname: string): boolean {
  return Object.keys(routeToMenuMap).some(route => {
    if (route === pathname) return true
    if (pathname.startsWith(`${route}/`)) return true
    return false
  })
}

/**
 * 获取第一个可访问的路由
 * @param menuConfig 菜单配置
 * @returns 第一个可访问的路由路径
 */
export function getFirstAccessibleRoute(menuConfig: Array<{ id: string; status: boolean }>): string | undefined {
  for (const item of menuItems) {
    if (menuConfig.find(menu => menu.id === item.menuId && menu.status)) {
      return item.route
    }
    // 检查子菜单项
    if (item.children && item.children.length > 0) {
      for (const child of item.children) {
        if (menuConfig.find(menu => menu.id === child.menuId && menu.status)) {
          return child.route
        }
      }
    }
  }
  // 如果没有找到可访问的菜单项，返回根路径
  return '/'
}
