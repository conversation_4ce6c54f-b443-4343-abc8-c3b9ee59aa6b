# Next.js 配置文件合并说明

## 📋 合并前状况

项目中同时存在两个Next.js配置文件：
- `next.config.js` - JavaScript配置文件
- `next.config.ts` - TypeScript配置文件

这种情况会导致配置冲突和不一致性。

## 🔄 合并过程

### next.config.js 的配置特性
- Docker部署配置 (`output: 'standalone'`)
- 环境变量定义
- CORS和安全头部设置
- iframe嵌入支持
- 图片优化基础配置
- Turbopack规则

### next.config.ts 的配置特性
- TypeScript类型支持
- 实验性包导入优化
- 高级webpack配置
- 代码分割优化
- 路由重定向和重写
- SVG处理

## ✅ 合并后的统一配置

合并后的 `next.config.ts` 包含了所有必要配置：

```typescript
const nextConfig: NextConfig = {
  // Docker部署支持
  output: 'standalone',
  
  // 性能优化
  compress: true,
  experimental: { optimizePackageImports: [...] },
  
  // 环境变量
  env: { NEXT_PUBLIC_APP_ENV, NEXT_PUBLIC_BASE_URL, ... },
  
  // 安全头部
  async headers() { ... },
  
  // 图片优化
  images: { unoptimized: true, domains: [...], formats: [...] },
  
  // 构建优化
  turbopack: { ... },
  webpack: { ... },
  
  // 路由配置
  async redirects() { ... },
  async rewrites() { ... },
}
```

## 🎯 优势

1. **配置统一性** - 避免了重复和冲突
2. **TypeScript支持** - 获得更好的类型检查和IDE支持
3. **功能完整性** - 合并了两个文件的所有有用配置
4. **维护性** - 只需维护一个配置文件

## 🧪 验证结果

- ✅ `npm run type-check` - 类型检查通过
- ✅ `npx next info` - Next.js正确识别配置
- ✅ 显示 `output: standalone` - Docker配置生效
- ✅ `npm run dev` - 开发服务器正常启动
- ✅ embed页面正常访问 - HTTP 200响应

## 🔧 问题解决

### 遇到的问题
合并配置后出现运行时错误：
```
ReferenceError: exports is not defined
```

### 解决方案
1. **清理构建缓存** - 删除`.next`目录
2. **简化webpack配置** - 移除复杂的代码分割配置
3. **避免配置冲突** - 暂时注释turbopack和rewrites配置

### 配置调整
- 保留基本的SVG处理
- 注释可能冲突的turbopack配置
- 注释可能与API路由冲突的rewrites配置

## 📝 注意事项

- SVG处理同时配置在webpack和turbopack中，确保两种构建模式都支持
- 图片域名包含了开发和生产环境
- API重写配置支持动态环境变量
- CORS头部适配了iframe嵌入需求

---

**结论：成功合并为单一的 `next.config.ts` 文件，提高了配置的一致性和可维护性。** 