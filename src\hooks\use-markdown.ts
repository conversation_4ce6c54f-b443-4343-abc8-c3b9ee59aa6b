'use client'

import { useMemo } from 'react'
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'

// 导入所有 CJS 插件
import * as abbr from 'markdown-it-abbr'
import * as anchor from 'markdown-it-anchor'
import * as container from 'markdown-it-container'
import * as deflist from 'markdown-it-deflist'
// 引入 emoji 插件：使用 full 配置
import { full as emoji } from 'markdown-it-emoji'
import * as footnote from 'markdown-it-footnote'
import * as ins from 'markdown-it-ins'
import * as mark from 'markdown-it-mark'
import * as sub from 'markdown-it-sub'
import * as sup from 'markdown-it-sup'
import * as taskLists from 'markdown-it-task-lists'

// 强制提取插件函数
function normalizePlugin(mod: any): any {
  if (typeof mod === 'function') return mod
  if (mod?.default && typeof mod.default === 'function') return mod.default
  const fn = Object.values(mod).find(v => typeof v === 'function')
  if (fn) return fn
  throw new Error('Invalid markdown-it plugin: no function export found')
}

// 注入全局 isSpace，解决解析依赖问题
function ensureGlobalIsSpace() {
  if (typeof window !== 'undefined' && !(window as any).isSpace) {
    const mdTmp = new MarkdownIt()
    ;(window as any).isSpace = mdTmp.utils.isSpace
  }
}

export function useMarkdown(content: string): string {
  return useMemo(() => {
    ensureGlobalIsSpace()

    const md = new MarkdownIt({
      html: false,
      linkify: true,
      typographer: true,
    })

    // 高亮回调，捕获外部 md
    md.set({
      highlight(str: string, lang: string) {
        if (lang && hljs.getLanguage(lang)) {
          try {
            const highlighted = hljs.highlight(str, { language: lang }).value
            return `<pre class="hljs"><code>${highlighted}</code></pre>`
          } catch {}
        }
        return `<pre class="hljs"><code>${md.utils.escapeHtml(str)}</code></pre>`
      },
    })

    // 注册常规容器，默认div
    const types = ['info', 'warning', 'tip', 'danger']
    types.forEach((type) => {
      md.use(normalizePlugin(container), type, {
        render(tokens:any, idx:number) {
          const token = tokens[idx]
          if (token.nesting === 1) {
            return `<div class="${type}">`
          } else {
            return `</div>\n`
          }
        },
      })
    })
    // 注册details容器
    md.use(normalizePlugin(container), 'details', {
      render(tokens:any, idx:number) {
        const info = tokens[idx].info.trim().slice('details'.length).trim()
        if (tokens[idx].nesting === 1) {
          return `<details><summary>${md.utils.escapeHtml(info) || 'Details'}</summary>\n`
        } else {
          return `</details>\n`
        }
      },
    })

    // 注册插件
    md.use(normalizePlugin(abbr))
      .use(normalizePlugin(anchor), { permalink: false })
      .use(normalizePlugin(container), 'info')
      .use(normalizePlugin(deflist))
      .use(normalizePlugin(emoji))
      .use(normalizePlugin(footnote))
      .use(normalizePlugin(ins))
      .use(normalizePlugin(mark))
      .use(normalizePlugin(sub))
      .use(normalizePlugin(sup))
      .use(normalizePlugin(taskLists), { enabled: true })

    return md.render(content)
  }, [content])
}
