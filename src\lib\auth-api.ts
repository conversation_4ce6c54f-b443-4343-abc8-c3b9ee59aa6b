/**
 * 认证相关API客户端
 * 处理登录、注册、token验证等认证相关的API调用
 */

import { tokenManager } from './token'
import { responseInterceptor, interceptorConfigs } from './api-interceptor'
import type { LoginType, LoginParams, ApiUser as User, MenuConfig, FeatureConfig, UserRole, UserPermission } from '@/stores/auth-store'

// API基础配置 - 使用相对路径通过Next.js代理
const API_BASE_URL = ''

// API响应类型
interface ApiResponse<T = any> {
  code: number
  success: boolean
  message: string
  data?: T
}

interface LoginResponse {
  token: string
  user: User
}



/**
 * HTTP请求工具
 */
async function request<T>(
  endpoint: string,
  options: RequestInit = {},
  skipAuth = false
): Promise<ApiResponse<T>> {
  const url = `${API_BASE_URL}${endpoint}`
  const token = tokenManager.getToken()

  // 获取用户语言设置，默认为中文
  const language = typeof window !== 'undefined'
    ? localStorage.getItem('user-language') || 'chinese'
    : 'chinese'

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'language': language,
    ...options.headers as Record<string, string>,
  }

  // 除了登录接口外，所有接口都需要携带authorization
  if (!skipAuth && token) {
    headers['authorization'] = token
  }

  const config: RequestInit = {
    ...options,
    headers,
  }

  try {
    const response = await fetch(url, config)
    const data = await response.json()

    // 如果HTTP状态码不是2xx，但API返回了结构化错误信息，仍然返回数据
    // 让响应拦截器处理success字段
    if (!response.ok && !data.hasOwnProperty('success')) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return data
  } catch (error: any) {
    console.error('API request failed:', error)
    throw error
  }
}

/**
 * 认证API客户端
 */
export const authAPI = {
  /**
   * 用户登录
   */
  login: async (type: LoginType, params: LoginParams): Promise<LoginResponse> => {
    // 根据登录类型转换参数
    let loginData: { user_name: string; password?: string; phone?: string; code?: string }

    if (type === 'password') {
      const passwordParams = params as Extract<LoginParams, { username: string }>
      loginData = {
        user_name: passwordParams.username,
        password: passwordParams.password
      }
    } else if (type === 'sms') {
      const smsParams = params as Extract<LoginParams, { phone: string }>
      loginData = {
        user_name: smsParams.phone,
        phone: smsParams.phone,
        code: smsParams.code
      }
    } else if (type === 'email') {
      const emailParams = params as Extract<LoginParams, { email: string }>
      loginData = {
        user_name: emailParams.email,
        code: emailParams.code
      }
    } else {
      throw new Error(`Unsupported login type: ${type}`)
    }

    return responseInterceptor(
      request<LoginResponse>('/api/user/login', {
        method: 'POST',
        body: JSON.stringify(loginData),
      }, true), // 跳过认证
      interceptorConfigs.auth
    )
  },

  /**
   * 用户注册
   */
  register: async (params: {
    name: string
    email: string
    password: string
    confirmPassword: string
  }): Promise<ApiResponse<LoginResponse>> => {
    return request<LoginResponse>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(params),
    })
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser: async (): Promise<User> => {
    return responseInterceptor(
      request<User>('/api/user/get_current'),
      interceptorConfigs.silent
    )
  },

  /**
   * 验证token并获取用户信息（兼容旧接口）
   */
  validateToken: async (): Promise<User> => {
    return authAPI.getCurrentUser()
  },

  /**
   * 刷新token
   */
  refreshToken: async (): Promise<ApiResponse<{ token: string; refreshToken: string }>> => {
    const refreshToken = tokenManager.getRefreshToken()
    return request('/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({ refreshToken }),
    })
  },

  /**
   * 用户登出
   */
  logout: async (): Promise<void> => {
    try {
      await responseInterceptor(
        request('/api/user/logout', {
          method: 'POST',
          body: JSON.stringify({}),
        }),
        interceptorConfigs.silent
      )
    } catch (error) {
      // 即使API调用失败，也要清除本地状态
      console.warn('Logout API call failed, but continuing with local cleanup:', error)
    }
  },

  /**
   * 获取菜单配置
   */
  getMenuConfig: async (): Promise<MenuConfig[]> => {
    const response = await request<MenuConfig[]>('/auth/menu-config')
    return response.data || []
  },

  /**
   * 获取功能配置
   */
  getFeatureConfig: async (): Promise<FeatureConfig[]> => {
    const response = await request<FeatureConfig[]>('/auth/feature-config')
    return response.data || []
  },

  /**
   * 获取用户角色
   */
  getUserRoles: async (): Promise<UserRole[]> => {
    const response = await request<UserRole[]>('/auth/user-roles')
    return response.data || []
  },

  /**
   * 获取用户权限
   */
  getUserPermissions: async (): Promise<UserPermission[]> => {
    const response = await request<UserPermission[]>('/auth/user-permissions')
    return response.data || []
  },

  /**
   * 修改密码
   */
  changePassword: async (params: {
    currentPassword: string
    newPassword: string
    confirmPassword: string
  }): Promise<ApiResponse> => {
    return request('/auth/change-password', {
      method: 'POST',
      body: JSON.stringify(params),
    })
  },

  /**
   * 忘记密码
   */
  forgotPassword: async (email: string): Promise<ApiResponse> => {
    return request('/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email }),
    })
  },

  /**
   * 重置密码
   */
  resetPassword: async (params: {
    token: string
    password: string
    confirmPassword: string
  }): Promise<ApiResponse> => {
    return request('/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify(params),
    })
  },
}
