/**
 * API响应拦截器
 * 类似Vue3中axios的响应拦截器，统一处理API响应
 * 特别处理认证相关错误码，自动清空token并重定向到登录页
 */

import { toast } from 'sonner'
import { shouldLogout, getErrorMessage as getErrorCodeMessage } from '@/lib/constants/error-codes'

/**
 * 处理认证错误的全局处理函数
 * 清空token、用户状态并重定向到登录页
 */
async function handleAuthenticationError(errorCode: number, message: string) {
  try {
    // 动态导入token管理器和auth store，避免循环依赖
    const { tokenManager } = await import('@/lib/token')
    
    // 清空token存储
    tokenManager.removeTokens()
    
    // 显示认证错误提示
    const errorMessage = getErrorCodeMessage(errorCode, message)
    toast.error(errorMessage)
    
    // 延迟重定向，确保用户能看到错误提示
    setTimeout(() => {
      if (typeof window !== 'undefined') {
        // 重定向到登录页，保存当前路径用于登录后回到原页面
        const currentPath = window.location.pathname
        const targetPath = currentPath === '/login' ? '/' : currentPath
        window.location.href = `/login?redirect=${encodeURIComponent(targetPath)}`
      }
    }, 1000)
    
  } catch (error) {
    console.error('处理认证错误时发生异常:', error)
    
    // 降级处理：直接重定向到登录页
    if (typeof window !== 'undefined') {
      window.location.href = '/login'
    }
  }
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  success: boolean
  message: string
  data?: T
}

// 错误类型
export class ApiError extends Error {
  code: number
  success: boolean
  data?: any

  constructor(response: ApiResponse) {
    super(response.message)
    this.name = 'ApiError'
    this.code = response.code
    this.success = response.success
    this.data = response.data
  }
}

/**
 * 响应拦截器
 * 自动提取data字段，处理错误响应
 */
export async function responseInterceptor<T>(
  responsePromise: Promise<ApiResponse<T>>,
  options: {
    showError?: boolean // 是否显示错误提示
    showSuccess?: boolean // 是否显示成功提示
    successMessage?: string // 自定义成功消息
    errorMessage?: string // 自定义错误消息
  } = {}
): Promise<T> {
  const {
    showError = true,
    showSuccess = false,
    successMessage,
    errorMessage
  } = options

  try {
    const response = await responsePromise

    // 检查响应是否成功
    if (response.success) {
      // 显示成功提示
      if (showSuccess) {
        toast.success(successMessage || response.message || '操作成功')
      }

      // 返回data字段，如果没有data则返回整个响应
      return response.data !== undefined ? response.data : (response as any)
    } else {
      // 响应失败，抛出错误
      const error = new ApiError(response)
      
      // 🔧 处理认证相关错误码，自动清空token并重定向
      if (shouldLogout(response.code)) {
        console.warn(`🚨 检测到认证错误 (${response.code}): ${response.message}`)
        
        // 清空token和认证状态
        await handleAuthenticationError(response.code, response.message)
        
        // 特殊处理，不显示通用错误提示，已在handleAuthenticationError中处理
        throw error
      }
      
      // 显示错误提示（非认证错误）
      if (showError) {
        const displayMessage = errorMessage || getErrorCodeMessage(response.code, response.message) || '操作失败'
        toast.error(displayMessage)
      }

      throw error
    }
  } catch (error) {
    // 网络错误或其他异常
    if (error instanceof ApiError) {
      throw error
    }

    // 处理网络错误
    const networkError = new ApiError({
      code: 0,
      success: false,
      message: error instanceof Error ? error.message : '网络错误',
      data: null
    })

    if (showError) {
      toast.error(errorMessage || '网络连接失败，请检查网络设置')
    }

    throw networkError
  }
}

/**
 * 创建带拦截器的API请求函数
 */
export function createApiRequest<T>(
  requestFn: () => Promise<ApiResponse<T>>,
  options?: Parameters<typeof responseInterceptor>[1]
): Promise<T> {
  return responseInterceptor(requestFn(), options)
}

/**
 * 常用的拦截器配置
 */
export const interceptorConfigs = {
  // 静默请求（不显示任何提示）
  silent: {
    showError: false,
    showSuccess: false
  },
  
  // 只显示错误
  errorOnly: {
    showError: true,
    showSuccess: false
  },
  
  // 显示成功和错误
  full: {
    showError: true,
    showSuccess: true
  },
  
  // 登录相关
  auth: {
    showError: true,
    showSuccess: true,
    successMessage: '登录成功',
    errorMessage: '登录失败'
  },
  
  // 数据保存
  save: {
    showError: true,
    showSuccess: true,
    successMessage: '保存成功',
    errorMessage: '保存失败'
  },
  
  // 数据删除
  delete: {
    showError: true,
    showSuccess: true,
    successMessage: '删除成功',
    errorMessage: '删除失败'
  }
}

/**
 * 错误码处理映射
 */
export const errorCodeMap: Record<number, string> = {
  400: '请求参数错误',
  401: '未授权，请重新登录',
  403: '权限不足',
  404: '请求的资源不存在',
  405: '请求方法不允许',
  408: '请求超时',
  500: '服务器内部错误',
  502: '网关错误',
  503: '服务不可用',
  504: '网关超时',
  601: '用户未登录',
  602: '参数错误'
}

/**
 * 根据错误码获取友好的错误消息
 */
export function getErrorMessage(code: number, defaultMessage?: string): string {
  return errorCodeMap[code] || defaultMessage || '未知错误'
}
