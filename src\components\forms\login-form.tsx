'use client'

import { useState, FormEvent } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslations, useLocale } from 'next-intl'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent } from '@/components/ui/card'
import { useAuth } from '@/hooks/use-auth-selectors'
import { useUI } from '@/hooks/use-store-selectors'
import type { PasswordLoginParams } from '@/stores/auth-store'

interface LoginFormProps {
  onSuccess?: () => void
  className?: string
}

export function LoginForm({ onSuccess, className, ...props }: LoginFormProps & React.ComponentProps<"div">) {
  const [form, setForm] = useState<PasswordLoginParams>({
    username: '',
    password: '',
    remember: false,
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  const { login, isLoading, error, user } = useAuth()
  const { addNotification } = useUI()
  const router = useRouter()
  const locale = useLocale()
  const t = useTranslations('auth')

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!form.username.trim()) {
      newErrors.username = t('validation.usernameRequired')
    }

    if (!form.password.trim()) {
      newErrors.password = t('validation.passwordRequired')
    }
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      const result = await login('password', form)

      if (result.success) {
        // 存储用户语言设置
        if (user?.language) {
          localStorage.setItem('user-language', user.language)
        }

        addNotification({
          type: 'success',
          title: t('messages.loginSuccess'),
          message: t('messages.welcomeBack', { username: form.username }),
        })

        // 根据用户状态决定跳转路径（考虑国际化）
        if (result.isToStep) {
          router.push(`/${locale}/poll`)
        } else {
          // 从URL参数获取重定向路径，默认为根路径
          const urlParams = new URLSearchParams(window.location.search)
          const redirectPath = urlParams.get('redirect') || `/${locale}`
          router.push(redirectPath)
        }

        onSuccess?.()
      }
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: t('messages.loginFailed'),
        message: error.message || t('messages.loginError'),
      })
    }
  }

  const handleInputChange = (field: keyof PasswordLoginParams) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value
    setForm(prev => ({ ...prev, [field]: value }))

    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card className="overflow-hidden p-0">
        <CardContent className="grid p-0 md:grid-cols-2">
          <form onSubmit={handleSubmit} className="p-6 md:p-8">
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <h1 className="text-2xl font-bold">{t('login.title')}</h1>
                <p className="text-muted-foreground text-balance">
                  {t('login.subtitle')}
                </p>
              </div>

              <div className="grid gap-3">
                <Label htmlFor="username">{t('login.username')}</Label>
                <Input
                  id="username"
                  type="text"
                  placeholder={t('login.usernamePlaceholder')}
                  value={form.username}
                  onChange={handleInputChange('username')}
                  disabled={isLoading}
                  required
                />
                {errors.username && (
                  <p className="text-sm text-destructive">{errors.username}</p>
                )}
              </div>

              <div className="grid gap-3">
                <div className="flex items-center">
                  <Label htmlFor="password">{t('login.password')}</Label>
                  <button
                    type="button"
                    className="ml-auto text-sm underline-offset-2 hover:underline"
                    onClick={() => {
                      addNotification({
                        type: 'info',
                        title: t('messages.featureInDevelopment'),
                        message: t('messages.forgotPasswordInDevelopment'),
                      })
                    }}
                  >
                    {t('login.forgotPassword')}
                  </button>
                </div>
                <Input
                  id="password"
                  type="password"
                  placeholder={t('login.passwordPlaceholder')}
                  value={form.password}
                  onChange={handleInputChange('password')}
                  disabled={isLoading}
                  required
                />
                {errors.password && (
                  <p className="text-sm text-destructive">{errors.password}</p>
                )}
              </div>

              {error && (
                <div className="p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md">
                  {error}
                </div>
              )}

              <Button type="submit" disabled={isLoading} className="w-full">
                {isLoading ? t('login.loggingIn') : t('login.loginButton')}
              </Button>

              <div className="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
                <span className="bg-card text-muted-foreground relative z-10 px-2">
                  {t('login.orLoginWith')}
                </span>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <Button variant="outline" type="button" className="w-full" disabled>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="h-4 w-4">
                    <path
                      d="M12.152 6.896c-.948 0-2.415-1.078-3.96-1.04-2.04.027-3.91 1.183-4.961 3.014-2.117 3.675-.546 9.103 1.519 12.09 1.013 1.454 2.208 3.09 3.792 3.039 1.52-.065 2.09-.987 3.935-.987 1.831 0 2.35.987 3.96.948 1.637-.026 2.676-1.48 3.676-2.948 1.156-1.688 1.636-3.325 1.662-3.415-.039-.013-3.182-1.221-3.22-4.857-.026-3.04 2.48-4.494 2.597-4.559-1.429-2.09-3.623-2.324-4.39-2.376-2-.156-3.675 1.09-4.61 1.09zM15.53 3.83c.843-1.012 1.4-2.427 1.245-3.83-1.207.052-2.662.805-3.532 1.818-.78.896-1.454 2.338-1.273 3.714 1.338.104 2.715-.688 3.559-1.701"
                      fill="currentColor"
                    />
                  </svg>
                  <span className="sr-only">{t('login.loginWithApple')}</span>
                </Button>
                <Button variant="outline" type="button" className="w-full" disabled>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="h-4 w-4">
                    <path
                      d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
                      fill="currentColor"
                    />
                  </svg>
                  <span className="sr-only">{t('login.loginWithGoogle')}</span>
                </Button>
                <Button variant="outline" type="button" className="w-full" disabled>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="h-4 w-4">
                    <path
                      d="M13.397 20.997v-8.196h2.765l.411-3.209h-3.176V7.548c0-.926.258-1.56 1.587-1.56h1.684V3.127A22.336 22.336 0 0 0 14.201 3c-2.444 0-4.122 1.492-4.122 4.231v2.355H7.332v3.209h2.753v8.202h3.312z"
                      fill="currentColor"
                    />
                  </svg>
                  <span className="sr-only">{t('login.loginWithFacebook')}</span>
                </Button>
              </div>

              <div className="text-center text-sm">
                {t('login.noAccount')}{" "}
                <button
                  type="button"
                  className="underline underline-offset-4"
                  onClick={() => {
                    addNotification({
                      type: 'info',
                      title: t('messages.featureInDevelopment'),
                      message: t('messages.registerInDevelopment'),
                    })
                  }}
                >
                  {t('login.registerNow')}
                </button>
              </div>
            </div>
          </form>
          <div className="bg-muted relative hidden md:block">
            <div className="absolute inset-0 bg-gradient-to-br from-primary-500 to-secondary-500 opacity-90" />
            <div className="absolute inset-0 flex items-center justify-center p-8">
              <div className="text-center text-white">
                <h2 className="text-2xl font-bold mb-4">{t('login.brandTitle')}</h2>
                <p className="text-lg opacity-90 mb-6">{t('login.brandSubtitle')}</p>
                <div className="space-y-2 text-sm opacity-80">
                  <p>{t('login.feature1')}</p>
                  <p>{t('login.feature2')}</p>
                  <p>{t('login.feature3')}</p>
                  <p>{t('login.feature4')}</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      <div className="text-muted-foreground text-center text-xs text-balance">
        {t('login.termsText')}{" "}
        <button className="underline underline-offset-4 hover:text-primary">
          {t('login.termsOfService')}
        </button>{" "}
        {t('login.and')}{" "}
        <button className="underline underline-offset-4 hover:text-primary">
          {t('login.privacyPolicy')}
        </button>
        {t('login.period')}
      </div>
    </div>
  )
}
