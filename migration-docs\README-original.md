# Specific AI - Next.js 企业级前端架构

## 项目概述

这是一个基于 Next.js 15 的企业级AI应用前端架构，从Vue3技术栈迁移而来。项目采用现代化的技术栈和最佳实践，提供高性能、可扩展的前端解决方案。

## 技术栈

### 核心框架
- **Next.js 15** - React全栈框架，支持App Router
- **React 18** - 用户界面库
- **TypeScript** - 类型安全的JavaScript

### 样式系统
- **TailwindCSS v4** - 原子化CSS框架，使用新的CSS-first配置
- **CSS Variables** - 动态主题系统
- **Responsive Design** - 移动优先的响应式设计

### 状态管理
- **Zustand** - 轻量级状态管理库
- **Immer** - 不可变状态更新
- **Persist Middleware** - 状态持久化

### UI组件
- **Radix UI** - 无样式、可访问的UI组件
- **Class Variance Authority** - 组件变体管理
- **Lucide React** - 图标库

### 动画系统
- **Framer Motion** - 声明式动画库
- **自定义动画组件** - 预设动画效果

### 开发工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **TypeScript** - 类型检查

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # 全局样式和主题
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/             # 组件库
│   └── ui/                # 基础UI组件
│       ├── button.tsx     # 按钮组件
│       ├── input.tsx      # 输入框组件
│       └── animated.tsx   # 动画组件
├── stores/                 # 状态管理
│   ├── auth-store.ts      # 认证状态
│   ├── ui-store.ts        # UI状态
│   ├── cache-store.ts     # 缓存状态
│   ├── error-store.ts     # 错误状态
│   └── index.ts           # Store导出
├── types/                  # 类型定义
│   └── index.ts           # 全局类型
├── lib/                    # 工具库
│   ├── utils.ts           # 通用工具函数
│   └── animations.ts      # 动画配置
└── hooks/                  # 自定义Hooks
```

## 核心特性

### 🎨 企业级设计系统
- 完整的颜色系统（主色、辅色、语义色）
- 统一的字体、间距、圆角规范
- 暗色模式支持
- 响应式断点系统

### 🔄 状态管理架构
- **认证Store**: 用户认证、权限管理
- **UI Store**: 主题、布局、通知管理
- **缓存Store**: 智能缓存系统，支持TTL和标签
- **错误Store**: 统一错误处理和重试机制

### 🎭 动画系统
- 预设动画效果（淡入淡出、滑动、缩放等）
- 列表动画和页面过渡
- 悬停和点击交互效果
- 性能优化的动画配置

### 🧩 组件库
- 基于Radix UI的无障碍组件
- 支持变体和主题的设计系统
- TypeScript类型安全
- 可扩展的组件架构

## 快速开始

### 安装依赖
```bash
npm install
```

### 开发环境
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 代码检查
```bash
npm run lint
```

## 配置说明

### TailwindCSS v4配置
项目使用TailwindCSS v4的新配置方式，通过`@theme`指令在CSS中定义设计令牌：

```css
@theme {
  --color-primary-500: oklch(0.65 0.25 264);
  --font-sans: "Inter", sans-serif;
  --spacing-md: 1rem;
}
```

### 状态管理
使用Zustand进行状态管理，支持TypeScript和持久化：

```typescript
const useAuthStore = create<AuthStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        // store implementation
      })),
      { name: 'auth-store' }
    )
  )
)
```

## 开发规范

### 代码风格
- 使用TypeScript进行类型安全开发
- 遵循ESLint和Prettier配置
- 组件使用PascalCase命名
- 文件使用kebab-case命名

### 组件开发
- 使用forwardRef支持ref传递
- 支持className和children props
- 使用Variants API管理组件变体
- 提供完整的TypeScript类型定义

### 状态管理
- 按功能模块划分Store
- 使用Immer进行不可变更新
- 重要状态启用持久化
- 提供清晰的action命名

## 迁移进度

### ✅ 已完成
- [x] Next.js 15项目初始化
- [x] TailwindCSS v4配置和主题系统
- [x] Zustand状态管理架构
- [x] 基础UI组件库（Button、Input）
- [x] Framer Motion动画系统
- [x] TypeScript类型系统
- [x] 开发工具配置

### 🔄 进行中
- [ ] 完整组件库开发
- [ ] 路由守卫系统
- [ ] 认证系统集成
- [ ] 页面模块迁移

### 📋 待开始
- [ ] 数据可视化组件
- [ ] 国际化系统
- [ ] 测试框架集成
- [ ] 性能优化

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License
