import type { NextConfig } from "next";
import createNextIntlPlugin from 'next-intl/plugin'

// 创建next-intl插件
const withNextIntl = createNextIntlPlugin('./src/lib/i18n/request.ts')

/** @type {import('next').NextConfig} */
const nextConfig: NextConfig = {
  // Enable standalone output for Docker deployment
  output: 'standalone',

  // Compress responses
  compress: true,

  // Asset prefix for proper static resource loading in iframe
  ...(process.env.NODE_ENV === 'production' && {
    assetPrefix: process.env.NEXT_PUBLIC_BASE_URL || 'https://dev.goglobalsp.com'
  }),

  // Base path for deployment
  basePath: process.env.NEXT_PUBLIC_BASE_PATH || '',

  // Environment variables
  env: {
    NEXT_PUBLIC_APP_ENV: process.env.NEXT_PUBLIC_APP_ENV || 'production',
    NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://dev.goglobalsp.com',
    NEXT_PUBLIC_EMBED_ENABLED: process.env.NEXT_PUBLIC_EMBED_ENABLED || 'true',
    NEXT_PUBLIC_EXCLUDE_UI_GALLERY: process.env.NEXT_PUBLIC_EXCLUDE_UI_GALLERY || 'false',
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // Webpack configuration - simplified
  webpack: (config) => {
    return config;
  },



  // Headers for CORS and security
  async headers() {
    // 🔧 开发环境和生产环境使用不同的CSP策略
    const isDevelopment = process.env.NODE_ENV === 'development'
    
    const embedHeaders = isDevelopment ? [
      // 🔓 开发环境：超级宽松的策略，允许所有localhost端口
      {
        key: 'X-Frame-Options',
        value: 'SAMEORIGIN'
      },
      {
        key: 'Content-Security-Policy',
        value: "frame-ancestors 'self' http://localhost:3000 http://localhost:3001 http://localhost:8000 http://localhost:8080 http://localhost:9000 http://localhost:10001 http://127.0.0.1:3000 http://127.0.0.1:3001 http://127.0.0.1:8000 http://127.0.0.1:8080 http://127.0.0.1:9000 http://127.0.0.1:10001 * data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"
      },
      {
        key: 'Cache-Control',
        value: 'no-cache, no-store, must-revalidate'
      },
      {
        key: 'X-Content-Type-Options',
        value: 'nosniff'
      }
    ] : [
      // 🔒 生产环境：严格的安全策略
      {
        key: 'X-Frame-Options',
        value: 'ALLOWALL'
      },
      {
        key: 'Content-Security-Policy',
        value: "frame-ancestors 'self' https://dev.goglobalsp.com https://www.goglobalsp.com https://dev.specific-ai.com; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"
      },
      {
        key: 'Cache-Control',
        value: 'public, max-age=31536000, immutable'
      },
      {
        key: 'X-Content-Type-Options',
        value: 'nosniff'
      }
    ]

    const baseHeaders = [
      {
        source: '/embed/:path*',
        headers: embedHeaders,
      },
      {
        source: '/_next/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable'
          },
          {
            key: 'Access-Control-Allow-Origin',
            value: '*'
          }
        ],
      },
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*'
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS'
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Authorization, language, timezone, timezonestr, X-Requested-With, Content-Type, Cookie, User-Agent'
          }
        ],
      }
    ];

    // 🎨 UI Gallery specific headers (仅在开发环境，因为生产环境ui-gallery被排除)
    if (isDevelopment) {
      baseHeaders.push({
        source: '/ui-gallery',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate'
          },
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow' // Prevent search engine indexing
          }
        ],
      });
    }

    return baseHeaders;
  },

  // Image optimization
  images: {
    unoptimized: true,
    domains: ['localhost', 'dev.goglobalsp.com'],
    formats: ['image/webp', 'image/avif'],
  },

  // 重定向配置
  async redirects() {
    const baseRedirects = [
      // 重定向到国际化根目录
      {
        source: '/',
        destination: '/zh',
        permanent: false,
      },
      // 重定向旧的chat路径到国际化路径
      {
        source: '/chat',
        destination: '/zh/chat',
        permanent: true,
      },
      // 重定向login路径到国际化路径
      {
        source: '/login',
        destination: '/zh/login',
        permanent: false,
      },
      // ui-gallery不再需要国际化重定向，直接访问
      // 注释掉旧的重定向规则
    ];

    // 🚫 生产环境重定向ui-gallery到404（因为文件在构建时被移除）
    if (process.env.NODE_ENV === 'production') {
      baseRedirects.push(
        {
          source: '/ui-gallery',
          destination: '/404',
          permanent: false,
        },
        {
          source: '/ui-gallery/:path*',
          destination: '/404',
          permanent: false,
        }
      );
    }

    return baseRedirects;
  },
};

// 使用next-intl插件包装配置
export default withNextIntl(nextConfig);
