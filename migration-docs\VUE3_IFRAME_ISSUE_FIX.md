# Vue3 项目 iframe 嵌入问题修复指南

## 🚨 问题分析

您在Vue3项目中嵌入iframe时遇到的问题主要是**域名白名单**问题：

**问题原因**：
- Vue3项目运行在 `http://localhost:10001`
- 但是Next.js嵌入系统的域名白名单中没有包含这个端口
- 导致跨域请求被拒绝，iframe无法加载

**错误表现**：
```
fetch("https://dev.goglobalsp.com/embed/dashboard?token=...", {
  "referrer": "http://localhost:10001/",
  "sec-fetch-site": "cross-site"
})
```

## ✅ 解决方案

### 1. 已修复的域名白名单配置

我已经更新了环境配置，添加了常用的本地开发端口：

```typescript
// src/lib/config/environment.ts - 开发环境配置
allowedEmbedDomains: [
  'https://dev.goglobalsp.com',
  'https://www.goglobalsp.com',
  'http://localhost:3000',
  'http://localhost:3001',
  'http://localhost:3002',
  'http://localhost:8080',
  'http://localhost:8081',
  'http://localhost:9000',
  'http://localhost:9001',
  'http://localhost:10000',
  'http://localhost:10001', // ✅ 您的Vue3项目端口
  'http://127.0.0.1:3000',
  'http://127.0.0.1:3001',
  'http://127.0.0.1:3002',
  'http://127.0.0.1:8080',
  'http://127.0.0.1:8081',
  'http://127.0.0.1:9000',
  'http://127.0.0.1:9001',
  'http://127.0.0.1:10000',
  'http://127.0.0.1:10001'  // ✅ 您的Vue3项目端口
]
```

### 2. Vue3 集成示例

创建一个完整的Vue3嵌入组件：

```vue
<template>
  <div class="embed-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <p>正在加载页面...</p>
      </div>
    </div>
    
    <!-- 错误状态 -->
    <div v-if="error" class="error-overlay">
      <div class="error-content">
        <h3>❌ 加载失败</h3>
        <p>{{ error }}</p>
        <div class="error-actions">
          <button @click="retry" class="retry-btn">🔄 重试</button>
          <button @click="checkStatus" class="check-btn">🔍 检查状态</button>
        </div>
      </div>
    </div>
    
    <!-- iframe -->
    <iframe
      v-show="!loading && !error"
      ref="embedIframe"
      :src="embedUrl"
      frameborder="0"
      width="100%"
      :height="iframeHeight"
      sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
      @load="onIframeLoad"
      @error="onIframeError"
      class="embed-iframe"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  page: {
    type: String,
    default: 'dashboard',
    validator: (value) => ['dashboard', 'reports', 'workspace', 'test'].includes(value)
  },
  token: {
    type: String,
    required: true
  },
  environment: {
    type: String,
    default: 'dev',
    validator: (value) => ['dev', 'prod'].includes(value)
  },
  height: {
    type: [String, Number],
    default: '600px'
  }
})

const emit = defineEmits(['load', 'error', 'message'])

// 响应式数据
const embedIframe = ref(null)
const loading = ref(true)
const error = ref(null)
const iframeHeight = ref(props.height)

// 计算嵌入URL
const embedUrl = computed(() => {
  const baseUrls = {
    dev: 'https://dev.goglobalsp.com',
    prod: 'https://www.goglobalsp.com'
  }
  
  const baseUrl = baseUrls[props.environment]
  const params = new URLSearchParams({
    token: props.token,
    debug: 'true',
    timestamp: Date.now().toString()
  })
  
  return `${baseUrl}/embed/${props.page}?${params.toString()}`
})

// iframe 加载完成
const onIframeLoad = () => {
  console.log('✅ iframe 加载成功')
  loading.value = false
  error.value = null
  emit('load')
}

// iframe 加载错误
const onIframeError = (event) => {
  console.error('❌ iframe 加载失败:', event)
  loading.value = false
  error.value = '页面加载失败，请检查网络连接或Token是否有效'
  emit('error', event)
}

// 重试加载
const retry = () => {
  console.log('🔄 重试加载iframe')
  loading.value = true
  error.value = null
  
  if (embedIframe.value) {
    const currentSrc = embedIframe.value.src
    embedIframe.value.src = ''
    setTimeout(() => {
      embedIframe.value.src = currentSrc
    }, 100)
  }
}

// 检查系统状态
const checkStatus = async () => {
  try {
    console.log('🔍 检查嵌入系统状态...')
    const response = await fetch('https://dev.goglobalsp.com/api/embed/health')
    const data = await response.json()
    console.log('✅ 系统状态:', data)
    alert(`系统状态: ${data.status}\n版本: ${data.version}\n环境: ${data.environment}`)
  } catch (err) {
    console.error('❌ 无法连接到嵌入系统:', err)
    alert('无法连接到嵌入系统，请检查网络连接')
  }
}

// 监听iframe消息
const handleMessage = (event) => {
  // 验证消息来源
  const allowedOrigins = [
    'https://dev.goglobalsp.com',
    'https://www.goglobalsp.com'
  ]
  
  if (!allowedOrigins.includes(event.origin)) {
    return
  }
  
  try {
    const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data
    console.log('📨 收到iframe消息:', data)
    
    // 处理高度调整消息
    if (data.type === 'resize' && data.height) {
      iframeHeight.value = `${data.height}px`
      console.log(`📏 调整iframe高度: ${data.height}px`)
    }
    
    // 转发消息给父组件
    emit('message', data)
  } catch (error) {
    console.warn('⚠️ 解析iframe消息失败:', error)
  }
}

// 生命周期
onMounted(() => {
  window.addEventListener('message', handleMessage)
  console.log('🚀 Vue3嵌入组件已挂载')
  console.log('🔗 嵌入URL:', embedUrl.value)
})

onUnmounted(() => {
  window.removeEventListener('message', handleMessage)
  console.log('🔚 Vue3嵌入组件已卸载')
})
</script>

<style scoped>
.embed-container {
  position: relative;
  width: 100%;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
  background: #ffffff;
  min-height: 400px;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  z-index: 10;
}

.loading-spinner {
  text-align: center;
  padding: 2rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-content {
  text-align: center;
  padding: 2rem;
  max-width: 400px;
}

.error-content h3 {
  color: #e74c3c;
  margin-bottom: 1rem;
}

.error-content p {
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.retry-btn,
.check-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.retry-btn {
  background: #3498db;
  color: white;
}

.retry-btn:hover {
  background: #2980b9;
}

.check-btn {
  background: #95a5a6;
  color: white;
}

.check-btn:hover {
  background: #7f8c8d;
}

.embed-iframe {
  width: 100%;
  border: none;
  display: block;
}
</style>
```

### 3. 使用示例

```vue
<template>
  <div class="dashboard-page">
    <h1>仪表板</h1>
    
    <!-- 嵌入Next.js仪表板 -->
    <EmbedFrame
      page="dashboard"
      :token="userToken"
      environment="dev"
      height="800px"
      @load="onEmbedLoad"
      @error="onEmbedError"
      @message="onEmbedMessage"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import EmbedFrame from '@/components/EmbedFrame.vue'

// 用户token（从您的认证系统获取）
const userToken = ref('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...')

const onEmbedLoad = () => {
  console.log('✅ 嵌入页面加载完成')
}

const onEmbedError = (error) => {
  console.error('❌ 嵌入页面加载失败:', error)
}

const onEmbedMessage = (data) => {
  console.log('📨 收到嵌入页面消息:', data)
}
</script>
```

## 🔧 故障排除

### 1. 检查当前域名

在浏览器控制台中运行：
```javascript
console.log('当前域名:', window.location.origin)
// 应该显示: http://localhost:10001
```

### 2. 检查Token有效性

```javascript
function checkToken(token) {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const exp = payload.exp * 1000
    const now = Date.now()
    
    console.log('Token过期时间:', new Date(exp))
    console.log('当前时间:', new Date(now))
    console.log('Token是否有效:', exp > now)
    
    return exp > now
  } catch (error) {
    console.error('Token解析失败:', error)
    return false
  }
}

// 使用您的token测试
checkToken('your-token-here')
```

### 3. 测试嵌入系统连接

```javascript
async function testConnection() {
  try {
    const response = await fetch('https://dev.goglobalsp.com/api/embed/health')
    const data = await response.json()
    console.log('✅ 嵌入系统状态:', data)
  } catch (error) {
    console.error('❌ 无法连接嵌入系统:', error)
  }
}

testConnection()
```

## 🎯 验证修复

修复后，您应该能够：

1. ✅ 在Vue3项目 (`http://localhost:10001`) 中成功加载iframe
2. ✅ 看到嵌入页面正常显示
3. ✅ 控制台没有跨域错误
4. ✅ iframe与父页面正常通信

## 📋 最佳实践

### 1. 错误处理
- 添加加载状态和错误提示
- 提供重试机制
- 记录详细的调试信息

### 2. 安全考虑
- 验证iframe消息来源
- 使用适当的sandbox属性
- 定期检查Token有效性

### 3. 性能优化
- 实现iframe预加载
- 监控加载性能
- 使用懒加载避免影响页面初始加载

---

## 🎉 总结

通过更新域名白名单配置，添加了 `http://localhost:10001` 端口支持，您现在应该能够在Vue3项目中成功嵌入Next.js页面了！

**关键修复**：
- ✅ 添加了端口10001到域名白名单
- ✅ 提供了完整的Vue3集成示例
- ✅ 包含了错误处理和调试功能
- ✅ 添加了故障排除指南

如果仍有问题，请检查Token是否有效，网络连接是否正常，或查看浏览器控制台的错误信息。
