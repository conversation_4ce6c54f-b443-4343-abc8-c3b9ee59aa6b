"use client"

import * as React from "react"
import {
  BarChart3,
  Bot,
  Building2,
  // FileText,
  Home,
  MessageSquare,
  PieChart,
  Settings,
  // ShoppingCart,
  TrendingUp,
  // Briefcase,
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavProjects } from "@/components/nav-projects"
import { NavUser } from "@/components/nav-user"
import { TeamSwitcher } from "@/components/team-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"
import { useAuth } from "@/hooks/use-auth-selectors"

// 菜单图标映射 - 🔧 已清理，仅保留实际存在的页面
const menuIcons = {
  chat: MessageSquare, // Chat页面（根路径）
  ui_gallery: Settings, // UI组件展示页面
  // 🚫 已删除不存在的页面图标映射
}

// 功能图标映射
const featureIcons = {
  risk_monitoring_intelligence_officer: TrendingUp,
  overseas_market_intelligence_officer: <PERSON><PERSON><PERSON>,
  business_opportunity_intelligence_officer: MessageSquare,
  strategic: BarChart3,
}


export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user, isAuthenticated } = useAuth()

  // 如果用户未登录，显示默认侧边栏
  if (!isAuthenticated || !user) {
    return (
      <Sidebar collapsible="icon" {...props}>
        <SidebarHeader>
          <TeamSwitcher teams={[]} />
        </SidebarHeader>
        <SidebarContent>
          <NavMain items={[]} />
          <NavProjects projects={[]} />
        </SidebarContent>
        <SidebarFooter>
          <NavUser user={{ name: "Guest", email: "", avatar: "" }} />
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
    )
  }

  // 构建团队数据
  const teams = [
    {
      name: user.company.name,
      logo: Building2,
      plan: user.company.config.plan,
    },
  ]

  // 构建主导航菜单
  const navMain = Object.entries(user.company.menu)
    .filter(([_, enabled]) => enabled)
    .map(([key, _]) => {
      const menuKey = key as keyof typeof menuIcons
      return {
        title: getMenuTitle(menuKey),
        url: getMenuUrl(menuKey),
        icon: menuIcons[menuKey] || Home,
        isActive: false,
        items: getMenuItems(menuKey),
      }
    })

  // 构建项目/功能列表
  const projects = Object.entries(user.company.feature)
    .filter(([_, enabled]) => enabled)
    .map(([key, _]) => {
      const featureKey = key as keyof typeof featureIcons
      return {
        name: getFeatureTitle(featureKey),
        url: getFeatureUrl(featureKey),
        icon: featureIcons[featureKey] || Bot,
      }
    })

  // 用户数据
  const userData = {
    name: user.user_name,
    email: user.email,
    avatar: user.avatar || "",
  }

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navMain} />
        <NavProjects projects={projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={userData} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

// 辅助函数：获取菜单标题 - 🔧 已清理
function getMenuTitle(key: string): string {
  const titles: Record<string, string> = {
    chat: "Chat",
    ui_gallery: "UI Gallery",
    // 🚫 已删除不存在页面的标题
  }
  return titles[key] || key
}

// 辅助函数：获取菜单URL - 🔧 已清理
function getMenuUrl(key: string): string {
  const urls: Record<string, string> = {
    chat: "/zh", // 指向国际化的chat页面
    ui_gallery: "/ui-gallery", // ui-gallery不需要国际化
    // 🚫 已删除不存在页面的URL
  }
  return urls[key] || "#"
}

// 辅助函数：获取菜单子项 - 🔧 已清理
function getMenuItems(key: string): Array<{ title: string; url: string }> {
  const items: Record<string, Array<{ title: string; url: string }>> = {
    chat: [
      { title: "Chat Interface", url: "/" },
    ],
    ui_gallery: [
      { title: "Component Gallery", url: "/ui-gallery" },
    ],
    // 🚫 已删除不存在页面的子项
  }
  return items[key] || []
}

// 辅助函数：获取功能标题
function getFeatureTitle(key: string): string {
  const titles: Record<string, string> = {
    risk_monitoring_intelligence_officer: "风险监控",
    overseas_market_intelligence_officer: "海外市场",
    business_opportunity_intelligence_officer: "商机分析",
    strategic: "战略分析",
  }
  return titles[key] || key
}

// 辅助函数：获取功能URL - 🔧 已清理，功能页面不存在，统一指向根页面
function getFeatureUrl(_key: string): string {
  // 🚫 所有功能页面都不存在，统一重定向到Chat页面
  return "/"
}
