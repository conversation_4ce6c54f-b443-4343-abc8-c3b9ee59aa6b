/**
 * API代理路由处理器
 * 处理所有API请求并转发到后端服务器
 */

import { NextRequest, NextResponse } from 'next/server'

// 配置路由段 - 强制动态渲染，因为需要处理实时请求
export const dynamic = 'force-dynamic'

// 🔧 修复API基础URL配置，使用支持的证书域名
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://dev.goglobalsp.com'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ proxy: string[] }> }
) {
  return handleRequest(request, params, 'GET')
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ proxy: string[] }> }
) {
  return handleRequest(request, params, 'POST')
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ proxy: string[] }> }
) {
  return handleRequest(request, params, 'PUT')
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ proxy: string[] }> }
) {
  return handleRequest(request, params, 'DELETE')
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ proxy: string[] }> }
) {
  return handleRequest(request, params, 'PATCH')
}

async function handleRequest(
  request: NextRequest,
  params: Promise<{ proxy: string[] }>,
  method: string
) {
  try {
    const { proxy } = await params
    const path = proxy.join('/')
    const url = `${API_BASE_URL}/api/${path}`

    console.log(`[API Proxy] ${method} ${request.url} -> ${url}`)
    console.log(`[API Proxy] Proxy path:`, proxy)
    
    // 获取查询参数
    const searchParams = request.nextUrl.searchParams
    const queryString = searchParams.toString()
    const finalUrl = queryString ? `${url}?${queryString}` : url

    // 准备请求头
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    // 转发原始请求头
    const authHeader = request.headers.get('authorization')
    const languageHeader = request.headers.get('language')
    
    if (authHeader) {
      headers['authorization'] = authHeader
    }
    
    if (languageHeader) {
      headers['language'] = languageHeader
    }

    // 准备请求体
    let body: string | undefined
    if (method !== 'GET' && method !== 'DELETE') {
      try {
        const requestBody = await request.text()
        if (requestBody) {
          body = requestBody
        }
      } catch (error) {
        console.error('Error reading request body:', error)
      }
    }

    // 发送请求到后端API
    const fetchOptions: RequestInit = {
      method,
      headers,
    }

    // 🔧 开发环境SSL处理
    if (process.env.NODE_ENV === 'development') {
      // 在开发环境中，如果遇到自签名证书或证书问题，可以添加处理
      // 注意：生产环境不应该跳过SSL验证
      (fetchOptions as any).agent = undefined;
    }

    if (body !== undefined) {
      fetchOptions.body = body
    }

    const response = await fetch(finalUrl, fetchOptions)

    // 获取响应数据
    const responseData = await response.text()
    
    // 创建响应
    const nextResponse = new NextResponse(responseData, {
      status: response.status,
      statusText: response.statusText,
    })

    // 转发响应头
    response.headers.forEach((value, key) => {
      // 跳过一些不需要转发的头
      if (!['content-encoding', 'content-length', 'transfer-encoding'].includes(key.toLowerCase())) {
        nextResponse.headers.set(key, value)
      }
    })

    // 设置CORS头
    nextResponse.headers.set('Access-Control-Allow-Origin', '*')
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH')
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, language')

    return nextResponse
  } catch (error) {
    console.error('API proxy error:', error)
    
    // 🔧 改进错误处理，为SSL证书错误提供友好提示
    let errorMessage = 'Internal server error'
    if (error instanceof Error) {
      if (error.message.includes('certificate') || error.message.includes('CERT_') || error.message.includes('TLS')) {
        errorMessage = 'API certificate error - please check configuration'
      }
      errorMessage = error.message
    }
    
    return NextResponse.json(
      { 
        success: false, 
        message: errorMessage,
        error: error instanceof Error ? error.message : 'Unknown error',
        // 🔧 开发环境提供更多调试信息
        ...(process.env.NODE_ENV === 'development' && {
          debug: {
            targetUrl: API_BASE_URL,
            timestamp: new Date().toISOString()
          }
        })
      },
      { status: 500 }
    )
  }
}

// 处理OPTIONS请求（CORS预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS, PATCH',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, language',
      'Access-Control-Max-Age': '86400',
    },
  })
}
