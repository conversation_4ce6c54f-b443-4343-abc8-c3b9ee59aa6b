# iframe 嵌入系统安全部署指南

## 📋 部署概述

本指南提供了一个高安全性的 Next.js iframe 嵌入系统部署方案，**只暴露 `/embed/[page]` 路径给外部服务访问**，所有其他页面和API都被严格保护。

## 🏗️ 安全架构设计

```
外部访问 (iframe嵌入)
    ↓
nginx (443/80) - 严格路径控制
    ↓
只允许访问: /embed/*
    ↓
Next.js Docker容器 (3001端口)
    ↓
内部网络隔离 - 其他服务不可外部访问
```

### 安全特性

- ✅ **路径隔离**: 只有 `/embed/*` 可被外部访问
- ✅ **域名白名单**: 限制允许嵌入的父域名
- ✅ **Token验证**: 必须携带有效token才能访问
- ✅ **内网隔离**: 其他路径只能内网访问
- ✅ **请求头验证**: 验证 Referer 和 Origin 头
- ✅ **防护机制**: 防止直接访问和恶意嵌入

## 🚀 详细部署步骤

### 步骤 1: 服务器环境准备

```bash
# 1. 连接到服务器
ssh ubuntu@your-server-ip

# 2. 更新系统包
sudo apt update && sudo apt upgrade -y

# 3. 安装必要的工具
sudo apt install -y curl wget git unzip nginx

# 4. 安装 Docker 和 Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker ubuntu

# 5. 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 6. 验证安装
docker --version
docker-compose --version
nginx -v
```

### 步骤 2: 项目部署目录设置

```bash
# 1. 创建项目目录
sudo mkdir -p /home/<USER>/vibe-nextjs-app
sudo chown -R ubuntu:ubuntu /home/<USER>/vibe-nextjs-app

# 2. 切换到项目目录
cd /home/<USER>/vibe-nextjs-app

# 3. 从本地上传项目文件 (在本地机器执行)
# 压缩项目文件 (排除不必要的文件)
tar -czf vibe-nextjs-app.tar.gz \
  --exclude='node_modules' \
  --exclude='.next' \
  --exclude='.git' \
  --exclude='*.log' \
  .

# 上传到服务器
scp vibe-nextjs-app.tar.gz ubuntu@your-server-ip:/home/<USER>/vibe-nextjs-app/

# 4. 在服务器上解压
cd /home/<USER>/vibe-nextjs-app
tar -xzf vibe-nextjs-app.tar.gz
rm vibe-nextjs-app.tar.gz
```

### 步骤 3: 安全配置文件创建

#### 3.1 创建生产环境配置

```bash
# 创建环境变量文件
cat > .env.production << 'EOF'
NODE_ENV=production
NEXT_PUBLIC_APP_ENV=production
NEXT_PUBLIC_BASE_URL=https://dev.goglobalsp.com
NEXT_PUBLIC_EMBED_ENABLED=true
NEXT_PUBLIC_DEBUG=false

# iframe 安全配置
ALLOWED_EMBED_DOMAINS=https://www.goglobalsp.com,https://dev.goglobalsp.com
EMBED_TOKEN_SECRET=your-super-secret-token-key-here
EMBED_SECURITY_ENABLED=true

# 内部服务配置 (不对外暴露)
INTERNAL_API_BASE=http://localhost:8136
INTERNAL_SOCKET_URL=http://localhost:8137
EOF
```

#### 3.2 更新 Docker Compose 配置

```bash
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  vibe-nextjs:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nextjs-web-ui
    restart: unless-stopped
    ports:
      - "127.0.0.1:3001:3000"  # 只绑定到localhost
    env_file:
      - .env.production
    networks:
      - embed-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
    # 安全限制
    security_opt:
      - no-new-privileges:true
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

networks:
  embed-network:
    driver: bridge
    internal: false  # 允许外部访问，但通过nginx控制
EOF
```

### 步骤 4: nginx 完整安全配置

#### 4.1 创建主nginx配置

```bash
# 备份原始配置
sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup

# 创建优化的主配置
sudo cat > /etc/nginx/nginx.conf << 'EOF'
user www-data;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # 基础设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # MIME类型
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format detailed '$remote_addr - $remote_user [$time_local] '
                       '"$request" $status $body_bytes_sent '
                       '"$http_referer" "$http_user_agent" '
                       '"$http_x_forwarded_for" "$http_origin"';

    # 日志配置
    access_log /var/log/nginx/access.log detailed;
    error_log /var/log/nginx/error.log warn;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # 安全头部
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 限制请求大小
    client_max_body_size 10M;
    client_body_buffer_size 128k;

    # 包含站点配置
    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}
EOF
```

#### 4.2 创建 iframe 专用站点配置

```bash
# 删除默认配置
sudo rm -f /etc/nginx/sites-enabled/default

# 创建 iframe 专用配置
sudo cat > /etc/nginx/sites-available/iframe-embed << 'EOF'
# iframe 嵌入系统配置 - 只允许访问 /embed/* 路径

# 限制访问的 map 配置
map $request_uri $allowed_embed_path {
    ~^/embed/.*$ 1;
    ~^/api/health$ 1;  # 健康检查
    default 0;
}

# 上游服务器配置
upstream nextjs_embed {
    server 127.0.0.1:3001;
    keepalive 32;
}

# HTTPS 服务器配置
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name dev.specific-ai.com;

    # SSL 配置 (假设您已有SSL证书)
    ssl_certificate /etc/ssl/certs/dev.specific-ai.com.crt;
    ssl_certificate_key /etc/ssl/private/dev.specific-ai.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 安全头部
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;  # 默认拒绝嵌入
    add_header X-XSS-Protection "1; mode=block" always;

    # 日志配置
    access_log /var/log/nginx/iframe-embed-access.log detailed;
    error_log /var/log/nginx/iframe-embed-error.log;

    # 只允许访问 /embed/* 路径的安全控制
    location / {
        # 检查是否为允许的路径
        if ($allowed_embed_path = 0) {
            return 403 "Access denied. Only /embed/* paths are allowed.";
        }

        # 对于 /embed/* 路径的特殊处理
        location ~* ^/embed/ {
            # 验证 Referer 头部 (确保来自允许的域名)
            if ($http_referer !~* "^https://(www\.goglobalsp\.com|dev\.specific-ai\.com)") {
                return 403 "Invalid referer domain";
            }

            # 允许 iframe 嵌入的特殊头部
            add_header X-Frame-Options "ALLOW-FROM $http_referer" always;
            add_header Content-Security-Policy "frame-ancestors 'self' https://www.goglobalsp.com https://dev.goglobalsp.com" always;
            
            # CORS 头部
            add_header Access-Control-Allow-Origin "$http_origin" always;
            add_header Access-Control-Allow-Credentials "true" always;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Authorization, Content-Type, X-Requested-With" always;

            # 处理 OPTIONS 预检请求
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                return 204;
            }

            # 代理到 Next.js 应用
            proxy_pass http://nextjs_embed;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_cache_bypass $http_upgrade;

            # 超时设置
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # 健康检查端点 (内部使用)
        location = /api/health {
            # 只允许内网访问
            allow 127.0.0.1;
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;

            proxy_pass http://nextjs_embed;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # 拒绝所有其他路径
    location ~* ^/(api|dashboard|ui-gallery|_next|static)/ {
        return 403 "Access to this path is not allowed from external sources";
    }

    # 特殊文件拒绝
    location ~* \.(env|config|backup|sql|log)$ {
        deny all;
        return 404;
    }

    # 隐藏版本信息
    location ~* /\. {
        deny all;
        return 404;
    }
}

# HTTP 重定向到 HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name dev.specific-ai.com;
    return 301 https://$server_name$request_uri;
}

# 拒绝未知域名的访问
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    listen 443 ssl default_server;
    listen [::]:443 ssl default_server;
    
    ssl_certificate /etc/ssl/certs/dev.specific-ai.com.crt;
    ssl_certificate_key /etc/ssl/private/dev.specific-ai.com.key;
    
    server_name _;
    return 444;  # nginx 特殊代码，直接关闭连接
}
EOF
```

#### 4.3 启用配置并测试

```bash
# 创建符号链接启用配置
sudo ln -s /etc/nginx/sites-available/iframe-embed /etc/nginx/sites-enabled/

# 测试 nginx 配置
sudo nginx -t

# 如果测试通过，重新加载配置
sudo systemctl reload nginx

# 启用开机自启
sudo systemctl enable nginx
```

### 步骤 5: 防火墙和安全设置

```bash
# 1. 配置 UFW 防火墙
sudo ufw --force reset
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 只开放必要端口
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 启用防火墙
sudo ufw --force enable
sudo ufw status

# 2. 配置 fail2ban (防止暴力破解)
sudo apt install -y fail2ban

sudo cat > /etc/fail2ban/jail.local << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[nginx-http-auth]
enabled = true
port = http,https
logpath = /var/log/nginx/iframe-embed-error.log

[nginx-limit-req]
enabled = true
port = http,https
logpath = /var/log/nginx/iframe-embed-error.log
maxretry = 2
EOF

sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

### 步骤 6: 构建和启动应用

#### 方式一：使用快速部署脚本 (推荐)

```bash
# 1. 进入项目目录
cd /home/<USER>/vibe-nextjs-app

# 2. 快速部署到生产环境
./quick_deploy.sh --pd

# 或部署到开发环境
./quick_deploy.sh --dev
```

#### 方式二：分步骤部署

```bash
# 1. 构建和推送镜像到私有仓库
./build_push_image.sh --pd

# 2. 部署服务
./deploy.sh --pd

# 3. 查看容器状态
docker ps
docker logs -f nextjs-web-ui-pd
```

#### 方式三：使用Docker Compose (传统方式)

```bash
# 1. 构建 Docker 镜像
cd /home/<USER>/vibe-nextjs-app
docker-compose build --no-cache

# 2. 启动服务
docker-compose up -d

# 3. 检查服务状态
docker-compose ps
docker-compose logs -f vibe-nextjs

# 4. 验证健康检查
curl -f http://localhost:3001/api/health
```

### 步骤 7: SSL 证书配置 (如果还没有)

```bash
# 使用 Let's Encrypt 自动获取SSL证书
sudo apt install -y certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d dev.specific-ai.com

# 设置自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

## 🔍 部署验证和测试

### 验证步骤

```bash
# 1. 检查端口监听
sudo netstat -tlnp | grep :443
sudo netstat -tlnp | grep :3001

# 2. 检查 Docker 容器状态
docker ps
docker-compose logs vibe-nextjs

# 3. 检查 nginx 状态
sudo systemctl status nginx
sudo nginx -t

# 4. 测试健康检查
curl -f https://dev.goglobalsp.com/api/health

# 5. 测试路径访问控制
# 应该成功 (带正确的 Referer)
curl -H "Referer: https://www.goglobalsp.com" https://dev.goglobalsp.com/embed/dashboard

# 应该被拒绝
curl https://dev.goglobalsp.com/dashboard
curl https://dev.goglobalsp.com/ui-gallery
```

### 测试 iframe 嵌入

创建测试文件 `/tmp/iframe-test.html`：

```html
<!DOCTYPE html>
<html>
<head>
    <title>iframe 嵌入测试</title>
</head>
<body>
    <h1>iframe 嵌入测试</h1>
    <iframe 
        src="https://dev.goglobalsp.com/embed/dashboard?token=test-token"
        width="100%" 
        height="600"
        frameborder="0">
    </iframe>
</body>
</html>
```

## 📊 监控和维护

### 日志监控

```bash
# 1. 实时查看访问日志
sudo tail -f /var/log/nginx/iframe-embed-access.log

# 2. 查看错误日志
sudo tail -f /var/log/nginx/iframe-embed-error.log

# 3. 查看应用日志
docker-compose logs -f vibe-nextjs

# 4. 系统资源监控
htop
docker stats nextjs-web-ui
```

### 性能监控脚本

```bash
# 创建监控脚本
cat > /home/<USER>/monitor-embed.sh << 'EOF'
#!/bin/bash

echo "=== iframe 嵌入系统状态监控 $(date) ==="

echo "Docker 容器状态:"
docker-compose ps

echo "nginx 状态:"
sudo systemctl status nginx --no-pager

echo "内存使用:"
free -h

echo "磁盘使用:"
df -h

echo "网络连接:"
sudo netstat -tlnp | grep -E ':(80|443|3001)'

echo "最近的错误日志 (最后10行):"
sudo tail -10 /var/log/nginx/iframe-embed-error.log

echo "============================================"
EOF

chmod +x /home/<USER>/monitor-embed.sh

# 设置定时监控 (每小时执行一次)
echo "0 * * * * /home/<USER>/monitor-embed.sh >> /home/<USER>/monitor.log 2>&1" | crontab -
```

## 🔄 更新和维护

### 应用更新流程

```bash
# 1. 备份当前版本
cd /home/<USER>/vibe-nextjs-app
docker-compose down
tar -czf backup-$(date +%Y%m%d-%H%M).tar.gz .

# 2. 更新代码 (从本地上传新版本)
# ... 上传新代码 ...

# 3. 重新构建和部署
docker-compose build --no-cache
docker-compose up -d

# 4. 验证部署
curl -f https://dev.goglobalsp.com/api/health
```

### 清理和优化

```bash
# 清理旧的 Docker 镜像
docker image prune -f
docker system prune -f

# 清理日志文件 (保留最近7天)
sudo find /var/log/nginx/ -name "*.log" -type f -mtime +7 -delete

# nginx 配置优化测试
sudo nginx -t && sudo nginx -s reload
```

## 🚨 故障排除

### 常见问题和解决方案

#### 1. iframe 无法加载

**问题**: iframe 显示空白或错误

**诊断**:

```bash
# 检查 nginx 错误日志
sudo tail -50 /var/log/nginx/iframe-embed-error.log

# 检查应用日志
docker-compose logs vibe-nextjs

# 测试直接访问
curl -I https://dev.goglobalsp.com/embed/dashboard
```

**解决方案**:

- 检查 Referer 头部是否正确
- 验证域名是否在白名单中
- 确认 SSL 证书有效

#### 2. 403 拒绝访问

**问题**: 返回 403 错误

**诊断**:

```bash
# 检查访问的路径
grep "403" /var/log/nginx/iframe-embed-access.log | tail -10

# 检查 nginx 配置
sudo nginx -t
```

**解决方案**:

- 确认访问路径是 `/embed/*`
- 检查 Referer 头部
- 验证防火墙设置

#### 3. 容器启动失败

**问题**: Docker 容器无法启动

**诊断**:

```bash
# 查看容器状态
docker-compose ps
docker-compose logs vibe-nextjs

# 检查端口占用
sudo netstat -tlnp | grep 3001
```

**解决方案**:

- 检查端口冲突
- 验证环境变量
- 查看构建日志

## 📋 安全检查清单

部署完成后，请确认以下安全措施：

- [ ] 只有 `/embed/*` 路径可外部访问
- [ ] 所有其他路径返回 403 错误
- [ ] Referer 验证正常工作
- [ ] SSL 证书有效且自动续期
- [ ] 防火墙只开放必要端口
- [ ] fail2ban 正常运行
- [ ] Docker 容器资源限制生效
- [ ] 日志监控和轮转配置正确
- [ ] 健康检查端点只能内网访问

## 📞 技术支持

如果遇到问题，请提供以下信息：

1. 错误信息和时间
2. nginx 错误日志
3. Docker 容器日志
4. 系统环境信息 (`uname -a`)
5. 网络配置 (`ip addr show`)

## 🚀 快速部署脚本使用指南

项目根目录包含三个部署脚本，简化了整个部署流程：

### 1. build_push_image.sh - 构建推送脚本

**功能**: 构建Docker镜像并推送到私有仓库

```bash
# 使用方法
./build_push_image.sh [--dev|--pd]

# 示例
./build_push_image.sh --pd    # 生产环境
./build_push_image.sh --dev   # 开发环境
```

**执行流程**:

1. 删除旧的本地镜像
2. 使用Dockerfile构建新镜像
3. 标记镜像为私有仓库格式
4. 推送到私有Docker仓库 (*************:5000)
5. 清理本地镜像

### 2. deploy.sh - 部署脚本

**功能**: 从私有仓库拉取镜像并部署服务

```bash
# 使用方法
./deploy.sh [--dev|--pd]

# 示例
./deploy.sh --pd    # 部署生产环境
./deploy.sh --dev   # 部署开发环境
```

**执行流程**:

1. 停止并删除现有容器
2. 从私有仓库拉取最新镜像
3. 配置环境变量
4. 启动新容器
5. 健康检查验证

### 3. quick_deploy.sh - 一键部署脚本 ⭐

**功能**: 整合构建、推送、部署全流程

```bash
# 使用方法
./quick_deploy.sh [--dev|--pd]

# 示例
./quick_deploy.sh --pd    # 一键部署生产环境
./quick_deploy.sh --dev   # 一键部署开发环境
```

**执行流程**:

1. 调用 build_push_image.sh 构建推送镜像
2. 调用 deploy.sh 部署服务
3. 检查容器状态和健康
4. 提供实时日志查看选项

### 环境配置对比

| 配置项 | 开发环境 (--dev) | 生产环境 (--pd) |
|--------|------------------|-----------------|
| DEBUG模式 | 启用 | 禁用 |
| 容器名称 | nextjs-web-ui-dev | nextjs-web-ui-pd |
| 端口绑定 | 127.0.0.1:3001:3000 | 127.0.0.1:3001:3000 |
| 环境变量 | NEXT_PUBLIC_DEBUG=true | NEXT_PUBLIC_DEBUG=false |
| 重启策略 | unless-stopped | unless-stopped |

### 快速上手

**新部署 (推荐)**:

```bash
# 1. 上传项目文件到服务器
scp -r . ubuntu@server:/home/<USER>/vibe-nextjs-app/

# 2. SSH到服务器
ssh ubuntu@server

# 3. 进入项目目录
cd /home/<USER>/vibe-nextjs-app

# 4. 一键部署
./quick_deploy.sh --pd
```

**更新部署**:

```bash
# 更新代码后快速重新部署
./quick_deploy.sh --pd
```

### 故障排除

**权限问题**:

```bash
# 设置脚本执行权限
chmod +x *.sh
```

**镜像推送失败**:

```bash
# 检查私有仓库连接
docker pull *************:5000/hello-world

# 手动推送测试
docker push *************:5000/nextjs-web-ui:latest
```

**容器启动失败**:

```bash
# 查看详细日志
docker logs nextjs-web-ui-pd

# 检查端口占用
netstat -tlnp | grep 3001
```

---

**部署完成！** 🎉

您的 iframe 嵌入系统现在已经安全部署，只有 `/embed/*` 路径可以被外部访问，为您的应用提供了最高级别的安全保护。
