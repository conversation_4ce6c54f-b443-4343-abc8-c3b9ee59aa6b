import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

export interface AppConfig {
  apiBaseUrl: string
  version: string
  environment: 'development' | 'staging' | 'production'
  features: Record<string, boolean>
}

export interface AppState {
  // 应用配置
  config: AppConfig

  // 应用状态
  isInitialized: boolean
  isOnline: boolean

  // 用户偏好
  preferences: {
    language: string
    timezone: string
    dateFormat: string
    theme: 'light' | 'dark' | 'system'
  }

  // 方法
  setConfig: (config: Partial<AppConfig>) => void
  setInitialized: (initialized: boolean) => void
  setOnlineStatus: (online: boolean) => void
  updatePreferences: (preferences: Partial<AppState['preferences']>) => void

  // 功能开关
  isFeatureEnabled: (feature: string) => boolean
  toggleFeature: (feature: string) => void
}

export const useAppStore = create<AppState>()(
  devtools(
    immer((set, get) => ({
        // 初始状态
        config: {
          apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || '',
          version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
          environment: (process.env.NODE_ENV as any) || 'development',
          features: {},
        },

        isInitialized: false,
        isOnline: true,

        preferences: {
          language: 'zh-CN',
          timezone: 'Asia/Shanghai',
          dateFormat: 'YYYY-MM-DD',
          theme: 'system',
        },

        // 方法实现
        setConfig: (config: Partial<AppConfig>) => set(state => {
          state.config = { ...state.config, ...config }
        }),

        setInitialized: (initialized: boolean) => set(state => {
          state.isInitialized = initialized
        }),

        setOnlineStatus: (online: boolean) => set(state => {
          state.isOnline = online
        }),

        updatePreferences: (preferences) => set(state => {
          state.preferences = { ...state.preferences, ...preferences }
        }),

        // 功能开关
        isFeatureEnabled: (feature: string) => {
          const { config } = get()
          return config.features[feature] ?? false
        },

        toggleFeature: (feature: string) => set(state => {
          state.config.features[feature] = !state.config.features[feature]
        }),
      })),
    { name: 'app-store' }
  )
)
