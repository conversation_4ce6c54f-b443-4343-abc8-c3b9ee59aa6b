#!/usr/bin/env node

/**
 * 构建时排除UI Gallery页面的脚本
 * 在生产构建时，如果设置了NEXT_PUBLIC_EXCLUDE_UI_GALLERY=true，
 * 则将ui-gallery页面重命名为.bak文件，构建完成后再恢复
 */

const fs = require('fs');
const path = require('path');

const UI_GALLERY_PATH = path.join(process.cwd(), 'src/app/ui-gallery');
const UI_GALLERY_BACKUP_PATH = path.join(process.cwd(), '.ui-gallery-backup');

function excludeUIGallery() {
  console.log('🚫 排除UI Gallery页面...');

  if (fs.existsSync(UI_GALLERY_PATH)) {
    // 如果备份已存在，先删除
    if (fs.existsSync(UI_GALLERY_BACKUP_PATH)) {
      fs.rmSync(UI_GALLERY_BACKUP_PATH, { recursive: true, force: true });
    }
    // 将ui-gallery目录重命名为备份
    fs.renameSync(UI_GALLERY_PATH, UI_GALLERY_BACKUP_PATH);
    console.log('✅ UI Gallery页面已排除');
  } else {
    console.log('⚠️ UI Gallery页面不存在，跳过排除');
  }
}

function restoreUIGallery() {
  if (fs.existsSync(UI_GALLERY_BACKUP_PATH)) {
    console.log('🔄 恢复UI Gallery页面...');

    // 如果目标目录已存在，先删除
    if (fs.existsSync(UI_GALLERY_PATH)) {
      fs.rmSync(UI_GALLERY_PATH, { recursive: true, force: true });
    }

    // 恢复备份
    fs.renameSync(UI_GALLERY_BACKUP_PATH, UI_GALLERY_PATH);
    console.log('✅ UI Gallery页面已恢复');
  } else {
    console.log('ℹ️ 没有找到UI Gallery备份，跳过恢复');
  }
}

// 根据命令行参数执行相应操作
const action = process.argv[2];

switch (action) {
  case 'exclude':
    excludeUIGallery();
    break;
  case 'restore':
    restoreUIGallery();
    break;
  default:
    console.log('用法: node exclude-ui-gallery.js [exclude|restore]');
    process.exit(1);
}
