"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { ChevronDown, Zap } from "lucide-react"
import { <PERSON>ton } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Separator } from "@/components/ui/separator"
import { useMarkdown } from "@/hooks/use-markdown"

// Mock streaming content chunks
const streamingChunks = [
  "# AI Market Analysis Report\n\n*Generated on December 20, 2024*\n\n---\n\nInitializing comprehensive market analysis...",

  "# AI Market Analysis Report\n\n*Generated on December 20, 2024*\n\n---\n\n## Executive Summary\n\nThe artificial intelligence market continues to experience unprecedented growth, with significant developments across multiple sectors. Our analysis reveals key trends that will shape the industry landscape in 2025.\n\n### Key Highlights\n\n- **Market Size**: $184.6B projected for 2024\n- **Growth Rate**: 28.7% CAGR through 2030\n- **Leading Sectors**: Healthcare, Finance, Automotive",

  "# AI Market Analysis Report\n\n*Generated on December 20, 2024*\n\n---\n\n## Executive Summary\n\nThe artificial intelligence market continues to experience unprecedented growth, with significant developments across multiple sectors. Our analysis reveals key trends that will shape the industry landscape in 2025.\n\n### Key Highlights\n\n- **Market Size**: $184.6B projected for 2024\n- **Growth Rate**: 28.7% CAGR through 2030\n- **Leading Sectors**: Healthcare, Finance, Automotive\n\n## Market Segmentation\n\n### By Technology\n\n::: info\n**Machine Learning** dominates with 65% market share, followed by Natural Language Processing at 18%.\n:::\n\n1. **Machine Learning**: $119.8B\n2. **Natural Language Processing**: $33.2B\n3. **Computer Vision**: $22.1B\n4. **Robotics**: $9.5B\n\n### Geographic Distribution\n\n- **North America**: 42% market share\n- **Asia-Pacific**: 31% market share\n- **Europe**: 21% market share\n- **Rest of World**: 6% market share",

  '# AI Market Analysis Report\n\n*Generated on December 20, 2024*\n\n---\n\n## Executive Summary\n\nThe artificial intelligence market continues to experience unprecedented growth, with significant developments across multiple sectors. Our analysis reveals key trends that will shape the industry landscape in 2025.\n\n### Key Highlights\n\n- **Market Size**: $184.6B projected for 2024\n- **Growth Rate**: 28.7% CAGR through 2030\n- **Leading Sectors**: Healthcare, Finance, Automotive\n\n## Market Segmentation\n\n### By Technology\n\n::: info\n**Machine Learning** dominates with 65% market share, followed by Natural Language Processing at 18%.\n:::\n\n1. **Machine Learning**: $119.8B\n2. **Natural Language Processing**: $33.2B\n3. **Computer Vision**: $22.1B\n4. **Robotics**: $9.5B\n\n### Geographic Distribution\n\n- **North America**: 42% market share\n- **Asia-Pacific**: 31% market share\n- **Europe**: 21% market share\n- **Rest of World**: 6% market share\n\n## Industry Applications\n\n### Healthcare\n\n```python\n# AI in Healthcare - Key Metrics\ndiagnostic_accuracy = 0.94\ncost_reduction = 0.23\npatient_satisfaction = 0.87\n\nprint(f"Diagnostic accuracy improved by {diagnostic_accuracy*100}%")\nprint(f"Cost reduction: {cost_reduction*100}%")\n```\n\nAI applications in healthcare are revolutionizing patient care through:\n\n- **Medical Imaging**: 94% accuracy in diagnostic imaging\n- **Drug Discovery**: 40% reduction in development time\n- **Personalized Treatment**: Tailored therapy recommendations\n\n### Financial Services\n\n::: warning\nRegulatory compliance remains a critical consideration for AI implementation in financial services.\n:::\n\n- **Fraud Detection**: Real-time transaction monitoring\n- **Algorithmic Trading**: High-frequency decision making\n- **Risk Assessment**: Enhanced credit scoring models',

  '# AI Market Analysis Report\n\n*Generated on December 20, 2024*\n\n---\n\n## Executive Summary\n\nThe artificial intelligence market continues to experience unprecedented growth, with significant developments across multiple sectors. Our analysis reveals key trends that will shape the industry landscape in 2025.\n\n### Key Highlights\n\n- **Market Size**: $184.6B projected for 2024\n- **Growth Rate**: 28.7% CAGR through 2030\n- **Leading Sectors**: Healthcare, Finance, Automotive\n\n## Market Segmentation\n\n### By Technology\n\n::: info\n**Machine Learning** dominates with 65% market share, followed by Natural Language Processing at 18%.\n:::\n\n1. **Machine Learning**: $119.8B\n2. **Natural Language Processing**: $33.2B\n3. **Computer Vision**: $22.1B\n4. **Robotics**: $9.5B\n\n### Geographic Distribution\n\n- **North America**: 42% market share\n- **Asia-Pacific**: 31% market share\n- **Europe**: 21% market share\n- **Rest of World**: 6% market share\n\n## Industry Applications\n\n### Healthcare\n\n```python\n# AI in Healthcare - Key Metrics\ndiagnostic_accuracy = 0.94\ncost_reduction = 0.23\npatient_satisfaction = 0.87\n\nprint(f"Diagnostic accuracy improved by {diagnostic_accuracy*100}%")\nprint(f"Cost reduction: {cost_reduction*100}%")\n```\n\nAI applications in healthcare are revolutionizing patient care through:\n\n- **Medical Imaging**: 94% accuracy in diagnostic imaging\n- **Drug Discovery**: 40% reduction in development time\n- **Personalized Treatment**: Tailored therapy recommendations\n\n### Financial Services\n\n::: warning\nRegulatory compliance remains a critical consideration for AI implementation in financial services.\n:::\n\n- **Fraud Detection**: Real-time transaction monitoring\n- **Algorithmic Trading**: High-frequency decision making\n- **Risk Assessment**: Enhanced credit scoring models\n\n## Future Outlook\n\n### Emerging Trends\n\n1. **Generative AI Adoption**\n   - 73% of enterprises planning implementation by 2025\n   - Focus on content creation and automation\n\n2. **Edge AI Computing**\n   - Reduced latency for real-time applications\n   - Enhanced privacy and security\n\n3. **AI Governance Frameworks**\n   - Standardized ethical guidelines\n   - Regulatory compliance automation\n\n### Investment Landscape\n\n::: tip\nVenture capital investment in AI startups reached $25.2B in 2024, representing a 15% increase from the previous year.\n:::\n\n**Top Investment Areas:**\n- Autonomous systems: $8.1B\n- Healthcare AI: $6.3B\n- Enterprise automation: $4.8B\n- Cybersecurity AI: $3.2B\n\n## Recommendations\n\nBased on our comprehensive analysis, we recommend the following strategic actions:\n\n1. **Prioritize AI Integration**: Organizations should develop clear AI adoption roadmaps\n2. **Invest in Talent**: Build internal AI capabilities through training and recruitment\n3. **Focus on Ethics**: Implement responsible AI practices from the outset\n4. **Monitor Regulations**: Stay informed about evolving AI governance requirements\n\n---\n\n*Report completed. Analysis based on data from 500+ companies and 50+ industry sources.*',
]

export default function ReportPage() {
  const [content, setContent] = useState("")
  const [isStreaming, setIsStreaming] = useState(true)
  const [showNewContentNotice, setShowNewContentNotice] = useState(false)
  const [isAtBottom, setIsAtBottom] = useState(true)
  const [chunkIndex, setChunkIndex] = useState(0)

  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)

  const renderedHtml = useMarkdown(content)

  // Simulate streaming content
  useEffect(() => {
    if (chunkIndex < streamingChunks.length) {
      const timer = setTimeout(() => {
        setContent(streamingChunks[chunkIndex])
        setChunkIndex((prev) => prev + 1)

        if (chunkIndex === streamingChunks.length - 1) {
          setIsStreaming(false)
        }
      }, 2500) // Slower streaming for better demo

      return () => clearTimeout(timer)
    }
    // 当chunkIndex >= streamingChunks.length时，也需要返回一个函数
    return () => {}
  }, [chunkIndex])

  // Handle scroll detection
  const handleScroll = useCallback(() => {
    if (!scrollAreaRef.current) return

    const { scrollTop, scrollHeight, clientHeight } = scrollAreaRef.current
    const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100

    setIsAtBottom(isNearBottom)

    if (!isNearBottom && isStreaming && chunkIndex > 1) {
      setShowNewContentNotice(true)
    } else {
      setShowNewContentNotice(false)
    }
  }, [isStreaming, chunkIndex])

  // Auto-scroll when new content arrives
  useEffect(() => {
    if (isAtBottom && scrollAreaRef.current && chunkIndex > 0) {
      setTimeout(() => {
        if (scrollAreaRef.current) {
          scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
        }
      }, 100)
    }
  }, [content, isAtBottom, chunkIndex])

  const jumpToLatest = () => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
      setShowNewContentNotice(false)
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Top Bar */}
      <div className="sticky top-0 z-50 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex h-16 items-center px-6">
          {/* Logo */}
          <div className="flex items-center gap-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
              <Zap className="h-4 w-4 text-primary-foreground" />
            </div>
            <span className="font-semibold text-lg">AI Insights</span>
          </div>

          {/* Report Title */}
          <div className="flex-1 flex justify-center px-8">
            <div className="text-center">
              <h1 className="font-semibold text-lg">Market Analysis Report</h1>
            </div>
          </div>

          {/* Status Indicator */}
          <div className="flex items-center gap-3">
            {isStreaming ? (
              <Badge variant="secondary" className="animate-pulse">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
                Generating
              </Badge>
            ) : (
              <Badge variant="outline">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-2" />
                Complete
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Main Layout */}
      <div className="flex">
        {/* Main Content Area */}
        <div className="flex-1">
          <ScrollArea className="h-[calc(100vh-4rem)]" ref={scrollAreaRef} onScrollCapture={handleScroll}>
            <div className="max-w-4xl mx-auto p-6">
              <Card className="min-h-[600px]">
                <div className="p-8">
                  {content ? (
                    <div
                      ref={contentRef}
                      className="prose prose-slate dark:prose-invert max-w-none prose-headings:scroll-mt-20"
                      dangerouslySetInnerHTML={{ __html: renderedHtml }}
                    />
                  ) : (
                    <div className="space-y-6">
                      <Skeleton className="h-8 w-3/4" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-5/6" />
                      <Separator className="my-6" />
                      <Skeleton className="h-6 w-1/2" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-4/5" />
                        <Skeleton className="h-4 w-3/4" />
                      </div>
                    </div>
                  )}

                  {/* Streaming Indicator */}
                  {isStreaming && content && (
                    <div className="mt-8 flex items-center gap-3 text-muted-foreground">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.3s]" />
                        <div className="w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.15s]" />
                        <div className="w-2 h-2 bg-current rounded-full animate-bounce" />
                      </div>
                      <span className="text-sm">Analyzing data and generating insights...</span>
                    </div>
                  )}
                </div>
              </Card>
            </div>
          </ScrollArea>

          {/* New Content Notice */}
          {showNewContentNotice && (
            <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-40">
              <Button onClick={jumpToLatest} className="shadow-lg animate-in slide-in-from-bottom-2" size="sm">
                <ChevronDown className="h-4 w-4 mr-2" />
                View latest updates
              </Button>
            </div>
          )}
        </div>

        {/* Right Side Area - Reserved for Future Outline */}
        <div className="hidden lg:block w-80 border-l bg-muted/20">
          <div className="sticky top-16 h-[calc(100vh-4rem)] p-6">
            <div className="text-center text-muted-foreground">
              <div className="w-16 h-16 mx-auto mb-4 rounded-lg bg-muted flex items-center justify-center">
                <div className="w-8 h-8 rounded bg-muted-foreground/20" />
              </div>
              <p className="text-sm">Outline navigation</p>
              <p className="text-xs mt-1">Coming soon</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
