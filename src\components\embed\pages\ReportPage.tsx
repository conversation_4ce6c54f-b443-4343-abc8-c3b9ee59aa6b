"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { ChevronDown, Zap } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Separator } from "@/components/ui/separator"
import { useMarkdown } from "@/hooks/use-markdown"
import { useTranslations } from 'next-intl'

// Mock streaming content chunks - 模拟流式内容块
const streamingChunks = [
  "# AI 市场分析报告\n\n*生成于 2024年12月20日*\n\n---\n\n正在初始化综合市场分析...",

  "# AI 市场分析报告\n\n*生成于 2024年12月20日*\n\n---\n\n## 执行摘要\n\n人工智能市场持续经历前所未有的增长，在多个行业都有重大发展。我们的分析揭示了将在2025年塑造行业格局的关键趋势。\n\n### 关键亮点\n\n- **市场规模**: 预计2024年达到1846亿美元\n- **增长率**: 到2030年复合年增长率28.7%\n- **领先行业**: 医疗保健、金融、汽车",

  "# AI 市场分析报告\n\n*生成于 2024年12月20日*\n\n---\n\n## 执行摘要\n\n人工智能市场持续经历前所未有的增长，在多个行业都有重大发展。我们的分析揭示了将在2025年塑造行业格局的关键趋势。\n\n### 关键亮点\n\n- **市场规模**: 预计2024年达到1846亿美元\n- **增长率**: 到2030年复合年增长率28.7%\n- **领先行业**: 医疗保健、金融、汽车\n\n## 市场细分\n\n### 按技术分类\n\n::: info\n**机器学习**占据65%的市场份额，其次是自然语言处理占18%。\n:::\n\n1. **机器学习**: 1198亿美元\n2. **自然语言处理**: 332亿美元\n3. **计算机视觉**: 221亿美元\n4. **机器人技术**: 95亿美元\n\n### 地理分布\n\n- **北美**: 42%市场份额\n- **亚太地区**: 31%市场份额\n- **欧洲**: 21%市场份额\n- **世界其他地区**: 6%市场份额",

  '# AI 市场分析报告\n\n*生成于 2024年12月20日*\n\n---\n\n## 执行摘要\n\n人工智能市场持续经历前所未有的增长，在多个行业都有重大发展。我们的分析揭示了将在2025年塑造行业格局的关键趋势。\n\n### 关键亮点\n\n- **市场规模**: 预计2024年达到1846亿美元\n- **增长率**: 到2030年复合年增长率28.7%\n- **领先行业**: 医疗保健、金融、汽车\n\n## 市场细分\n\n### 按技术分类\n\n::: info\n**机器学习**占据65%的市场份额，其次是自然语言处理占18%。\n:::\n\n1. **机器学习**: 1198亿美元\n2. **自然语言处理**: 332亿美元\n3. **计算机视觉**: 221亿美元\n4. **机器人技术**: 95亿美元\n\n### 地理分布\n\n- **北美**: 42%市场份额\n- **亚太地区**: 31%市场份额\n- **欧洲**: 21%市场份额\n- **世界其他地区**: 6%市场份额\n\n## 行业应用\n\n### 医疗保健\n\n```python\n# AI在医疗保健中的关键指标\ndiagnostic_accuracy = 0.94\ncost_reduction = 0.23\npatient_satisfaction = 0.87\n\nprint(f"诊断准确率提升了 {diagnostic_accuracy*100}%")\nprint(f"成本降低: {cost_reduction*100}%")\n```\n\n医疗保健中的AI应用正在通过以下方式革命性地改变患者护理：\n\n- **医学影像**: 诊断影像准确率94%\n- **药物发现**: 开发时间缩短40%\n- **个性化治疗**: 定制化治疗建议\n\n### 金融服务\n\n::: warning\n监管合规仍然是AI在金融服务实施中的关键考虑因素。\n:::\n\n- **欺诈检测**: 实时交易监控\n- **算法交易**: 高频决策制定\n- **风险评估**: 增强的信用评分模型',

  '# AI 市场分析报告\n\n*生成于 2024年12月20日*\n\n---\n\n## 执行摘要\n\n人工智能市场持续经历前所未有的增长，在多个行业都有重大发展。我们的分析揭示了将在2025年塑造行业格局的关键趋势。\n\n### 关键亮点\n\n- **市场规模**: 预计2024年达到1846亿美元\n- **增长率**: 到2030年复合年增长率28.7%\n- **领先行业**: 医疗保健、金融、汽车\n\n## 市场细分\n\n### 按技术分类\n\n::: info\n**机器学习**占据65%的市场份额，其次是自然语言处理占18%。\n:::\n\n1. **机器学习**: 1198亿美元\n2. **自然语言处理**: 332亿美元\n3. **计算机视觉**: 221亿美元\n4. **机器人技术**: 95亿美元\n\n### 地理分布\n\n- **北美**: 42%市场份额\n- **亚太地区**: 31%市场份额\n- **欧洲**: 21%市场份额\n- **世界其他地区**: 6%市场份额\n\n## 行业应用\n\n### 医疗保健\n\n```python\n# AI在医疗保健中的关键指标\ndiagnostic_accuracy = 0.94\ncost_reduction = 0.23\npatient_satisfaction = 0.87\n\nprint(f"诊断准确率提升了 {diagnostic_accuracy*100}%")\nprint(f"成本降低: {cost_reduction*100}%")\n```\n\n医疗保健中的AI应用正在通过以下方式革命性地改变患者护理：\n\n- **医学影像**: 诊断影像准确率94%\n- **药物发现**: 开发时间缩短40%\n- **个性化治疗**: 定制化治疗建议\n\n### 金融服务\n\n::: warning\n监管合规仍然是AI在金融服务实施中的关键考虑因素。\n:::\n\n- **欺诈检测**: 实时交易监控\n- **算法交易**: 高频决策制定\n- **风险评估**: 增强的信用评分模型\n\n## 未来展望\n\n### 新兴趋势\n\n1. **生成式AI采用**\n   - 73%的企业计划在2025年前实施\n   - 专注于内容创作和自动化\n\n2. **边缘AI计算**\n   - 为实时应用减少延迟\n   - 增强隐私和安全性\n\n3. **AI治理框架**\n   - 标准化的伦理指导原则\n   - 监管合规自动化\n\n### 投资格局\n\n::: tip\nAI初创公司的风险投资在2024年达到252亿美元，比上一年增长15%。\n:::\n\n**主要投资领域：**\n- 自主系统: 81亿美元\n- 医疗保健AI: 63亿美元\n- 企业自动化: 48亿美元\n- 网络安全AI: 32亿美元\n\n## 建议\n\n基于我们的综合分析，我们建议采取以下战略行动：\n\n1. **优先考虑AI集成**: 组织应制定明确的AI采用路线图\n2. **投资人才**: 通过培训和招聘建立内部AI能力\n3. **关注伦理**: 从一开始就实施负责任的AI实践\n4. **监控法规**: 了解不断发展的AI治理要求\n\n---\n\n*报告完成。分析基于来自500多家公司和50多个行业来源的数据。*',
]

export default function ReportPage() {
  const t = useTranslations('embed.report')
  const [content, setContent] = useState("")
  const [isStreaming, setIsStreaming] = useState(true)
  const [showNewContentNotice, setShowNewContentNotice] = useState(false)
  const [isAtBottom, setIsAtBottom] = useState(true)
  const [chunkIndex, setChunkIndex] = useState(0)

  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)

  const renderedHtml = useMarkdown(content)

  // Simulate streaming content
  useEffect(() => {
    if (chunkIndex < streamingChunks.length) {
      const timer = setTimeout(() => {
        setContent(streamingChunks[chunkIndex])
        setChunkIndex((prev) => prev + 1)

        if (chunkIndex === streamingChunks.length - 1) {
          setIsStreaming(false)
        }
      }, 2500) // Slower streaming for better demo

      return () => clearTimeout(timer)
    }
    // 当chunkIndex >= streamingChunks.length时，也需要返回一个函数
    return () => {}
  }, [chunkIndex])

  // Handle scroll detection
  const handleScroll = useCallback(() => {
    if (!scrollAreaRef.current) return

    const { scrollTop, scrollHeight, clientHeight } = scrollAreaRef.current
    const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100

    setIsAtBottom(isNearBottom)

    if (!isNearBottom && isStreaming && chunkIndex > 1) {
      setShowNewContentNotice(true)
    } else {
      setShowNewContentNotice(false)
    }
  }, [isStreaming, chunkIndex])

  // Auto-scroll when new content arrives
  useEffect(() => {
    if (isAtBottom && scrollAreaRef.current && chunkIndex > 0) {
      setTimeout(() => {
        if (scrollAreaRef.current) {
          scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
        }
      }, 100)
    }
  }, [content, isAtBottom, chunkIndex])

  const jumpToLatest = () => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
      setShowNewContentNotice(false)
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Top Bar */}
      <div className="sticky top-0 z-50 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex h-16 items-center px-6">
          {/* Logo */}
          <div className="flex items-center gap-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
              <Zap className="h-4 w-4 text-primary-foreground" />
            </div>
            <span className="font-semibold text-lg">{t('subtitle')}</span>
          </div>

          {/* Report Title */}
          <div className="flex-1 flex justify-center px-8">
            <div className="text-center">
              <h1 className="font-semibold text-lg">{t('title')}</h1>
            </div>
          </div>

          {/* Status Indicator */}
          <div className="flex items-center gap-3">
            {isStreaming ? (
              <Badge variant="secondary" className="animate-pulse">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
                {t('generating')}
              </Badge>
            ) : (
              <Badge variant="outline">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-2" />
                {t('complete')}
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Main Layout */}
      <div className="flex">
        {/* Main Content Area */}
        <div className="flex-1">
          <ScrollArea className="h-[calc(100vh-4rem)]" ref={scrollAreaRef} onScrollCapture={handleScroll}>
            <div className="max-w-4xl mx-auto p-6">
              <Card className="min-h-[600px]">
                <div className="p-8">
                  {content ? (
                    <div
                      ref={contentRef}
                      className="prose prose-slate dark:prose-invert max-w-none prose-headings:scroll-mt-20"
                      dangerouslySetInnerHTML={{ __html: renderedHtml }}
                    />
                  ) : (
                    <div className="space-y-6">
                      <Skeleton className="h-8 w-3/4" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-5/6" />
                      <Separator className="my-6" />
                      <Skeleton className="h-6 w-1/2" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-4/5" />
                        <Skeleton className="h-4 w-3/4" />
                      </div>
                    </div>
                  )}

                  {/* Streaming Indicator */}
                  {isStreaming && content && (
                    <div className="mt-8 flex items-center gap-3 text-muted-foreground">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.3s]" />
                        <div className="w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.15s]" />
                        <div className="w-2 h-2 bg-current rounded-full animate-bounce" />
                      </div>
                      <span className="text-sm">{t('analyzing')}</span>
                    </div>
                  )}
                </div>
              </Card>
            </div>
          </ScrollArea>

          {/* New Content Notice */}
          {showNewContentNotice && (
            <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-40">
              <Button onClick={jumpToLatest} className="shadow-lg animate-in slide-in-from-bottom-2" size="sm">
                <ChevronDown className="h-4 w-4 mr-2" />
                {t('viewLatest')}
              </Button>
            </div>
          )}
        </div>

        {/* Right Side Area - Reserved for Future Outline */}
        <div className="hidden lg:block w-80 border-l bg-muted/20">
          <div className="sticky top-16 h-[calc(100vh-4rem)] p-6">
            <div className="text-center text-muted-foreground">
              <div className="w-16 h-16 mx-auto mb-4 rounded-lg bg-muted flex items-center justify-center">
                <div className="w-8 h-8 rounded bg-muted-foreground/20" />
              </div>
              <p className="text-sm">{t('outlineNavigation')}</p>
              <p className="text-xs mt-1">{t('comingSoon')}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
