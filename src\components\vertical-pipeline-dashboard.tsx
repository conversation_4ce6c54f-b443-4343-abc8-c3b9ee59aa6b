'use client'

import { useState, useEffect, useCallback, useRef, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { StepCard } from './step-card'
import { Button } from '@/components/ui/button'
import { Play, Activity } from 'lucide-react'
import { useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'

export type ToolData = {
  id: string
  name: string
  component_id: 'TextRender' | 'UrlListRender' | 'SummaryRender'
  status: 'pending' | 'running' | 'completed' | 'error'
  startedAt?: string
  finishedAt?: string
  data?: any
}

export type StepData = {
  id: string
  title: string
  description: string
  status: 'pending' | 'running' | 'completed' | 'error'
  tools: ToolData[]
  createdAt: string
}

export interface VerticalPipelineDashboardProps {
  user?: {
    user_name?: string
    email?: string
  } | null | undefined
}

// Mock WebSocket events based on real API specification
// URL: /api/v1/aftercure/start
// 状态码设计: 1000-获取招投标数据, 2000-生成招标摘要, 3000-背景分析, 5000-错误处理
// 注意：这个函数将在组件内部重新定义以支持国际化
const createMockWebSocketEvents = (tSteps: any, tTools: any, tContent: any) => [
  {
    type: 'step_created',
    data: {
      id: '1000',
      title: tSteps('dataCollection.title'),
      description: tSteps('dataCollection.description'),
      status: 'pending',
      tools: [],
      createdAt: new Date().toISOString(),
    },
  },
  {
    type: 'tool_added',
    stepId: '1000',
    data: {
      id: '1001',
      name: tTools('dataCollector'),
      component_id: 'TextRender',
      status: 'pending',
    },
  },
  {
    type: 'step_status_updated',
    stepId: '1000',
    status: 'running',
  },
  {
    type: 'tool_status_updated',
    stepId: '1000',
    toolId: '1001',
    status: 'running',
    startedAt: new Date().toISOString(),
  },
  {
    type: 'tool_data_updated',
    stepId: '1000',
    toolId: '1001',
    data: {
      content: `🚀 招投标数据采集启动

数据源目标: 15个政府采购网站
已处理页面: 1,247 / 1,500
成功率: 89.3%
平均响应时间: 2.1s

当前状态:
✅ 所有API认证成功
✅ 速率限制已配置 (100 req/min)
✅ 数据验证管道活跃
🔄 正在处理招标公告页面
🔄 提取结构化元数据
⏳ 剩余时间: ~3分钟

质量指标:
- 高质量数据: 78%
- 中等质量数据: 18%
- 需要人工审核: 4%

下一步: 准备数据丰富管道...`,
    },
  },
  {
    type: 'tool_status_updated',
    stepId: '1000',
    toolId: '1001',
    status: 'completed',
    finishedAt: new Date().toISOString(),
  },
  {
    type: 'step_status_updated',
    stepId: '1000',
    status: 'completed',
  },
  {
    type: 'step_created',
    data: {
      id: '2000',
      title: '生成招标摘要',
      description: '基于采集的招投标数据，使用AI技术生成结构化的招标摘要信息',
      status: 'pending',
      tools: [],
      createdAt: new Date().toISOString(),
    },
  },
  {
    type: 'tool_added',
    stepId: '2000',
    data: {
      id: '2001',
      name: 'AI摘要生成引擎',
      component_id: 'UrlListRender',
      status: 'pending',
    },
  },
  {
    type: 'step_status_updated',
    stepId: '2000',
    status: 'running',
  },
  {
    type: 'tool_status_updated',
    stepId: '2000',
    toolId: '2001',
    status: 'running',
    startedAt: new Date().toISOString(),
  },
  {
    type: 'tool_data_updated',
    stepId: '2000',
    toolId: '2001',
    data: {
      urls: [
        {
          url: 'https://ccgp.gov.cn/cggg/zygg/gkzb/202401/t20240115_19876543.htm',
          title: '某市政府办公设备采购项目',
          status: 'active',
          priority: 'high',
          category: '政府采购',
          responseTime: '145ms',
        },
        {
          url: 'https://ccgp.gov.cn/cggg/zygg/gkzb/202401/t20240116_19876544.htm',
          title: '医疗设备购置招标公告',
          status: 'active',
          priority: 'high',
          category: '医疗采购',
          responseTime: '230ms',
        },
        {
          url: 'https://ccgp.gov.cn/cggg/zygg/gkzb/202401/t20240117_19876545.htm',
          title: '教育信息化建设项目',
          status: 'active',
          priority: 'high',
          category: '教育采购',
          responseTime: '189ms',
        },
        {
          url: 'https://ccgp.gov.cn/cggg/zygg/gkzb/202401/t20240118_19876546.htm',
          title: '交通基础设施建设招标',
          status: 'active',
          priority: 'medium',
          category: '基建项目',
          responseTime: '312ms',
        },
        {
          url: 'https://ccgp.gov.cn/cggg/zygg/gkzb/202401/t20240119_19876547.htm',
          title: '环保设备采购（已截标）',
          status: 'deprecated',
          priority: 'low',
          category: '环保采购',
          responseTime: '1.2s',
        },
        {
          url: 'https://ccgp.gov.cn/cggg/zygg/gkzb/202401/t20240120_19876548.htm',
          title: '文化设施建设项目',
          status: 'active',
          priority: 'medium',
          category: '文化项目',
          responseTime: '267ms',
        },
      ],
    },
  },
  {
    type: 'tool_status_updated',
    stepId: '2000',
    toolId: '2001',
    status: 'completed',
    finishedAt: new Date().toISOString(),
  },
  {
    type: 'step_status_updated',
    stepId: '2000',
    status: 'completed',
  },
  {
    type: 'step_created',
    data: {
      id: '3000',
      title: '背景分析',
      description: '对招投标项目进行深度背景分析，包括市场趋势、竞争环境、风险评估等',
      status: 'pending',
      tools: [],
      createdAt: new Date().toISOString(),
    },
  },
  {
    type: 'tool_added',
    stepId: '3000',
    data: {
      id: '3001',
      name: 'AI智能分析引擎',
      component_id: 'SummaryRender',
      status: 'pending',
    },
  },
  {
    type: 'step_status_updated',
    stepId: '3000',
    status: 'running',
  },
  {
    type: 'tool_status_updated',
    stepId: '3000',
    toolId: '3001',
    status: 'running',
    startedAt: new Date().toISOString(),
  },
  {
    type: 'tool_data_updated',
    stepId: '3000',
    toolId: '3001',
    data: {
      summary: [
        {
          title: '数据采集成功率',
          content: '成功处理1,247个招投标页面，成功率89.3%，数据质量高',
          type: 'success',
          metric: '1,247页面',
        },
        {
          title: '市场趋势分析',
          content: '政府采购项目占比67%，医疗设备采购增长显著，平均响应时间205ms',
          type: 'performance',
          metric: '205ms平均',
        },
        {
          title: '行业分类洞察',
          content: '政府办公设备和医疗设备招标频次最高，占总体流量的67%',
          type: 'insight',
          metric: '67%流量',
        },
        {
          title: '竞争环境评估',
          content: '所有活跃招投标项目均在SLA范围内，已标记过期项目待处理',
          type: 'status',
          metric: '100%正常',
        },
        {
          title: '投标建议',
          content: '重点关注政府采购类项目，考虑针对医疗设备领域的专业化发展',
          type: 'recommendation',
          metric: '23%提升空间',
        },
        {
          title: '下阶段行动',
          content: '部署自动化监控系统，实现招投标趋势实时预警机制',
          type: 'action',
          metric: '2项行动',
        },
      ],
    },
  },
  {
    type: 'tool_status_updated',
    stepId: '3000',
    toolId: '3001',
    status: 'completed',
    finishedAt: new Date().toISOString(),
  },
  {
    type: 'step_status_updated',
    stepId: '3000',
    status: 'completed',
  },
  {
    type: 'step_created',
    data: {
      id: '5000',
      title: '错误处理与恢复',
      description: '处理系统异常和错误情况，确保流程的稳定性和可恢复性',
      status: 'pending',
      tools: [],
      createdAt: new Date().toISOString(),
    },
  },
  {
    type: 'tool_added',
    stepId: '5000',
    data: {
      id: '5001',
      name: '错误诊断与恢复工具',
      component_id: 'TextRender',
      status: 'pending',
    },
  },
  {
    type: 'step_status_updated',
    stepId: '5000',
    status: 'running',
  },
  {
    type: 'tool_status_updated',
    stepId: '5000',
    toolId: '5001',
    status: 'running',
    startedAt: new Date().toISOString(),
  },
  {
    type: 'tool_data_updated',
    stepId: '5000',
    toolId: '5001',
    data: {
      content: `⚠️ 错误处理与系统诊断报告
生成时间: ${new Date().toLocaleString()}

=== 系统异常检测 ===
检测到的异常: 2个轻微警告，0个严重错误
总体健康状态: 95.8%
处理时间: 2m 15s
监控数据点: 847个

=== 关键性能指标 ===
✅ 数据质量评分: 89.3%
✅ API响应时间: 205ms平均
⚠️ 网络连接: 1次超时重试
✅ 错误恢复: 6项自动处理

=== 异常处理分析 ===
🔧 已处理问题:
   • 数据源连接超时 (+自动重连成功)
   • 部分招标页面解析失败 (+降级处理)
   • 临时存储空间不足 (+自动清理)

📊 性能指标:
   • 错误恢复成功率: 100%
   • 系统稳定性: 95.8%
   • 自动化处理覆盖率: 85%

=== 改进建议 ===
1. 即时行动:
   - 增加数据源冗余备份
   - 优化解析算法错误容忍度
   - 扩展存储容量监控

2. 长期策略:
   - 实施预测性错误防护
   - 建立智能故障转移机制
   - 开发高级异常预警系统

=== 后续步骤 ===
• 系统健康报告已生成
• 监控优化计划: 1-2周内实施
• 错误预防机制已激活

状态: ✅ 系统稳定运行
监控: 持续进行中...`,
    },
  },
  {
    type: 'tool_status_updated',
    stepId: '5000',
    toolId: '5001',
    status: 'completed',
    finishedAt: new Date().toISOString(),
  },
  {
    type: 'step_status_updated',
    stepId: '5000',
    status: 'completed',
  },
]

export function VerticalPipelineDashboard({ user }: VerticalPipelineDashboardProps) {
  // 移除频繁的渲染日志，只在真正挂载时记录
  const [steps, setSteps] = useState<StepData[]>([])
  const [isStreaming, setIsStreaming] = useState(false)
  const [eventIndex, setEventIndex] = useState(0)

  // i18n翻译
  const t = useTranslations('embed.pipeline.status')
  const tSteps = useTranslations('embed.pipeline.steps')
  const tTools = useTranslations('embed.pipeline.tools')
  const tContent = useTranslations('embed.pipeline.content')

  // 获取URL参数中的title
  const searchParams = useSearchParams()
  const title = searchParams.get('title') || t('defaultTitle')

  // 创建国际化的mock事件数据
  const mockWebSocketEvents = useMemo(() => [
    {
      type: 'step_created',
      data: {
        id: '1000',
        title: tSteps('dataCollection.title'),
        description: tSteps('dataCollection.description'),
        status: 'pending',
        tools: [],
        createdAt: new Date().toISOString(),
      },
    },
    {
      type: 'tool_added',
      stepId: '1000',
      data: {
        id: '1001',
        name: tTools('dataCollector'),
        component_id: 'TextRender',
        status: 'pending',
      },
    },
    {
      type: 'step_status_updated',
      stepId: '1000',
      status: 'running',
    },
    {
      type: 'tool_status_updated',
      stepId: '1000',
      toolId: '1001',
      status: 'running',
      startedAt: new Date().toISOString(),
    },
    {
      type: 'tool_data_updated',
      stepId: '1000',
      toolId: '1001',
      data: {
        content: tContent('dataCollectionStartup'),
      },
    },
    {
      type: 'tool_status_updated',
      stepId: '1000',
      toolId: '1001',
      status: 'completed',
      finishedAt: new Date().toISOString(),
    },
    {
      type: 'step_status_updated',
      stepId: '1000',
      status: 'completed',
    },
    {
      type: 'step_created',
      data: {
        id: '2000',
        title: tSteps('summaryGeneration.title'),
        description: tSteps('summaryGeneration.description'),
        status: 'pending',
        tools: [],
        createdAt: new Date().toISOString(),
      },
    },
    {
      type: 'tool_added',
      stepId: '2000',
      data: {
        id: '2001',
        name: tTools('summaryEngine'),
        component_id: 'UrlListRender',
        status: 'pending',
      },
    },
    // 简化版本，只包含前两个步骤
  ], [tSteps, tTools, tContent])

  // 滚动相关状态
  const [isUserAtBottom, setIsUserAtBottom] = useState(true)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const isAutoScrolling = useRef(false)

  // 获取当前正在执行的步骤
  const getCurrentStep = useCallback(() => {
    const runningStep = steps.find(step => step.status === 'running')
    if (runningStep) {
      return runningStep.title
    }

    // 如果没有正在运行的步骤，返回最后一个步骤
    if (steps.length > 0) {
      const lastStep = steps[steps.length - 1]
      return lastStep.title
    }

    return '准备启动'
  }, [steps])

  // 获取用户名，优先使用user_name，其次使用email的用户名部分
  const getUserName = useCallback(() => {
    if (!user) return t('defaultUser')

    if (user.user_name) {
      return user.user_name
    }

    if (user.email) {
      // 从邮箱中提取用户名部分
      const emailUsername = user.email.split('@')[0]
      return emailUsername
    }

    return t('defaultUser')
  }, [user, t])

  // 检测用户是否在滚动容器底部
  const checkIfUserAtBottom = useCallback(() => {
    if (!scrollContainerRef.current || isAutoScrolling.current) return

    const container = scrollContainerRef.current
    const threshold = 50 // 50px阈值，用户在底部附近就认为在底部
    const isAtBottom = 
      container.scrollHeight - container.scrollTop - container.clientHeight <= threshold

    setIsUserAtBottom(isAtBottom)
  }, [])

  // 平滑滚动到底部
  const scrollToBottom = useCallback(() => {
    if (!scrollContainerRef.current) return

    isAutoScrolling.current = true
    
    scrollContainerRef.current.scrollTo({
      top: scrollContainerRef.current.scrollHeight,
      behavior: 'smooth'
    })

    // 滚动动画完成后重置自动滚动标志
    setTimeout(() => {
      isAutoScrolling.current = false
      setIsUserAtBottom(true)
    }, 500) // 等待滚动动画完成
  }, [])

  // 滚动事件处理
  const handleScroll = useCallback(() => {
    checkIfUserAtBottom()
  }, [checkIfUserAtBottom])

  // 添加滚动事件监听
  useEffect(() => {
    const container = scrollContainerRef.current
    if (!container) return

    container.addEventListener('scroll', handleScroll, { passive: true })
    
    return () => {
      container.removeEventListener('scroll', handleScroll)
    }
  }, [handleScroll])

  const processWebSocketEvent = useCallback((event: any) => {
    setSteps(prevSteps => {
      const newSteps = [...prevSteps]

      switch (event.type) {
        case 'step_created':
          newSteps.push(event.data)
          break

        case 'tool_added':
          const stepIndex = newSteps.findIndex(step => step.id === event.stepId)
          if (stepIndex !== -1) {
            const currentTools = newSteps[stepIndex].tools
            // 检查工具是否已存在，避免重复添加
            const existingTool = currentTools.find(tool => tool.id === event.data.id)
            if (!existingTool) {
              newSteps[stepIndex].tools.push(event.data)
            }
          }
          break

        case 'step_status_updated':
          const stepToUpdate = newSteps.find(step => step.id === event.stepId)
          if (stepToUpdate) {
            stepToUpdate.status = event.status
          }
          break

        case 'tool_status_updated':
          const step = newSteps.find(step => step.id === event.stepId)
          if (step) {
            const tool = step.tools.find(tool => tool.id === event.toolId)
            if (tool) {
              tool.status = event.status
              if (event.startedAt) tool.startedAt = event.startedAt
              if (event.finishedAt) tool.finishedAt = event.finishedAt
            }
          }
          break

        case 'tool_data_updated':
          const stepWithTool = newSteps.find(step => step.id === event.stepId)
          if (stepWithTool) {
            const toolToUpdate = stepWithTool.tools.find(tool => tool.id === event.toolId)
            if (toolToUpdate) {
              toolToUpdate.data = event.data
            }
          }
          break
      }

      return newSteps
    })
  }, [])

  // 独立的滚动逻辑 - 监听steps变化自动滚动
  useEffect(() => {
    // 当steps发生变化时，自动滚动到底部
    if (steps.length > 0) {
      // 延迟滚动，确保DOM更新完成
      const timer = setTimeout(() => {
        scrollToBottom()
      }, 100)

      return () => clearTimeout(timer)
    }
    // 如果没有steps，不需要清理函数
    return undefined
  }, [steps, scrollToBottom])

  // 移除了手动控制函数，现在使用自动循环执行

  // 自动启动流水线
  useEffect(() => {
    // 组件挂载后自动启动模拟流水线
    const autoStartTimer = setTimeout(() => {
      console.log('自动启动流水线，清空数据')
      setSteps([]) // 确保清空步骤
      setEventIndex(0) // 重置事件索引
      setIsStreaming(true)
    }, 1000) // 延迟1秒启动，让用户看到初始状态

    return () => clearTimeout(autoStartTimer)
  }, []) // 只在组件挂载时执行一次

  useEffect(() => {
    // 只有在流水线运行时才处理事件
    if (!isStreaming) {
      return
    }

    // 检查是否所有事件都已处理完成
    if (eventIndex >= mockWebSocketEvents.length) {
      // 流水线完成后，等待3秒自动重新开始
      const restartTimer = setTimeout(() => {
        console.log('🔄 流水线重新开始，清空所有数据')
        setIsStreaming(false) // 先停止流水线
        setSteps([]) // 清空步骤
        setEventIndex(0) // 重置事件索引
        // 延迟重新启动，确保状态更新完成
        setTimeout(() => {
          setIsStreaming(true) // 重新开始流水线
        }, 500)
      }, 3000)

      return () => clearTimeout(restartTimer)
    }

    // 处理当前事件
    const timer = setTimeout(() => {
      const event = mockWebSocketEvents[eventIndex]
      console.log('🎯 即将处理事件:', eventIndex, event.type)
      processWebSocketEvent(event)
      setEventIndex(prev => prev + 1)
    }, 2000) // 2秒间隔

    return () => clearTimeout(timer)
  }, [isStreaming, eventIndex, processWebSocketEvent, mockWebSocketEvents])

  return (
    <div className="flex flex-col h-full w-full">

      {/* Pipeline Status - 固定在顶部 */}
      <div className="text-center w-full p-4 bg-slate-50 border-b border-slate-200 flex-shrink-0">
        <div className="inline-flex items-center gap-3 px-6 py-3 bg-white rounded-full shadow-sm border">
          <div
            className={`w-3 h-3 rounded-full ${
              isStreaming
                ? 'bg-green-500 animate-pulse'
                : 'bg-slate-300'
            }`}
          />
          <Activity
            className={`w-4 h-4 ${
              isStreaming
                ? 'text-green-600'
                : 'text-slate-400'
            }`}
          />
          <span className="text-sm font-medium text-slate-700">
            {isStreaming
              ? t('running', {
                  userName: getUserName(),
                  title: title,
                  currentStep: getCurrentStep()
                })
              : steps.length > 0
                ? t('completed')
                : t('preparing')}
          </span>
        </div>
      </div>

      {/* Scrollable Pipeline Flow - 可滚动区域 */}
      <div
        ref={scrollContainerRef}
        data-testid="scroll-container"
        className="flex-1 overflow-y-auto px-4 py-6"
        style={{
          height: 'calc(100vh - 120px)', // 减去Pipeline Status的高度
          wordBreak: 'break-word', // 确保长文本正确换行
          overflowWrap: 'break-word' // 兼容性设置
        }}
      >
        {steps.length === 0 && !isStreaming && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-20"
          >
            <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-slate-100 flex items-center justify-center">
              <Play className="w-10 h-10 text-slate-400" />
            </div>
            <h3 className="text-xl font-semibold text-slate-900 mb-2">{t('completed')}</h3>
            <p className="text-slate-500 text-lg">{t('nextRound')}</p>
          </motion.div>
        )}

        <div className="space-y-6 max-w-4xl mx-auto">
          <AnimatePresence mode="popLayout">
            {steps.map((step, index) => (
              <motion.div
                key={step.id}
                layout
                initial={{ opacity: 0, y: 100, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -50, scale: 0.95 }}
                transition={{
                  duration: 0.6,
                  ease: 'easeOut',
                  layout: { duration: 0.4 },
                }}
                className="w-full"
              >
                <StepCard step={step} index={index} />
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* 滚动到底部按钮 */}
        {!isUserAtBottom && steps.length > 0 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="fixed bottom-6 right-6 z-10"
          >
            <Button
              onClick={scrollToBottom}
              data-testid="scroll-to-bottom-button"
              className="rounded-full w-12 h-12 shadow-lg bg-blue-600 hover:bg-blue-700"
              size="sm"
            >
              <svg 
                width="20" 
                height="20" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2"
                className="text-white"
              >
                <path d="M7 13l3 3 7-7" />
                <path d="M7 6l3 3 7-7" />
              </svg>
            </Button>
          </motion.div>
        )}
      </div>
    </div>
  )
}
