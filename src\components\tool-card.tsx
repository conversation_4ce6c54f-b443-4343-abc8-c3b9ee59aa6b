"use client"

import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import type { ToolData } from "./vertical-pipeline-dashboard"
import { Clock, CheckCircle, AlertCircle, Loader2, FileText, Link, BarChart3, ExternalLink } from "lucide-react"

interface ToolCardProps {
  tool: ToolData
}

const statusConfig = {
  pending: {
    color: "bg-slate-100 text-slate-600 border-slate-200",
    icon: Clock,
  },
  running: {
    color: "bg-blue-100 text-blue-600 border-blue-200",
    icon: Loader2,
  },
  completed: {
    color: "bg-emerald-100 text-emerald-600 border-emerald-200",
    icon: CheckCircle,
  },
  error: {
    color: "bg-red-100 text-red-600 border-red-200",
    icon: AlertCircle,
  },
}

const componentIcons = {
  TextRender: FileText,
  UrlListRender: Link,
  SummaryRender: BarChart3,
}

export function ToolCard({ tool }: ToolCardProps) {
  const config = statusConfig[tool.status]
  const StatusIcon = config.icon
  const ComponentIcon = componentIcons[tool.component_id]

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString("en-US", {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    })
  }

  const getDuration = () => {
    if (!tool.startedAt) return null
    const start = new Date(tool.startedAt)
    const end = tool.finishedAt ? new Date(tool.finishedAt) : new Date()
    const duration = Math.round((end.getTime() - start.getTime()) / 1000)
    return `${duration}s`
  }

  const renderContent = () => {
    if (!tool.data) return null

    switch (tool.component_id) {
      case "TextRender":
        return <TextRenderComponent data={tool.data} />
      case "UrlListRender":
        return <UrlListRenderComponent data={tool.data} />
      case "SummaryRender":
        return <SummaryRenderComponent data={tool.data} />
      default:
        return null
    }
  }

  return (
    <Card className="border border-slate-200 bg-slate-50/50">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-white shadow-sm">
              <ComponentIcon className="w-4 h-4 text-slate-600" />
            </div>
            <div>
              <h4 className="font-semibold text-slate-900">{tool.name}</h4>
              <div className="flex items-center gap-4 text-xs text-slate-500 mt-1">
                {tool.startedAt && <span>Started: {formatTime(tool.startedAt)}</span>}
                {getDuration() && <span>Duration: {getDuration()}</span>}
              </div>
            </div>
          </div>
          <Badge variant="outline" className={`${config.color} text-xs`}>
            <StatusIcon className={`w-3 h-3 mr-1 ${tool.status === "running" ? "animate-spin" : ""}`} />
            {tool.status}
          </Badge>
        </div>
      </CardHeader>

      <AnimatePresence>
        {tool.data && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.4, ease: "easeOut" }}
          >
            <CardContent className="pt-0">{renderContent()}</CardContent>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  )
}

function TextRenderComponent({ data }: { data: { content: string } }) {
  return (
    <ScrollArea className="h-48 w-full rounded-md border bg-white">
      <div className="p-4">
        <pre className="text-sm text-slate-700 whitespace-pre-wrap leading-relaxed font-mono">{data.content}</pre>
      </div>
    </ScrollArea>
  )
}

function UrlListRenderComponent({
  data,
}: {
  data: {
    urls: Array<{
      url: string
      title: string
      status: string
      priority: string
      category?: string
      responseTime?: string
    }>
  }
}) {
  const priorityColors = {
    high: "bg-red-100 text-red-700 border-red-200",
    medium: "bg-yellow-100 text-yellow-700 border-yellow-200",
    low: "bg-slate-100 text-slate-600 border-slate-200",
  }

  const statusColors = {
    active: "bg-green-100 text-green-700 border-green-200",
    deprecated: "bg-slate-100 text-slate-500 border-slate-200",
    inactive: "bg-red-100 text-red-600 border-red-200",
  }

  return (
    <ScrollArea className="h-64 w-full" data-testid="url-list">
      <div className="space-y-3 p-1">
        {data.urls.map((urlItem, index) => (
          <motion.div
            key={index}
            data-testid="url-item"
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.05 }}
            className="p-4 bg-white rounded-lg border hover:shadow-sm transition-all duration-200"
          >
            <div className="flex items-start justify-between mb-2">
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <ExternalLink className="w-4 h-4 text-slate-400 flex-shrink-0" />
                <div className="min-w-0 flex-1">
                  <h5 className="font-medium text-sm text-slate-900 truncate">{urlItem.title}</h5>
                  <p className="text-xs text-slate-500 truncate font-mono mt-1">{urlItem.url}</p>
                </div>
              </div>
              <div className="flex gap-1 ml-3 flex-shrink-0">
                <Badge
                  variant="outline"
                  className={`text-xs ${priorityColors[urlItem.priority as keyof typeof priorityColors]}`}
                >
                  {urlItem.priority}
                </Badge>
                <Badge
                  variant="outline"
                  className={`text-xs ${statusColors[urlItem.status as keyof typeof statusColors]}`}
                >
                  {urlItem.status}
                </Badge>
              </div>
            </div>
            {(urlItem.category || urlItem.responseTime) && (
              <div className="flex items-center gap-4 text-xs text-slate-500">
                {urlItem.category && <span>Category: {urlItem.category}</span>}
                {urlItem.responseTime && <span>Response: {urlItem.responseTime}</span>}
              </div>
            )}
          </motion.div>
        ))}
      </div>
    </ScrollArea>
  )
}

function SummaryRenderComponent({
  data,
}: {
  data: { summary: Array<{ title: string; content: string; type: string; metric?: string }> }
}) {
  const typeConfig = {
    success: { color: "bg-green-100 text-green-700 border-green-200", icon: "✅" },
    performance: { color: "bg-blue-100 text-blue-700 border-blue-200", icon: "⚡" },
    insight: { color: "bg-purple-100 text-purple-700 border-purple-200", icon: "💡" },
    status: { color: "bg-indigo-100 text-indigo-700 border-indigo-200", icon: "📊" },
    recommendation: { color: "bg-orange-100 text-orange-700 border-orange-200", icon: "🎯" },
    action: { color: "bg-emerald-100 text-emerald-700 border-emerald-200", icon: "🚀" },
  }

  return (
    <ScrollArea className="h-72 w-full">
      <div className="space-y-3 p-1">
        {data.summary.map((item, index) => {
          const config = typeConfig[item.type as keyof typeof typeConfig] || typeConfig.insight

          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.08 }}
              className={`p-4 rounded-lg border ${config.color}`}
            >
              <div className="flex items-start gap-3">
                <span className="text-lg flex-shrink-0">{config.icon}</span>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-semibold text-sm">{item.title}</h5>
                    {item.metric && (
                      <Badge variant="outline" className="text-xs bg-white/50">
                        {item.metric}
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm leading-relaxed">{item.content}</p>
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>
    </ScrollArea>
  )
}
