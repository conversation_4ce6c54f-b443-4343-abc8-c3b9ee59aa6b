/**
 * Fetch拦截器提供者组件
 * 在应用启动时安装全局fetch拦截器
 */

'use client'

import { useEffect } from 'react'
import { installFetchInterceptor, getInterceptorStatus } from '@/lib/fetch-interceptor'

interface FetchInterceptorProviderProps {
  children: React.ReactNode
}

export function FetchInterceptorProvider({ children }: FetchInterceptorProviderProps) {
  useEffect(() => {
    // 安装全局fetch拦截器
    installFetchInterceptor()
    
    // 调试信息
    if (process.env.NODE_ENV === 'development') {
      const status = getInterceptorStatus()
      console.log('🔧 Fetch拦截器状态:', status)
    }
    
    // 清理函数（组件卸载时不需要卸载拦截器，因为它是全局的）
    return () => {
      // 在开发环境下可以选择卸载，但通常不需要
      // uninstallFetchInterceptor()
    }
  }, [])

  return <>{children}</>
}

/**
 * 拦截器调试组件（仅开发环境）
 */
export function FetchInterceptorDebugger() {
  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') {
      return
    }

    const interval = setInterval(() => {
      const status = getInterceptorStatus()
      console.log('🔍 [调试] Fetch拦截器状态:', status)
    }, 10000) // 每10秒检查一次

    return () => clearInterval(interval)
  }, [])

  return null
}
