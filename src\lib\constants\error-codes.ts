/**
 * 错误码常量定义
 * 统一管理所有API错误码，特别是认证相关的错误码
 */

// HTTP状态码
export const HTTP_STATUS = {
  OK: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const

// 认证相关错误码
export const AUTH_ERROR_CODES = {
  // 未授权
  UNAUTHORIZED: 401,
  // 无权限
  NO_PERMISSION: 402,
  // 用户未登录
  USER_NOT_LOGIN: 601,
  // 该用户已经被删除
  USER_DELETED: 603,
} as const

// 业务错误码
export const BUSINESS_ERROR_CODES = {
  // 参数错误
  PARAMETER_ERROR: 602,
  // Token过期
  TOKEN_EXPIRED: 604,
  // 账户被锁定
  ACCOUNT_LOCKED: 605,
  // 验证码错误
  VERIFICATION_CODE_ERROR: 606,
} as const

// 需要清空token并重定向到登录页的错误码
export const LOGOUT_ERROR_CODES = [
  AUTH_ERROR_CODES.UNAUTHORIZED,
  AUTH_ERROR_CODES.NO_PERMISSION,
  AUTH_ERROR_CODES.USER_NOT_LOGIN,
  AUTH_ERROR_CODES.USER_DELETED,
] as const

// 错误码消息映射
export const ERROR_CODE_MESSAGES: Record<number, string> = {
  // HTTP状态码
  [HTTP_STATUS.BAD_REQUEST]: '请求参数错误',
  [HTTP_STATUS.UNAUTHORIZED]: '未授权，请重新登录',
  [HTTP_STATUS.FORBIDDEN]: '权限不足',
  [HTTP_STATUS.NOT_FOUND]: '请求的资源不存在',
  [HTTP_STATUS.INTERNAL_SERVER_ERROR]: '服务器内部错误',
  
  // 认证错误码 (402单独处理，避免重复401)
  [AUTH_ERROR_CODES.NO_PERMISSION]: '权限不足，无法访问',
  [AUTH_ERROR_CODES.USER_NOT_LOGIN]: '用户未登录，请先登录',
  [AUTH_ERROR_CODES.USER_DELETED]: '该用户已被删除，请联系管理员',
  
  // 业务错误码
  [BUSINESS_ERROR_CODES.PARAMETER_ERROR]: '参数错误',
  [BUSINESS_ERROR_CODES.TOKEN_EXPIRED]: 'Token已过期，请重新登录',
  [BUSINESS_ERROR_CODES.ACCOUNT_LOCKED]: '账户已被锁定，请联系管理员',
  [BUSINESS_ERROR_CODES.VERIFICATION_CODE_ERROR]: '验证码错误',
} as const

/**
 * 检查是否为需要退出登录的错误码
 */
export function shouldLogout(errorCode: number): boolean {
  return LOGOUT_ERROR_CODES.includes(errorCode as any)
}

/**
 * 获取错误码对应的消息
 */
export function getErrorMessage(errorCode: number, defaultMessage?: string): string {
  return ERROR_CODE_MESSAGES[errorCode] || defaultMessage || '未知错误'
} 