# 路由冲突修复说明

## 🚨 问题描述

在实现嵌入系统时遇到了 Next.js App Router 的路由冲突错误：

```
Error: ./src/app/(embed)
You cannot have two parallel pages that resolve to the same path.
```

## 🔍 问题原因

原始的目录结构存在路由冲突：

```
src/app/
├── (dashboard)/
│   ├── reports/page.tsx     → 解析为 /reports
│   └── workspace/page.tsx   → 解析为 /workspace
└── (embed)/
    ├── reports/page.tsx     → 解析为 /reports (冲突!)
    └── workspace/page.tsx   → 解析为 /workspace (冲突!)
```

在 Next.js App Router 中，路由组 `(dashboard)` 和 `(embed)` 不会影响 URL 路径，因此两个路由组中的同名页面会产生冲突。

## ✅ 解决方案

将嵌入页面从路由组 `(embed)` 改为实际的路由段 `embed`：

### 修复前的结构
```
src/app/
└── (embed)/                 # 路由组，不影响URL
    ├── layout.tsx
    ├── dashboard/page.tsx   → /dashboard
    ├── reports/page.tsx     → /reports (冲突)
    └── workspace/page.tsx   → /workspace (冲突)
```

### 修复后的结构
```
src/app/
└── embed/                   # 实际路由段
    ├── layout.tsx
    ├── dashboard/page.tsx   → /embed/dashboard
    ├── reports/page.tsx     → /embed/reports
    └── workspace/page.tsx   → /embed/workspace
```

## 🔄 修改内容

### 1. 删除的文件
- `src/app/(embed)/layout.tsx`
- `src/app/(embed)/dashboard/page.tsx`
- `src/app/(embed)/reports/page.tsx`
- `src/app/(embed)/workspace/page.tsx`

### 2. 新创建的文件
- `src/app/embed/layout.tsx`
- `src/app/embed/dashboard/page.tsx`
- `src/app/embed/reports/page.tsx`
- `src/app/embed/workspace/page.tsx`

### 3. 更新的文档
- `migration-docs/IMPLEMENTATION_SUMMARY.md` - 更新目录结构说明
- `migration-docs/QUICK_START_GUIDE.md` - 更新访问URL示例

## 🎯 最终的嵌入页面URL

修复后，嵌入页面的访问路径为：

- **仪表板**: `http://localhost:3001/embed/dashboard?token=your-jwt-token`
- **报告**: `http://localhost:3001/embed/reports?token=your-jwt-token`
- **工作区**: `http://localhost:3001/embed/workspace?token=your-jwt-token`

## ✅ 验证结果

修复后的项目可以正常启动：

```bash
npm run dev
# ✓ Starting...
# ✓ Compiled middleware in 142ms
# ✓ Ready in 1620ms
# - Local: http://localhost:3001
```

## 📝 注意事项

1. **URL 变化**: 嵌入页面的URL从 `/dashboard` 变为 `/embed/dashboard`
2. **中间件匹配**: 确保中间件中的路径匹配逻辑正确（`pathname.startsWith('/embed')`）
3. **Vue3 集成**: 需要更新 Vue3 项目中的嵌入URL
4. **文档更新**: 相关文档已同步更新新的路径结构

## 🔧 相关配置更新

### 中间件配置
中间件中的路径匹配逻辑保持不变，因为已经使用 `/embed` 前缀：

```typescript
// src/lib/middleware/embed-guard.ts
if (!pathname.startsWith('/embed')) {
  return await next()
}
```

### 域名验证
域名验证逻辑无需修改，继续支持指定的白名单域名。

### API 端点
API 端点路径保持不变：
- `/api/embed/health`
- `/api/embed/config`

---

*此修复确保了 Next.js App Router 的路由系统正常工作，避免了路径冲突问题。*
