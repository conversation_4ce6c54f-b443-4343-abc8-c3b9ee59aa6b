/**
 * 开发环境调试配置
 * 专门用于支持iframe嵌入的本地开发和调试
 */

export interface DevDebugConfig {
  enabled: boolean
  allowedLocalPorts: number[]
  specialPaths: string[]
  logLevel: 'none' | 'basic' | 'detailed' | 'verbose'
  bypassSecurityChecks: boolean
  features: {
    apiMonitoring: boolean
    performanceTracking: boolean
    errorTracking: boolean
    loopDetection: boolean
  }
}

/**
 * 开发调试配置
 */
export const devDebugConfig: DevDebugConfig = {
  // 是否启用开发调试模式
  enabled: process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_DEBUG === 'true',
  
  // 允许的本地端口列表
  allowedLocalPorts: [
    3000,  // Next.js 默认端口
    3001,  // Next.js 备用端口
    10001, // 🎯 用户的外部项目端口
    8080,  // 常用开发端口
    8000,  // 常用开发端口
    9000   // 常用开发端口
  ],
  
  // 特殊路径支持
  specialPaths: [
    '/iframe-demo',
    '/iframe-test', 
    '/embed-demo',
    '/demo',
    '/test'
  ],
  
  // 日志级别
  logLevel: 'detailed',
  
  // 是否完全绕过安全检查（仅开发环境）
  bypassSecurityChecks: true,

  features: {
    apiMonitoring: true,
    performanceTracking: true,
    errorTracking: true,
    loopDetection: true
  }
}

/**
 * 开发调试工具类
 */
export class DevDebugger {
  private static config = devDebugConfig
  private apiCallCounts = new Map<string, number>()
  private lastApiCall = new Map<string, number>()
  private loopDetectionThreshold = 5 // 5秒内超过10次同样调用认为是循环
  private maxCallsInWindow = 10

  /**
   * 检查是否为开发调试模式
   */
  static isDebugMode(): boolean {
    return this.config.enabled
  }

  /**
   * 检查端口是否被允许
   */
  static isPortAllowed(port: number): boolean {
    return this.config.allowedLocalPorts.includes(port)
  }

  /**
   * 检查路径是否为特殊调试路径
   */
  static isSpecialPath(pathname: string): boolean {
    return this.config.specialPaths.some(path => pathname.includes(path))
  }

  /**
   * 检查URL是否为允许的开发URL
   */
  static isAllowedDevUrl(url: string): boolean {
    if (!this.isDebugMode()) return false

    try {
      const urlObj = new URL(url)
      
      // 检查是否为localhost系列
      const isLocalhost = ['localhost', '127.0.0.1', '0.0.0.0'].includes(urlObj.hostname)
      if (!isLocalhost) return false
      
      // 检查端口
      const port = parseInt(urlObj.port) || (urlObj.protocol === 'https:' ? 443 : 80)
      const isPortAllowed = this.isPortAllowed(port)
      
      // 特殊支持：localhost:10001的任何路径
      if (urlObj.hostname === 'localhost' && port === 10001) {
        this.log('🎯 特殊支持localhost:10001', url)
        return true
      }
      
      return isPortAllowed
    } catch {
      return false
    }
  }

  /**
   * 记录调试日志
   */
  static log(message: string, data?: any): void {
    if (!this.isDebugMode() || this.config.logLevel === 'none') return

    const timestamp = new Date().toISOString()
    const logMessage = `[DevDebug ${timestamp}] ${message}`

    if (this.config.logLevel === 'verbose') {
      console.log(logMessage, data)
    } else if (this.config.logLevel === 'detailed') {
      console.log(logMessage, data ? JSON.stringify(data, null, 2) : '')
    } else {
      console.log(logMessage)
    }
  }

  /**
   * 创建调试信息对象
   */
  static createDebugInfo(request: { url: string; referer?: string | null; origin?: string | null }) {
    return {
      timestamp: new Date().toISOString(),
      url: request.url,
      referer: request.referer || undefined,
      origin: request.origin || undefined,
      isDebugMode: this.isDebugMode(),
      allowedPorts: this.config.allowedLocalPorts,
      specialPaths: this.config.specialPaths
    }
  }

  /**
   * 检查iframe嵌入是否应该被允许
   */
  static shouldAllowIframeEmbed(referer: string | null, origin: string | null): {
    allowed: boolean
    reason: string
    debugInfo?: any
  } {
    if (!this.isDebugMode()) {
      return { allowed: false, reason: 'Debug mode disabled' }
    }

         // 如果配置了绕过安全检查
     if (this.config.bypassSecurityChecks) {
       this.log('🔓 绕过安全检查模式 - 自动允许嵌入')
       return { 
         allowed: true, 
         reason: 'Security checks bypassed in debug mode',
         debugInfo: this.createDebugInfo({ url: referer || origin || '', referer, origin })
       }
     }

     // 检查referer
     if (referer && this.isAllowedDevUrl(referer)) {
       this.log('✅ 开发URL验证通过', referer)
       return { 
         allowed: true, 
         reason: 'Allowed development URL',
         debugInfo: this.createDebugInfo({ url: referer, referer, origin })
       }
     }

     // 检查origin
     if (origin && this.isAllowedDevUrl(origin)) {
       this.log('✅ 开发Origin验证通过', origin)
       return { 
         allowed: true, 
         reason: 'Allowed development origin',
         debugInfo: this.createDebugInfo({ url: origin, referer, origin })
       }
     }

     return { 
       allowed: false, 
       reason: 'URL not in allowed development URLs',
       debugInfo: this.createDebugInfo({ url: referer || origin || '', referer, origin })
     }
  }

  /**
   * 记录API调用
   */
  logApiCall(url: string, method: string = 'GET', context?: string) {
    if (!DevDebugger.config.enabled || !DevDebugger.config.features.apiMonitoring) return

    const key = `${method}:${url}`
    const now = Date.now()
    
    // 重置计数器（如果上次调用超过阈值时间）
    const lastCall = this.lastApiCall.get(key) || 0
    if (now - lastCall > this.loopDetectionThreshold * 1000) {
      this.apiCallCounts.set(key, 0)
    }
    
    // 增加计数
    const count = (this.apiCallCounts.get(key) || 0) + 1
    this.apiCallCounts.set(key, count)
    this.lastApiCall.set(key, now)

    // 循环检测
    if (DevDebugger.config.features.loopDetection && count > this.maxCallsInWindow) {
      console.error(`🚨 [DevDebugger] 检测到可能的无限循环!`, {
        api: key,
        context,
        count,
        timeWindow: `${this.loopDetectionThreshold}秒`,
        timestamp: new Date().toISOString()
      })
      
      // 在iframe环境中通知父窗口
      if (typeof window !== 'undefined' && window.parent !== window) {
        window.parent.postMessage({
          type: 'LOOP_DETECTED',
          data: {
            api: key,
            context,
            count,
            timestamp: new Date().toISOString()
          }
        }, '*')
      }
    } else {
      DevDebugger.log(`📡 [API调用] ${key}`, {
        context,
        count,
        timestamp: new Date().toISOString()
      })
    }
  }

  /**
   * 记录Hook状态变化
   */
  logHookStateChange(hookName: string, prevState: any, newState: any, context?: string) {
    if (!DevDebugger.config.enabled || !DevDebugger.config.features.loopDetection) return

    // 检查状态是否真的发生了变化
    const hasChanged = JSON.stringify(prevState) !== JSON.stringify(newState)
    
    if (hasChanged) {
      DevDebugger.log(`🎣 [Hook状态变化] ${hookName}`, {
        context,
        prevState,
        newState,
        timestamp: new Date().toISOString()
      })
    }
  }

  /**
   * 记录性能指标
   */
  logPerformance(label: string, duration: number, context?: string) {
    if (!DevDebugger.config.enabled || !DevDebugger.config.features.performanceTracking) return

    DevDebugger.log(`⚡ [性能] ${label}: ${duration}ms`, {
      context,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * 记录错误
   */
  logError(error: Error | string, context?: string, additionalData?: any) {
    if (!DevDebugger.config.enabled || !DevDebugger.config.features.errorTracking) return

    const errorObj = typeof error === 'string' ? new Error(error) : error

    DevDebugger.log(`❌ [错误] ${errorObj.message}`, {
      stack: errorObj.stack,
      context,
      additionalData,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * 重置API调用计数
   */
  resetApiCounts() {
    this.apiCallCounts.clear()
    this.lastApiCall.clear()
    DevDebugger.log('🔄 API调用计数已重置')
  }

  /**
   * 获取API调用统计
   */
  getApiStats() {
    return {
      callCounts: Object.fromEntries(this.apiCallCounts),
      lastCalls: Object.fromEntries(this.lastApiCall)
    }
  }
}

// 全局调试器实例
export const devDebugger = new DevDebugger()

// localhost:10001 专用调试配置
export const LOCALHOST_10001_DEBUG_CONFIG = {
  enabled: true,
  logLevel: 'detailed' as const,
  features: {
    apiMonitoring: true,
    performanceTracking: true,
    errorTracking: true,
    loopDetection: true // 启用循环检测
  }
} 