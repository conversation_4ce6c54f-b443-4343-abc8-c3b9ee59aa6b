"use client"

import { motion } from "framer-motion"
import { ChevronRight } from "lucide-react"

interface SimpleArrowProps {
  isActive: boolean
}

export function SimpleArrow({ isActive }: SimpleArrowProps) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className="hidden lg:flex items-center justify-center"
    >
      <motion.div
        className={`p-2 rounded-full transition-all duration-500 ${
          isActive ? "bg-emerald-100 text-emerald-600 shadow-lg shadow-emerald-100" : "bg-slate-100 text-slate-400"
        }`}
        animate={{
          scale: isActive ? [1, 1.1, 1] : 1,
        }}
        transition={{
          duration: 2,
          repeat: isActive ? Number.POSITIVE_INFINITY : 0,
          ease: "easeInOut",
        }}
      >
        <ChevronRight className="w-5 h-5" />
      </motion.div>
    </motion.div>
  )
}
