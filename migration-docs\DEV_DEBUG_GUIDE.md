# 🔓 开发环境 Iframe 调试指南

## 📝 问题描述

在本地开发环境下，由于安全机制的限制，直接访问 iframe 嵌入页面可能会遇到以下错误：

```
[EmbedSecurity] 拒绝访问 /embed/* - 无效的 referer: http://localhost:3000
```

## 🎯 解决方案：开发调试后门

我们为本地开发环境提供了多层次的调试后门，可以根据需要选择合适的级别：

### 🔓 级别1：Localhost域名白名单

**自动启用条件：**

- `NODE_ENV=development`
- 或 `NEXT_PUBLIC_DEBUG=true`

**功能：**

- 自动允许来自 localhost、127.0.0.1、0.0.0.0 的 referer
- 保留基本的安全检查机制

### 🔓 级别2：超级调试模式

**启用条件：**

- 开发调试模式开启 AND 请求来自 localhost IP

**功能：**

- 完全跳过所有安全检查
- 自动设置正确的CORS头部
- 记录调试绕过日志

## 🛠️ 使用方法

### 方法1：环境变量配置

创建 `.env.local` 文件：

```env
# 启用开发调试模式
NODE_ENV=development
NEXT_PUBLIC_DEBUG=true
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NEXT_PUBLIC_EMBED_ENABLED=true
```

### 方法2：直接访问（推荐）

启动开发服务器后，直接访问：

```bash
# 启动开发服务器
npm run dev
# 或
pnpm dev

# 直接访问embed页面（会自动绕过安全检查）
http://localhost:3000/embed/dashboard?token=test-token-123
```

### 方法3：使用测试工具

```bash
# 使用HTML测试工具
http://localhost:3000/iframe-test.html

# 使用Next.js调试页面
http://localhost:3000/iframe-debug
```

## 📊 调试日志示例

### 成功绕过安全检查

```
[DomainValidator] 🔓 开发调试模式 - 允许localhost访问: http://localhost:3000
[EmbedSecurity] 🔓 开发调试模式 - 跳过所有安全检查: /embed/dashboard
[EmbedAccess] SUCCESS - /embed/dashboard { error: 'Development debug mode bypass' }
```

### 普通域名验证通过

```
[EmbedSecurity] 允许访问 /embed/* - /embed/dashboard
[EmbedAccess] SUCCESS - /embed/dashboard
```

## 🔐 安全注意事项

### ⚠️ 重要警告

- **仅在开发环境使用**：这些后门功能只在 `NODE_ENV=development` 时启用
- **生产环境自动禁用**：部署到生产环境时，所有调试后门都会自动关闭
- **IP限制**：超级调试模式仅对 localhost IP (127.0.0.1, ::1) 有效

### 🔍 安全检查清单

部署前确认：

- [ ] `NODE_ENV=production`
- [ ] `NEXT_PUBLIC_DEBUG=false` 或未设置
- [ ] 生产环境不包含调试相关的环境变量
- [ ] 域名白名单只包含合法的生产域名

## 🧪 测试场景

### 场景1：本地开发 iframe 功能

```javascript
// 在父页面中嵌入iframe
<iframe 
  src="http://localhost:3000/embed/dashboard?token=test-token"
  width="100%" 
  height="600">
</iframe>
```

### 场景2：PostMessage 通信测试

```javascript
// 监听来自iframe的消息
window.addEventListener('message', (event) => {
  if (event.origin === 'http://localhost:3000') {
    console.log('收到iframe消息:', event.data);
  }
});
```

### 场景3：直接访问调试

```bash
# 直接在浏览器中访问（会自动绕过限制）
http://localhost:3000/embed/dashboard?token=test&debug=true
```

## 📋 故障排除

### 问题1：仍然收到403错误

**检查：**

- 确认 `NODE_ENV=development`
- 确认请求来自 localhost
- 检查浏览器开发者工具的Console面板查看详细日志

### 问题2：iframe无法加载

**检查：**

- 确认开发服务器正在运行
- 检查端口是否被占用
- 确认防火墙设置

### 问题3：PostMessage不工作

**检查：**

- 确认iframe已完全加载
- 检查消息监听器的origin验证
- 查看浏览器Console的错误信息

## 🚀 最佳实践

### 开发流程建议

1. **启动开发环境**

   ```bash
   npm run dev
   ```

2. **使用调试工具**
   - 先用 `/iframe-debug` 页面测试基本功能
   - 再用 `/iframe-test.html` 测试完整场景

3. **逐步验证功能**
   - 页面加载 ✓
   - 认证流程 ✓  
   - PostMessage通信 ✓
   - 样式和布局 ✓

4. **部署前测试**
   - 本地构建：`npm run build && npm start`
   - 验证生产模式下的安全限制
   - 确认调试功能已禁用

---

**💡 提示：** 如果您需要在团队中共享调试配置，可以创建 `.env.development` 文件并提交到版本控制，但记住不要包含敏感信息！
