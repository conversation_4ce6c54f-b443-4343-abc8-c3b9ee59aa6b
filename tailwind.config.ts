import type { Config } from "tailwindcss";

const config: Config = {
    content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "*.{js,ts,jsx,tsx,mdx}"
  ],
  theme: {
    extend: {
        colors: {
          primary: 'oklch(0.8371 0.0649 267.5453)',
          secondary: 'oklch(0.9486 0.0228 266.9082)',
        },
      },
  },
  plugins: [require("tailwindcss-animate"), 
    require('@tailwindcss/typography')],
};
export default config;
