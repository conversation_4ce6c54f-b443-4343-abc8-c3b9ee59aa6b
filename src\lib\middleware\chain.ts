/**
 * 中间件链管理系统
 * 按顺序执行多个中间件函数
 */

import { NextRequest, NextResponse } from 'next/server'
import type { MiddlewareFunction } from './types'

/**
 * 创建中间件链
 * @param middlewares 中间件函数数组
 * @returns 组合后的中间件函数
 */
export function createMiddlewareChain(middlewares: MiddlewareFunction[]) {
  return async (request: NextRequest): Promise<NextResponse> => {
    let index = 0

    const next = async (): Promise<NextResponse> => {
      if (index >= middlewares.length) {
        return NextResponse.next()
      }

      const middleware = middlewares[index++]
      return await middleware(request, next)
    }

    return await next()
  }
}

/**
 * 中间件执行器
 * 提供更详细的执行日志和错误处理
 */
export class MiddlewareExecutor {
  private middlewares: MiddlewareFunction[]
  private enableLogging: boolean

  constructor(middlewares: MiddlewareFunction[], enableLogging = false) {
    this.middlewares = middlewares
    this.enableLogging = enableLogging
  }

  async execute(request: NextRequest): Promise<NextResponse> {
    let index = 0
    const startTime = Date.now()

    if (this.enableLogging) {
      console.log(`[Middleware] Starting chain for ${request.nextUrl.pathname}`)
    }

    const next = async (): Promise<NextResponse> => {
      if (index >= this.middlewares.length) {
        if (this.enableLogging) {
          console.log(`[Middleware] Chain completed in ${Date.now() - startTime}ms`)
        }
        return NextResponse.next()
      }

      const middleware = this.middlewares[index]
      const middlewareName = middleware.name || `middleware-${index}`
      
      if (this.enableLogging) {
        console.log(`[Middleware] Executing ${middlewareName}`)
      }

      index++

      try {
        const middlewareStartTime = Date.now()
        const result = await middleware(request, next)
        
        if (this.enableLogging) {
          console.log(`[Middleware] ${middlewareName} completed in ${Date.now() - middlewareStartTime}ms`)
        }

        return result
      } catch (error) {
        console.error(`[Middleware] Error in ${middlewareName}:`, error)
        
        // 发生错误时继续执行下一个中间件
        return await next()
      }
    }

    return await next()
  }
}

/**
 * 条件中间件包装器
 * 只在满足条件时执行中间件
 */
export function conditionalMiddleware(
  condition: (request: NextRequest) => boolean,
  middleware: MiddlewareFunction
): MiddlewareFunction {
  return async (request: NextRequest, next: () => Promise<NextResponse>) => {
    if (condition(request)) {
      return await middleware(request, next)
    }
    return await next()
  }
}

/**
 * 路径匹配中间件包装器
 * 只对匹配的路径执行中间件
 */
export function pathMatchMiddleware(
  pathPattern: string | RegExp | ((pathname: string) => boolean),
  middleware: MiddlewareFunction
): MiddlewareFunction {
  return conditionalMiddleware((request) => {
    const pathname = request.nextUrl.pathname

    if (typeof pathPattern === 'string') {
      return pathname === pathPattern || pathname.startsWith(pathPattern + '/')
    }
    
    if (pathPattern instanceof RegExp) {
      return pathPattern.test(pathname)
    }
    
    if (typeof pathPattern === 'function') {
      return pathPattern(pathname)
    }

    return false
  }, middleware)
}

/**
 * 异步中间件包装器
 * 为同步中间件提供异步支持
 */
export function asyncMiddleware(
  syncMiddleware: (request: NextRequest) => NextResponse | void
): MiddlewareFunction {
  return async (request: NextRequest, next: () => Promise<NextResponse>) => {
    const result = syncMiddleware(request)
    
    if (result instanceof NextResponse) {
      return result
    }
    
    return await next()
  }
}

/**
 * 错误处理中间件包装器
 * 为中间件提供统一的错误处理
 */
export function errorHandlingMiddleware(
  middleware: MiddlewareFunction,
  errorHandler?: (error: Error, request: NextRequest) => NextResponse
): MiddlewareFunction {
  return async (request: NextRequest, next: () => Promise<NextResponse>) => {
    try {
      return await middleware(request, next)
    } catch (error) {
      console.error('[Middleware] Unhandled error:', error)
      
      if (errorHandler) {
        return errorHandler(error as Error, request)
      }
      
      // 默认错误处理：继续执行下一个中间件
      return await next()
    }
  }
}
