/**
 * 快速验证测试 - 验证核心功能是否正常工作
 */

import { test, expect } from '@playwright/test'

const TEST_URL = 'http://localhost:3000/zh/embed/dashboard?from-og=true&token=test-token-123'

test.describe('快速验证 - 核心功能测试', () => {
  test('页面应该正常加载并显示流水线', async ({ page }) => {
    await page.goto(TEST_URL)
    await page.waitForLoadState('networkidle')

    // 验证页面标题
    await expect(page).toHaveTitle('SpecificAI')

    // 验证Pipeline Status存在
    const pipelineStatus = page.locator('text=模拟流水线运行中')
    await expect(pipelineStatus).toBeVisible({ timeout: 10000 })

    // 验证第一个步骤出现
    const firstStep = page.locator('[data-testid="step-card"]').first()
    await expect(firstStep).toBeVisible({ timeout: 15000 })

    // 验证步骤编号
    const stepNumber = page.locator('[data-testid="step-number"]').first()
    await expect(stepNumber).toHaveText('1')
  })

  test('应该没有横向滚动条', async ({ page }) => {
    await page.goto(TEST_URL)
    await page.waitForLoadState('networkidle')

    // 等待内容加载
    await page.waitForSelector('[data-testid="step-card"]', { timeout: 15000 })

    // 检查是否有横向滚动
    const hasHorizontalScroll = await page.evaluate(() => {
      const container = document.querySelector('[data-testid="scroll-container"]') as HTMLElement
      if (!container) return false
      return container.scrollWidth > container.clientWidth
    })

    expect(hasHorizontalScroll).toBe(false)
  })

  test('URL内容应该正确换行', async ({ page }) => {
    await page.goto(TEST_URL)
    await page.waitForLoadState('networkidle')

    // 等待URL列表出现
    await page.waitForSelector('[data-testid="url-list"]', { timeout: 30000 })

    // 检查URL项目是否存在
    const urlItems = page.locator('[data-testid="url-item"]')
    const count = await urlItems.count()
    expect(count).toBeGreaterThan(0)

    // 检查第一个URL项目的布局
    const firstUrlItem = urlItems.first()
    const boundingBox = await firstUrlItem.boundingBox()
    const containerBox = await page.locator('[data-testid="scroll-container"]').boundingBox()

    if (boundingBox && containerBox) {
      // 验证URL元素没有超出容器宽度
      expect(boundingBox.x + boundingBox.width).toBeLessThanOrEqual(containerBox.x + containerBox.width + 10) // 10px tolerance
    }
  })

  test('自动滚动功能应该正常工作', async ({ page }) => {
    await page.goto(TEST_URL)
    await page.waitForLoadState('networkidle')

    // 等待第一个步骤出现
    await page.waitForSelector('[data-testid="step-card"]', { timeout: 15000 })

    // 获取初始滚动位置
    const initialScrollTop = await page.evaluate(() => {
      const container = document.querySelector('[data-testid="scroll-container"]') as HTMLElement
      return container?.scrollTop || 0
    })

    // 等待更多内容添加（等待第二个步骤）
    await page.waitForTimeout(10000)

    // 检查是否有多个步骤
    const stepCount = await page.locator('[data-testid="step-card"]').count()
    
    if (stepCount > 1) {
      // 获取最终滚动位置
      const finalScrollTop = await page.evaluate(() => {
        const container = document.querySelector('[data-testid="scroll-container"]') as HTMLElement
        return container?.scrollTop || 0
      })

      // 验证页面已经滚动（如果有多个步骤）
      expect(finalScrollTop).toBeGreaterThanOrEqual(initialScrollTop)
    }

    // 至少验证页面内容是可见的
    const lastStep = page.locator('[data-testid="step-card"]').last()
    await expect(lastStep).toBeVisible()
  })

  test('流水线应该能够循环重新开始', async ({ page }) => {
    await page.goto(TEST_URL)
    await page.waitForLoadState('networkidle')

    // 等待第一个步骤出现
    await page.waitForSelector('[data-testid="step-card"]', { timeout: 15000 })

    // 等待足够长的时间让流水线完成一轮
    await page.waitForTimeout(45000)

    // 检查是否重新开始（页面应该被清空然后重新开始）
    // 这里我们检查步骤编号是否重新从1开始
    const firstStepNumber = await page.locator('[data-testid="step-number"]').first().textContent()
    expect(firstStepNumber).toBe('1')

    // 验证状态显示
    const pipelineStatus = page.locator('text=模拟流水线运行中')
    await expect(pipelineStatus).toBeVisible()
  })
})
