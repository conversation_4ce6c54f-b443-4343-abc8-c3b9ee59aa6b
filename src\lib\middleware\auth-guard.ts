/**
 * 认证守卫
 * 处理用户登录状态验证和重定向
 */

import { NextRequest, NextResponse } from 'next/server'
import { buildGuardContext } from './context-builder'
import type { MiddlewareFunction } from './types'

/**
 * 认证守卫中间件
 * 检查用户认证状态并处理相应的重定向
 */
export const authGuard: MiddlewareFunction = async (
  request: NextRequest,
  next: () => Promise<NextResponse>
): Promise<NextResponse> => {
  const context = await buildGuardContext(request)
  const { routeConfig, user, pathname } = context

  // 如果路由不需要认证处理，直接继续
  if (!routeConfig?.requiresAuth && !routeConfig?.requiresGuest && !routeConfig?.tryAuth) {
    return await next()
  }

  const isAuthenticated = !!user

  // 需要认证但未登录
  if (routeConfig.requiresAuth && !isAuthenticated) {
    const redirectPath = routeConfig.redirectIfUnauthorized || '/login'
    const redirectUrl = new URL(redirectPath, request.url)

    // 添加来源查询参数，用于登录后重定向
    redirectUrl.searchParams.set('from', pathname)
    redirectUrl.searchParams.set('redirect', pathname)

    console.warn(`[AuthGuard] Visited ${pathname}, but not authorized, redirect to ${redirectPath}`)
    return NextResponse.redirect(redirectUrl)
  }

  // 仅限游客但已登录
  if (routeConfig.requiresGuest && isAuthenticated) {
    const redirectPath = routeConfig.redirectIfUnauthorized || '/'
    const redirectUrl = new URL(redirectPath, request.url)

    // 保留现有的重定向参数
    const existingRedirect = request.nextUrl.searchParams.get('redirect')
    if (existingRedirect) {
      redirectUrl.searchParams.set('redirect', existingRedirect)
    }

    console.warn(`[AuthGuard] Visited ${pathname}, but already authorized, redirect to ${redirectPath}`)
    return NextResponse.redirect(redirectUrl)
  }

  // 尝试认证但不强制（tryAuth）
  if (routeConfig.tryAuth && !isAuthenticated) {
    console.info(`[AuthGuard] Try auth for ${pathname}, but user not authenticated`)
    // 继续执行，不重定向
  }

  return await next()
}

/**
 * 创建自定义认证守卫
 * @param options 认证守卫选项
 * @returns 自定义认证守卫中间件
 */
export function createAuthGuard(options: {
  defaultLoginPath?: string
  defaultHomePath?: string
  enableLogging?: boolean
  customRedirectLogic?: (context: any) => string | null
}): MiddlewareFunction {
  const {
    defaultLoginPath = '/login',
    defaultHomePath = '/',
    enableLogging = false,
    customRedirectLogic
  } = options

  return async (request: NextRequest, next: () => Promise<NextResponse>) => {
    const context = await buildGuardContext(request)
    const { routeConfig, user, pathname } = context

    if (!routeConfig?.requiresAuth && !routeConfig?.requiresGuest && !routeConfig?.tryAuth) {
      return await next()
    }

    const isAuthenticated = !!user

    // 使用自定义重定向逻辑
    if (customRedirectLogic) {
      const customRedirect = customRedirectLogic({ ...context, isAuthenticated })
      if (customRedirect) {
        const redirectUrl = new URL(customRedirect, request.url)
        if (enableLogging) {
          console.log(`[CustomAuthGuard] Custom redirect: ${pathname} -> ${customRedirect}`)
        }
        return NextResponse.redirect(redirectUrl)
      }
    }

    // 标准认证逻辑
    if (routeConfig.requiresAuth && !isAuthenticated) {
      const redirectPath = routeConfig.redirectIfUnauthorized || defaultLoginPath
      const redirectUrl = new URL(redirectPath, request.url)
      redirectUrl.searchParams.set('from', pathname)

      if (enableLogging) {
        console.log(`[CustomAuthGuard] Auth required: ${pathname} -> ${redirectPath}`)
      }
      return NextResponse.redirect(redirectUrl)
    }

    if (routeConfig.requiresGuest && isAuthenticated) {
      const redirectPath = routeConfig.redirectIfUnauthorized || defaultHomePath
      const redirectUrl = new URL(redirectPath, request.url)

      if (enableLogging) {
        console.log(`[CustomAuthGuard] Guest only: ${pathname} -> ${redirectPath}`)
      }
      return NextResponse.redirect(redirectUrl)
    }

    return await next()
  }
}

/**
 * 认证状态检查器
 * 用于在其他守卫中快速检查认证状态
 */
export class AuthChecker {
  static async isAuthenticated(request: NextRequest): Promise<boolean> {
    try {
      const context = await buildGuardContext(request)
      return !!context.user
    } catch {
      return false
    }
  }

  static async getUserId(request: NextRequest): Promise<string | null> {
    try {
      const context = await buildGuardContext(request)
      return context.user?.id || null
    } catch {
      return null
    }
  }

  static async hasRole(request: NextRequest, role: string): Promise<boolean> {
    try {
      const context = await buildGuardContext(request)
      return context.userRoles.some(r => r.roleCode === role)
    } catch {
      return false
    }
  }
}

/**
 * 认证守卫工具函数
 */
export const authGuardUtils = {
  /**
   * 创建重定向URL
   */
  createRedirectUrl(basePath: string, originalUrl: string, options: {
    addFrom?: boolean
    addRedirect?: boolean
    preserveQuery?: boolean
  } = {}): URL {
    const url = new URL(basePath, originalUrl)
    const originalUrlObj = new URL(originalUrl)

    if (options.addFrom) {
      url.searchParams.set('from', originalUrlObj.pathname)
    }

    if (options.addRedirect) {
      url.searchParams.set('redirect', originalUrlObj.pathname + originalUrlObj.search)
    }

    if (options.preserveQuery) {
      originalUrlObj.searchParams.forEach((value, key) => {
        if (!url.searchParams.has(key)) {
          url.searchParams.set(key, value)
        }
      })
    }

    return url
  },

  /**
   * 检查路径是否为认证相关路径
   */
  isAuthPath(pathname: string): boolean {
    const authPaths = ['/login', '/register', '/forgot-password', '/reset-password']
    return authPaths.some(path => pathname.startsWith(path))
  },

  /**
   * 检查路径是否为公开路径
   */
  isPublicPath(pathname: string): boolean {
    const publicPaths = ['/', '/about', '/help', '/landing', '/api']
    return publicPaths.some(path => 
      pathname === path || (path !== '/' && pathname.startsWith(path))
    )
  }
}
