/**
 * 嵌入页面认证逻辑
 */

import { NextRequest } from 'next/server'
import { validateTokenWithDetails } from '../auth/token-validator'
import { EmbedSecurity } from './security'
import { isDebugEnabled } from '../config/environment'
import type { EmbedAuthResult, EmbedContext } from './types'

export class EmbedAuth {
  /**
   * 从请求中提取 token
   */
  static extractToken(request: NextRequest): string | null {
    // 优先从 URL 参数获取 token（用于嵌入页面）
    const urlToken = request.nextUrl.searchParams.get('token')
    if (urlToken && EmbedSecurity.isValidTokenFormat(urlToken)) {
      return urlToken
    }

    // 备选：从 cookie 获取 token
    const cookieToken = request.cookies.get('auth-token')?.value
    if (cookieToken && EmbedSecurity.isValidTokenFormat(cookieToken)) {
      return cookieToken
    }

    // 备选：从 Authorization 头获取 token
    const authHeader = request.headers.get('authorization')
    if (authHeader?.startsWith('Bearer ')) {
      const headerToken = authHeader.slice(7)
      if (EmbedSecurity.isValidTokenFormat(headerToken)) {
        return headerToken
      }
    }

    return null
  }

  /**
   * 验证嵌入页面认证
   */
  static async validateEmbedAuth(request: NextRequest): Promise<EmbedAuthResult> {
    try {
      // 提取 token
      const token = this.extractToken(request)
      if (!token) {
        if (isDebugEnabled()) {
          console.log('[EmbedAuth] No token found in request')
        }
        return {
          success: false,
          error: 'No valid token found'
        }
      }

      if (isDebugEnabled()) {
        console.log('[EmbedAuth] Validating token for embed request')
      }

      // 使用详细验证获取更多信息
      const validationResult = await validateTokenWithDetails(token)

      if (!validationResult.success) {
        if (isDebugEnabled()) {
          console.log(`[EmbedAuth] Token validation failed: ${validationResult.error}`)
        }
        return {
          success: false,
          error: validationResult.error || 'Token validation failed'
        }
      }

      if (isDebugEnabled()) {
        console.log(`[EmbedAuth] Token validation successful for user: ${validationResult.data?.user.email}`)
      }

      return {
        success: true,
        user: validationResult.data?.user
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.error('[EmbedAuth] Authentication error:', errorMessage)
      return {
        success: false,
        error: 'Authentication error'
      }
    }
  }

  /**
   * 构建嵌入上下文
   */
  static async buildEmbedContext(request: NextRequest): Promise<EmbedContext> {
    const referer = request.headers.get('referer')
    const pathname = request.nextUrl.pathname
    const isEmbedded = !!referer && (
      pathname.startsWith('/embed') ||
      pathname.match(/^\/[a-z]{2}\/embed/) !== null
    )

    let parentOrigin: string | undefined = undefined
    let token: string | undefined = undefined
    let user: any = undefined

    if (isEmbedded) {
      // 获取父页面源
      if (referer) {
        try {
          parentOrigin = new URL(referer).origin
        } catch {
          // 忽略无效的 referer
        }
      }

      // 提取 token 和用户信息
      const extractedToken = this.extractToken(request)
      if (extractedToken) {
        token = extractedToken
        const authResult = await this.validateEmbedAuth(request)
        if (authResult.success) {
          user = authResult.user
        }
      }
    }

    return {
      isEmbedded,
      parentOrigin,
      token,
      user
    }
  }

  /**
   * 检查是否为嵌入页面请求
   */
  static isEmbedRequest(request: NextRequest): boolean {
    const pathname = request.nextUrl.pathname
    return pathname.startsWith('/embed') || pathname.match(/^\/[a-z]{2}\/embed/) !== null
  }

  /**
   * 生成嵌入页面 URL
   */
  static generateEmbedUrl(
    basePath: string,
    token: string,
    additionalParams?: Record<string, string>
  ): string {
    const url = new URL(basePath, process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000')
    
    // 添加 token
    url.searchParams.set('token', token)
    
    // 添加其他参数
    if (additionalParams) {
      Object.entries(additionalParams).forEach(([key, value]) => {
        url.searchParams.set(key, value)
      })
    }
    
    return url.toString()
  }

  /**
   * 验证嵌入页面访问权限
   */
  static async validateEmbedAccess(
    request: NextRequest,
    requiredPermissions?: string[]
  ): Promise<{
    allowed: boolean
    reason?: string
    user?: any
  }> {
    try {
      // 验证认证
      const authResult = await this.validateEmbedAuth(request)
      if (!authResult.success) {
        return {
          allowed: false,
          reason: authResult.error || 'Authentication failed'
        }
      }

      // 如果需要特定权限，进行权限检查
      if (requiredPermissions && requiredPermissions.length > 0) {
        // 这里可以根据实际需求实现权限检查逻辑
        // 暂时简化处理
        const hasPermissions = true // 实际应该检查用户权限
        
        if (!hasPermissions) {
          return {
            allowed: false,
            reason: 'Insufficient permissions',
            user: authResult.user
          }
        }
      }

      return {
        allowed: true,
        user: authResult.user
      }
    } catch (error) {
      console.error('Embed access validation error:', error)
      return {
        allowed: false,
        reason: 'Access validation error'
      }
    }
  }

  /**
   * 创建嵌入页面响应
   */
  static createEmbedResponse(
    content: string,
    allowedOrigin: string,
    options?: ResponseInit
  ): Response {
    return EmbedSecurity.createSecureResponse(content, allowedOrigin, options)
  }
}
