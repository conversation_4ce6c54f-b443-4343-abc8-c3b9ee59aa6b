# Page snapshot

```yaml
- img
- text: 模拟流水线运行中... 1
- heading "获取招投标数据" [level=3]
- img
- text: completed
- paragraph: 从多个数据源获取招投标相关信息，包括招标公告、投标文件等关键数据
- img
- text: "Created: 13:08:40 Progress: 1/1 tools"
- progressbar
- heading "Tools & Results" [level=4]
- img
- heading "招投标数据采集器" [level=4]
- text: "Started: 13:08:40 Duration: 0s"
- img
- text: "completed 🚀 招投标数据采集启动 数据源目标: 15个政府采购网站 已处理页面: 1,247 / 1,500 成功率: 89.3% 平均响应时间: 2.1s 当前状态: ✅ 所有API认证成功 ✅ 速率限制已配置 (100 req/min) ✅ 数据验证管道活跃 🔄 正在处理招标公告页面 🔄 提取结构化元数据 ⏳ 剩余时间: ~3分钟 质量指标: - 高质量数据: 78% - 中等质量数据: 18% - 需要人工审核: 4% 下一步: 准备数据丰富管道... 2"
- heading "生成招标摘要" [level=3]
- img
- text: running
- paragraph: 基于采集的招投标数据，使用AI技术生成结构化的招标摘要信息
- img
- text: "Created: 13:08:40 Progress: 1/1 tools"
- progressbar
- heading "Tools & Results" [level=4]
- img
- heading "AI摘要生成引擎" [level=4]
- text: "Started: 13:08:40 Duration: 0s"
- img
- text: completed
- img
- heading "某市政府办公设备采购项目" [level=5]
- paragraph: https://ccgp.gov.cn/cggg/zygg/gkzb/202401/t20240115_19876543.htm
- text: "high active Category: 政府采购 Response: 145ms"
- img
- heading "医疗设备购置招标公告" [level=5]
- paragraph: https://ccgp.gov.cn/cggg/zygg/gkzb/202401/t20240116_19876544.htm
- text: "high active Category: 医疗采购 Response: 230ms"
- img
- heading "教育信息化建设项目" [level=5]
- paragraph: https://ccgp.gov.cn/cggg/zygg/gkzb/202401/t20240117_19876545.htm
- text: "high active Category: 教育采购 Response: 189ms"
- img
- heading "交通基础设施建设招标" [level=5]
- paragraph: https://ccgp.gov.cn/cggg/zygg/gkzb/202401/t20240118_19876546.htm
- text: "medium active Category: 基建项目 Response: 312ms"
- img
- heading "环保设备采购（已截标）" [level=5]
- paragraph: https://ccgp.gov.cn/cggg/zygg/gkzb/202401/t20240119_19876547.htm
- text: "low deprecated Category: 环保采购 Response: 1.2s"
- img
- heading "文化设施建设项目" [level=5]
- paragraph: https://ccgp.gov.cn/cggg/zygg/gkzb/202401/t20240120_19876548.htm
- text: "medium active Category: 文化项目 Response: 267ms"
- region "Notifications alt+T"
- alert
- button "Open Next.js Dev Tools":
  - img
```