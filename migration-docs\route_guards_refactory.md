# 路由守卫系统重构详细设计

## 📋 模块概述

### 重构目标
将Vue Router的5层路由守卫系统完整迁移到Next.js Middleware，保持所有权限控制、业务流程控制和重定向逻辑。

### 工作量估算
- **总工时**: 18小时
- **复杂度**: 极高
- **风险等级**: 高

## 🔍 原系统深度分析

### 原系统架构
```typescript
// 5层守卫系统，按文件名数字顺序执行
src/router/guards/
├── auth.guard.0.ts      // 认证守卫 - 处理登录状态验证
├── access.guard.1.ts    // 访问控制守卫 - 处理权限验证
├── poll.guard.2.ts      // 业务流程守卫 - 处理问卷流程
├── menu.guard.3.ts      // 菜单守卫 - 处理菜单权限
└── feedback.guard.4.ts  // 反馈守卫 - 处理页面重定向

// 守卫加载机制
const modules = import.meta.glob('./guards/*.ts')
const sortedPaths = Object.keys(modules).sort((a, b) => {
  const extractNumber = (path: string) => {
    const parts = path.split('.')
    const numberPart = parts.length > 2 ? parts[parts.length - 2] : '0'
    const number = numberPart.match(/\d+/)
    return number ? parseInt(number[0], 10) : 0
  }
  return extractNumber(a) - extractNumber(b)
})
```

### 守卫详细分析

#### 1. 认证守卫 (auth.guard.0.ts)
```typescript
// 原系统伪代码
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()

  // 检查路由是否需要认证处理
  if (to.meta.requiresAuth || to.meta.requiresGuest || to.meta.tryAuth) {
    const isAuthenticated = await userStore.validateToken()

    // 需要认证但未登录
    if (to.meta.requiresAuth && !isAuthenticated) {
      const redirectPath = to.meta.redirectIfUnauthorized || '/landing'
      redirectToPath(redirectPath, to, from, next, {
        addFromQuery: true,
        addSSORedirectQuery: true
      })
      return
    }

    // 仅限游客但已登录
    if (to.meta.requiresGuest && isAuthenticated) {
      const redirectPath = to.meta.redirectIfUnauthorized || '/'
      redirectToPath(redirectPath, to, from, next, {
        replaceRedirectFromQuery: true
      })
      return
    }
  }

  next()
})

// 路由元数据示例
{
  path: '/workspace',
  meta: {
    requiresAuth: true,
    redirectIfUnauthorized: '/landing'
  }
}
```

#### 2. 访问控制守卫 (access.guard.1.ts)
```typescript
// 原系统伪代码
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  const accessParams = to.meta.access || {}

  // 检查是否需要权限验证
  if (accessParams.fallback?.length ||
      accessParams.features?.length ||
      accessParams.roles?.length ||
      accessParams.permissions?.length ||
      accessParams.rightSchemeType?.length) {

    // 确保用户已认证
    userStore.validateToken()

    // 执行权限检查
    const hasAccess = userStore.checkAccess(accessParams)
    if (!hasAccess) {
      const redirectPath = to.meta.redirectIfNoAccess || '/'
      redirectToPath(redirectPath, to, from, next)
      return
    }
  }

  next()
})

// 复杂权限配置示例
{
  path: '/admin',
  meta: {
    access: {
      features: ['admin_panel'],
      roles: ['admin', 'super_admin'],
      permissions: ['user_management'],
      rightSchemeType: ['enterprise'],
      fallback: ['basic_admin']
    },
    redirectIfNoAccess: '/dashboard'
  }
}
```

#### 3. 业务流程守卫 (poll.guard.2.ts)
```typescript
// 原系统伪代码 - 问卷流程控制
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  const currentStep = userStore.userInfo?.step || 0

  // 策略模式处理不同重定向场景
  const strategies = [
    new CompletedPollRedirect(),    // 已完成问卷的重定向
    new IncompletePollRedirect(),   // 未完成问卷的重定向
  ]

  for (const strategy of strategies) {
    if (strategy.handle(to, from, next, currentStep)) {
      return // 已处理重定向，直接返回
    }
  }

  next() // 无需重定向，继续路由
})

// 问卷流程策略实现
class CompletedPollRedirect {
  handle(to, from, next, stepNum) {
    // 如果用户已完成问卷(step=4)但访问问卷页面，重定向到主页
    if (stepNum === 4 && to.path.startsWith('/poll')) {
      redirectToPath('/', to, from, next)
      return true
    }
    return false
  }
}

class IncompletePollRedirect {
  handle(to, from, next, stepNum) {
    // 如果用户未完成问卷但访问其他页面，重定向到问卷页面
    if (stepNum < 4 && !to.path.startsWith('/poll') && to.path !== '/landing') {
      redirectToPath(`/poll/step/${stepNum + 1}`, to, from, next)
      return true
    }
    return false
  }
}
```

#### 4. 菜单守卫 (menu.guard.3.ts)
```typescript
// 原系统伪代码 - 菜单权限控制
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()

  // 跳过非菜单路由
  if (!isRouteInMenu(to)) {
    return next()
  }

  // 跳过认证相关路由
  if (to.meta.requiresGuest) {
    return next()
  }

  // 检查是否有启用的菜单
  const hasEnabledMenu = userStore.menuConfig.some(menu => menu.status)
  if (!hasEnabledMenu) {
    redirectToPath('/landing', to, from, next, { replaceRedirectFromQuery: true })
    return
  }

  // 查找当前路由对应的菜单ID
  let menuId = routeToMenuMap[to.path]

  // 处理嵌套路由
  if (!menuId) {
    for (const [route, id] of Object.entries(routeToMenuMap)) {
      if (to.path.startsWith(`${route}/`)) {
        menuId = id
        break
      }
    }
  }

  // 检查菜单是否被禁用
  const menuItem = userStore.menuConfig.find(item => item.id === menuId)
  if (!menuItem?.status) {
    const firstAccessibleRoute = getFirstAccessibleRoute(userStore)
    if (firstAccessibleRoute) {
      redirectToPath(firstAccessibleRoute, to, from, next, { replaceRedirectFromQuery: true })
    } else {
      redirectToPath('/landing', to, from, next, { replaceRedirectFromQuery: true })
    }
    return
  }

  next()
})

// 获取第一个可访问的路由
function getFirstAccessibleRoute(userStore) {
  for (const item of menuItems) {
    if (userStore.menuConfig.find(menu => menu.id === item.menuId && menu.status)) {
      return item.route
    }
    if (item.children) {
      for (const child of item.children) {
        if (userStore.menuConfig.find(menu => menu.id === child.menuId && menu.status)) {
          return child.route
        }
      }
    }
  }
  return undefined
}
```

#### 5. 反馈守卫 (feedback.guard.4.ts)
```typescript
// 原系统伪代码 - 简单重定向处理
router.beforeEach(async (to, from, next) => {
  // 特定路径重定向
  if (to.path === '/dashboard') {
    redirectToPath('/workspace', to, from, next, { replaceRedirectFromQuery: true })
  } else {
    next()
  }
})
```

### 重定向工具函数
```typescript
// src/utils/router.ts - 核心重定向逻辑
export function redirectToPath(
  redirectPath: string,
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext,
  options: {
    addFromQuery?: boolean
    addSSORedirectQuery?: boolean
    replaceRedirectFromQuery?: boolean
  } = {}
) {
  const query: Record<string, any> = {}

  // 添加来源查询参数
  if (options.addFromQuery) {
    query.from = to.fullPath
  }

  // 添加SSO重定向参数
  if (options.addSSORedirectQuery) {
    query.redirect = to.fullPath
  }

  // 替换现有重定向参数
  if (options.replaceRedirectFromQuery) {
    if (to.query.redirect) {
      query.redirect = to.query.redirect
    }
  }

  next({
    path: redirectPath,
    query: Object.keys(query).length > 0 ? query : undefined,
    replace: true
  })
}
```

## 🎯 目标架构设计

### Next.js Middleware架构
```typescript
// middleware.ts - 统一中间件入口
import { NextRequest, NextResponse } from 'next/server'
import { createMiddlewareChain } from './lib/middleware/chain'
import { authGuard } from './lib/middleware/auth-guard'
import { accessGuard } from './lib/middleware/access-guard'
import { pollGuard } from './lib/middleware/poll-guard'
import { menuGuard } from './lib/middleware/menu-guard'
import { feedbackGuard } from './lib/middleware/feedback-guard'

// 创建中间件链，按顺序执行
const middlewareChain = createMiddlewareChain([
  authGuard,      // 认证守卫
  accessGuard,    // 访问控制守卫
  pollGuard,      // 业务流程守卫
  menuGuard,      // 菜单守卫
  feedbackGuard,  // 反馈守卫
])

export async function middleware(request: NextRequest) {
  return await middlewareChain(request)
}

export const config = {
  matcher: [
    // 匹配所有路径，除了静态资源和API路由
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
```

### 中间件链实现
```typescript
// lib/middleware/chain.ts - 中间件链管理
type MiddlewareFunction = (
  request: NextRequest,
  next: () => Promise<NextResponse>
) => Promise<NextResponse>

export function createMiddlewareChain(middlewares: MiddlewareFunction[]) {
  return async (request: NextRequest): Promise<NextResponse> => {
    let index = 0

    const next = async (): Promise<NextResponse> => {
      if (index >= middlewares.length) {
        return NextResponse.next()
      }

      const middleware = middlewares[index++]
      return await middleware(request, next)
    }

    return await next()
  }
}
```

### 路由配置系统
```typescript
// lib/route-config.ts - 路由配置管理
interface RouteConfig {
  path: string
  requiresAuth?: boolean
  requiresGuest?: boolean
  tryAuth?: boolean
  redirectIfUnauthorized?: string
  access?: {
    features?: string[]
    roles?: string[]
    permissions?: string[]
    rightSchemeType?: string[]
    fallback?: string[]
  }
  redirectIfNoAccess?: string
  menuId?: string
  keepAlive?: boolean
}

export const routeConfigs: RouteConfig[] = [
  {
    path: '/workspace',
    requiresAuth: true,
    redirectIfUnauthorized: '/landing',
    access: {
      features: ['workspace_access']
    },
    menuId: 'workspace',
    keepAlive: true
  },
  {
    path: '/admin',
    requiresAuth: true,
    access: {
      roles: ['admin', 'super_admin'],
      permissions: ['admin_panel'],
      fallback: ['basic_admin']
    },
    redirectIfNoAccess: '/dashboard',
    menuId: 'admin'
  },
  {
    path: '/login',
    requiresGuest: true,
    redirectIfUnauthorized: '/'
  },
  // ... 更多路由配置
]

// 路由配置查找函数
export function getRouteConfig(pathname: string): RouteConfig | null {
  // 精确匹配
  let config = routeConfigs.find(config => config.path === pathname)

  if (!config) {
    // 前缀匹配（用于动态路由）
    config = routeConfigs.find(config =>
      pathname.startsWith(config.path + '/') ||
      config.path.includes('[') // 动态路由标识
    )
  }

  return config || null
}
```

## 🔧 分步实施计划

### 步骤1: 基础架构搭建 (4小时)

#### 1.1 中间件链系统 (2小时)
```typescript
// lib/middleware/types.ts - 类型定义
export interface GuardContext {
  request: NextRequest
  pathname: string
  searchParams: URLSearchParams
  routeConfig: RouteConfig | null
  user: User | null
  userPermissions: UserPermission[]
  userRoles: UserRole[]
  menuConfig: MenuConfig[]
  featureConfig: FeatureConfig[]
}

export interface GuardResult {
  type: 'continue' | 'redirect' | 'rewrite'
  response?: NextResponse
  url?: string
  query?: Record<string, string>
}

export type GuardFunction = (context: GuardContext) => Promise<GuardResult>
```

#### 1.2 上下文构建器 (2小时)
```typescript
// lib/middleware/context-builder.ts
export async function buildGuardContext(request: NextRequest): Promise<GuardContext> {
  const pathname = request.nextUrl.pathname
  const searchParams = request.nextUrl.searchParams
  const routeConfig = getRouteConfig(pathname)

  // 从cookie或header获取用户信息
  const token = request.cookies.get('auth-token')?.value
  let user: User | null = null
  let userPermissions: UserPermission[] = []
  let userRoles: UserRole[] = []
  let menuConfig: MenuConfig[] = []
  let featureConfig: FeatureConfig[] = []

  if (token && !isTokenExpired(token)) {
    try {
      // 从token解析用户信息或调用API验证
      const authData = await validateTokenOnServer(token)
      if (authData) {
        user = authData.user
        userPermissions = authData.permissions
        userRoles = authData.roles
        menuConfig = authData.menuConfig
        featureConfig = authData.featureConfig
      }
    } catch (error) {
      console.error('Failed to validate token in middleware:', error)
    }
  }

  return {
    request,
    pathname,
    searchParams,
    routeConfig,
    user,
    userPermissions,
    userRoles,
    menuConfig,
    featureConfig
  }
}
```

### 步骤2: 认证守卫实现 (4小时)

#### 2.1 认证守卫核心逻辑 (3小时)
```typescript
// lib/middleware/auth-guard.ts
export async function authGuard(
  request: NextRequest,
  next: () => Promise<NextResponse>
): Promise<NextResponse> {
  const context = await buildGuardContext(request)
  const { routeConfig, user, pathname } = context

  // 如果路由不需要认证处理，直接继续
  if (!routeConfig?.requiresAuth && !routeConfig?.requiresGuest && !routeConfig?.tryAuth) {
    return await next()
  }

  const isAuthenticated = !!user

  // 需要认证但未登录
  if (routeConfig.requiresAuth && !isAuthenticated) {
    const redirectPath = routeConfig.redirectIfUnauthorized || '/landing'
    const redirectUrl = new URL(redirectPath, request.url)

    // 添加来源查询参数
    redirectUrl.searchParams.set('from', pathname)
    redirectUrl.searchParams.set('redirect', pathname)

    console.warn(`Visited ${pathname}, but not authorized, redirect to ${redirectPath}`)
    return NextResponse.redirect(redirectUrl)
  }

  // 仅限游客但已登录
  if (routeConfig.requiresGuest && isAuthenticated) {
    const redirectPath = routeConfig.redirectIfUnauthorized || '/'
    const redirectUrl = new URL(redirectPath, request.url)

    // 保留现有的重定向参数
    const existingRedirect = request.nextUrl.searchParams.get('redirect')
    if (existingRedirect) {
      redirectUrl.searchParams.set('redirect', existingRedirect)
    }

    console.warn(`Visited ${pathname}, but already authorized, redirect to ${redirectPath}`)
    return NextResponse.redirect(redirectUrl)
  }

  // 尝试认证但不强制（tryAuth）
  if (routeConfig.tryAuth && !isAuthenticated) {
    // 可以在这里添加静默认证逻辑
    console.info(`Try auth for ${pathname}, but user not authenticated`)
  }

  return await next()
}
```

#### 2.2 Token验证服务 (1小时)
```typescript
// lib/auth/token-validator.ts
interface AuthData {
  user: User
  permissions: UserPermission[]
  roles: UserRole[]
  menuConfig: MenuConfig[]
  featureConfig: FeatureConfig[]
}

export async function validateTokenOnServer(token: string): Promise<AuthData | null> {
  try {
    // 首先检查token格式和过期时间
    if (isTokenExpired(token)) {
      return null
    }

    // 调用认证API验证token
    const response = await fetch(`${process.env.API_BASE_URL}/user/validate`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      return null
    }

    const data = await response.json()
    return data.success ? data.data : null
  } catch (error) {
    console.error('Token validation failed:', error)
    return null
  }
}

export function isTokenExpired(token: string): boolean {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    return Date.now() >= payload.exp * 1000
  } catch {
    return true
  }
}
```

### 步骤3: 访问控制守卫实现 (6小时)

#### 3.1 权限检查核心逻辑 (4小时)
```typescript
// lib/middleware/access-guard.ts
export async function accessGuard(
  request: NextRequest,
  next: () => Promise<NextResponse>
): Promise<NextResponse> {
  const context = await buildGuardContext(request)
  const { routeConfig, user, userPermissions, userRoles, featureConfig } = context

  // 如果没有访问控制配置，直接继续
  if (!routeConfig?.access) {
    return await next()
  }

  const accessParams = routeConfig.access

  // 确保用户已认证（如果需要权限检查，通常需要认证）
  if (!user) {
    const redirectPath = routeConfig.redirectIfNoAccess || '/'
    return NextResponse.redirect(new URL(redirectPath, request.url))
  }

  // 执行权限检查
  const hasAccess = checkUserAccess(accessParams, {
    userPermissions,
    userRoles,
    featureConfig
  })

  if (!hasAccess) {
    const redirectPath = routeConfig.redirectIfNoAccess || '/'
    console.warn(`No access to ${context.pathname}, redirect to ${redirectPath}`)
    return NextResponse.redirect(new URL(redirectPath, request.url))
  }

  return await next()
}

// 权限检查函数
function checkUserAccess(
  accessParams: NonNullable<RouteConfig['access']>,
  userContext: {
    userPermissions: UserPermission[]
    userRoles: UserRole[]
    featureConfig: FeatureConfig[]
  }
): boolean {
  const { features, roles, permissions, rightSchemeType, fallback } = accessParams
  const { userPermissions, userRoles, featureConfig } = userContext

  // 检查功能权限
  if (features?.length) {
    const hasFeatureAccess = features.some(feature =>
      featureConfig.find(f => f.id === feature && f.status)
    )

    if (!hasFeatureAccess) {
      // 检查是否有fallback选项
      if (fallback?.length) {
        const hasFallbackAccess = fallback.some(fb =>
          featureConfig.find(f => f.id === fb && f.status)
        )
        if (!hasFallbackAccess) return false
      } else {
        return false
      }
    }
  }

  // 检查角色权限
  if (roles?.length) {
    const userRoleCodes = userRoles.map(r => r.roleCode)
    const hasRoleAccess = roles.some(role => userRoleCodes.includes(role))
    if (!hasRoleAccess) return false
  }

  // 检查具体权限
  if (permissions?.length) {
    const userPermissionCodes = userPermissions.map(p => p.code)
    const hasPermissionAccess = permissions.some(permission =>
      userPermissionCodes.includes(permission)
    )
    if (!hasPermissionAccess) return false
  }

  // 检查权限方案类型
  if (rightSchemeType?.length) {
    // 这里需要根据具体业务逻辑实现
    // 例如检查用户的订阅类型、企业版权限等
    const hasRightSchemeAccess = checkRightSchemeType(rightSchemeType, userContext)
    if (!hasRightSchemeAccess) return false
  }

  return true
}
```

#### 3.2 权限方案类型检查 (2小时)
```typescript
// lib/middleware/right-scheme-checker.ts
export function checkRightSchemeType(
  rightSchemeTypes: string[],
  userContext: {
    userPermissions: UserPermission[]
    userRoles: UserRole[]
    featureConfig: FeatureConfig[]
  }
): boolean {
  // 根据业务需求实现具体的权限方案检查
  // 这里是示例实现

  for (const schemeType of rightSchemeTypes) {
    switch (schemeType) {
      case 'enterprise':
        // 检查是否为企业版用户
        if (hasEnterpriseAccess(userContext)) {
          return true
        }
        break

      case 'premium':
        // 检查是否为高级版用户
        if (hasPremiumAccess(userContext)) {
          return true
        }
        break

      case 'basic':
        // 检查是否为基础版用户
        if (hasBasicAccess(userContext)) {
          return true
        }
        break

      default:
        console.warn(`Unknown right scheme type: ${schemeType}`)
    }
  }

  return false
}

function hasEnterpriseAccess(userContext: any): boolean {
  // 实现企业版权限检查逻辑
  return userContext.featureConfig.some(f =>
    f.id === 'enterprise_features' && f.status
  )
}

function hasPremiumAccess(userContext: any): boolean {
  // 实现高级版权限检查逻辑
  return userContext.featureConfig.some(f =>
    f.id === 'premium_features' && f.status
  )
}

function hasBasicAccess(userContext: any): boolean {
  // 基础版通常是默认权限
  return true
}
```

### 步骤4: 业务流程守卫实现 (4小时)

#### 4.1 问卷流程控制 (3小时)
```typescript
// lib/middleware/poll-guard.ts
export async function pollGuard(
  request: NextRequest,
  next: () => Promise<NextResponse>
): Promise<NextResponse> {
  const context = await buildGuardContext(request)
  const { user, pathname } = context

  if (!user) {
    return await next()
  }

  const currentStep = user.step || 0

  // 使用策略模式处理不同的重定向场景
  const strategies = [
    new CompletedPollRedirectStrategy(),
    new IncompletePollRedirectStrategy(),
  ]

  for (const strategy of strategies) {
    const result = strategy.handle(pathname, currentStep, request.url)
    if (result.shouldRedirect) {
      console.info(`Poll guard redirect: ${pathname} -> ${result.redirectPath}`)
      return NextResponse.redirect(new URL(result.redirectPath, request.url))
    }
  }

  return await next()
}

// 策略接口
interface PollRedirectStrategy {
  handle(pathname: string, stepNum: number, baseUrl: string): {
    shouldRedirect: boolean
    redirectPath?: string
  }
}

// 已完成问卷的重定向策略
class CompletedPollRedirectStrategy implements PollRedirectStrategy {
  handle(pathname: string, stepNum: number): {
    shouldRedirect: boolean
    redirectPath?: string
  } {
    // 如果用户已完成问卷(step=4)但访问问卷页面，重定向到主页
    if (stepNum === 4 && pathname.startsWith('/poll')) {
      return {
        shouldRedirect: true,
        redirectPath: '/workspace'
      }
    }

    return { shouldRedirect: false }
  }
}

// 未完成问卷的重定向策略
class IncompletePollRedirectStrategy implements PollRedirectStrategy {
  handle(pathname: string, stepNum: number): {
    shouldRedirect: boolean
    redirectPath?: string
  } {
    // 如果用户未完成问卷但访问其他页面，重定向到问卷页面
    const isAccessingPoll = pathname.startsWith('/poll')
    const isLandingPage = pathname === '/landing'
    const isAuthPage = pathname.startsWith('/login') || pathname.startsWith('/register')

    if (stepNum < 4 && !isAccessingPoll && !isLandingPage && !isAuthPage) {
      return {
        shouldRedirect: true,
        redirectPath: `/poll/step/${stepNum + 1}`
      }
    }

    return { shouldRedirect: false }
  }
}
```

#### 4.2 扩展业务流程支持 (1小时)
```typescript
// lib/middleware/business-flow-strategies.ts
// 可扩展的业务流程策略

// 新用户引导流程
export class OnboardingFlowStrategy implements PollRedirectStrategy {
  handle(pathname: string, stepNum: number): {
    shouldRedirect: boolean
    redirectPath?: string
  } {
    // 新用户引导逻辑
    if (stepNum === 0 && !pathname.startsWith('/onboarding')) {
      return {
        shouldRedirect: true,
        redirectPath: '/onboarding/welcome'
      }
    }

    return { shouldRedirect: false }
  }
}

// 订阅验证流程
export class SubscriptionFlowStrategy implements PollRedirectStrategy {
  handle(pathname: string, stepNum: number): {
    shouldRedirect: boolean
    redirectPath?: string
  } {
    // 订阅验证逻辑
    // 这里可以添加订阅状态检查
    return { shouldRedirect: false }
  }
}
```
```