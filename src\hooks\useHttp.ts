/**
 * 统一HTTP请求Hook
 * 提供标准化的GET、POST、PUT、DELETE等HTTP方法
 * 基于底层的useRequest实现
 */

import { useCallback } from 'react'
import { useRequest, useAutoRequest, type RequestConfig, type RequestState, type RequestExecutor } from './useRequest'

// HTTP方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

// HTTP请求结果接口
export interface HttpRequestResult<T> {
  data: T | null
  loading: boolean
  error: Error | null
  executed: boolean
  execute: RequestExecutor<T>
}

// HTTP自动请求结果接口
export interface HttpAutoRequestResult<T> {
  data: T | null
  loading: boolean
  error: Error | null
  executed: boolean
}

/**
 * 主要的HTTP Hook
 * 提供所有HTTP方法的统一接口
 */
export function useHttp() {
  /**
   * GET请求
   */
  const get = useCallback(<T = any>(
    url: string, 
    config: Omit<RequestConfig, 'method'> = {}
  ): HttpRequestResult<T> => {
    const [state, execute] = useRequest<T>(url, { ...config, method: 'GET' })
    return { ...state, execute }
  }, [])

  /**
   * POST请求
   */
  const post = useCallback(<T = any>(
    url: string, 
    config: Omit<RequestConfig, 'method'> = {}
  ): HttpRequestResult<T> => {
    const [state, execute] = useRequest<T>(url, { ...config, method: 'POST' })
    return { ...state, execute }
  }, [])

  /**
   * PUT请求
   */
  const put = useCallback(<T = any>(
    url: string, 
    config: Omit<RequestConfig, 'method'> = {}
  ): HttpRequestResult<T> => {
    const [state, execute] = useRequest<T>(url, { ...config, method: 'PUT' })
    return { ...state, execute }
  }, [])

  /**
   * DELETE请求
   */
  const del = useCallback(<T = any>(
    url: string, 
    config: Omit<RequestConfig, 'method'> = {}
  ): HttpRequestResult<T> => {
    const [state, execute] = useRequest<T>(url, { ...config, method: 'DELETE' })
    return { ...state, execute }
  }, [])

  /**
   * PATCH请求
   */
  const patch = useCallback(<T = any>(
    url: string, 
    config: Omit<RequestConfig, 'method'> = {}
  ): HttpRequestResult<T> => {
    const [state, execute] = useRequest<T>(url, { ...config, method: 'PATCH' })
    return { ...state, execute }
  }, [])

  return {
    get,
    post,
    put,
    delete: del, // delete是保留字，使用del
    patch
  }
}

/**
 * 便捷的GET请求Hook
 */
export function useGet<T = any>(
  url: string,
  config: Omit<RequestConfig, 'method'> = {}
): HttpRequestResult<T> {
  const [state, execute] = useRequest<T>(url, { ...config, method: 'GET' })
  return { ...state, execute }
}

/**
 * 便捷的POST请求Hook
 */
export function usePost<T = any>(
  url: string,
  config: Omit<RequestConfig, 'method'> = {}
): HttpRequestResult<T> {
  const [state, execute] = useRequest<T>(url, { ...config, method: 'POST' })
  return { ...state, execute }
}

/**
 * 便捷的PUT请求Hook
 */
export function usePut<T = any>(
  url: string,
  config: Omit<RequestConfig, 'method'> = {}
): HttpRequestResult<T> {
  const [state, execute] = useRequest<T>(url, { ...config, method: 'PUT' })
  return { ...state, execute }
}

/**
 * 便捷的DELETE请求Hook
 */
export function useDelete<T = any>(
  url: string,
  config: Omit<RequestConfig, 'method'> = {}
): HttpRequestResult<T> {
  const [state, execute] = useRequest<T>(url, { ...config, method: 'DELETE' })
  return { ...state, execute }
}

/**
 * 自动执行的GET请求Hook
 * 组件挂载时自动执行
 */
export function useAutoGet<T = any>(
  url: string,
  config: Omit<RequestConfig, 'method'> = {},
  deps: React.DependencyList = []
): HttpAutoRequestResult<T> {
  return useAutoRequest<T>(url, { ...config, method: 'GET' }, deps)
}

/**
 * RESTful资源Hook
 * 提供完整的CRUD操作
 */
export function useResource<T = any>(baseUrl: string) {
  const http = useHttp()

  return {
    // 获取列表
    list: (config?: Omit<RequestConfig, 'method'>) => 
      http.get<T[]>(baseUrl, config),
    
    // 获取单个资源
    get: (id: string | number, config?: Omit<RequestConfig, 'method'>) => 
      http.get<T>(`${baseUrl}/${id}`, config),
    
    // 创建资源
    create: (config?: Omit<RequestConfig, 'method'>) => 
      http.post<T>(baseUrl, config),
    
    // 更新资源
    update: (id: string | number, config?: Omit<RequestConfig, 'method'>) => 
      http.put<T>(`${baseUrl}/${id}`, config),
    
    // 删除资源
    remove: (id: string | number, config?: Omit<RequestConfig, 'method'>) => 
      http.delete<void>(`${baseUrl}/${id}`, config)
  }
}

/**
 * 预配置的HTTP Hooks
 * 提供常用的配置组合
 */
export const useHttpPresets = {
  // 静默请求（不显示任何提示）
  silent: {
    get: <T = any>(url: string, config: Omit<RequestConfig, 'method' | 'showError' | 'showSuccess'> = {}) =>
      useGet<T>(url, { ...config, showError: false, showSuccess: false }),
    
    post: <T = any>(url: string, config: Omit<RequestConfig, 'method' | 'showError' | 'showSuccess'> = {}) =>
      usePost<T>(url, { ...config, showError: false, showSuccess: false })
  },

  // 保存操作（显示成功和错误提示）
  save: {
    post: <T = any>(url: string, config: Omit<RequestConfig, 'method' | 'showError' | 'showSuccess'> = {}) =>
      usePost<T>(url, { 
        ...config, 
        showError: true, 
        showSuccess: true,
        successMessage: config.successMessage || 'Save successful',
        errorMessage: config.errorMessage || 'Save failed'
      }),
    
    put: <T = any>(url: string, config: Omit<RequestConfig, 'method' | 'showError' | 'showSuccess'> = {}) =>
      usePut<T>(url, { 
        ...config, 
        showError: true, 
        showSuccess: true,
        successMessage: config.successMessage || 'Update successful',
        errorMessage: config.errorMessage || 'Update failed'
      })
  },

  // 删除操作
  delete: <T = any>(url: string, config: Omit<RequestConfig, 'method' | 'showError' | 'showSuccess'> = {}) =>
    useDelete<T>(url, { 
      ...config, 
      showError: true, 
      showSuccess: true,
      successMessage: config.successMessage || 'Delete successful',
      errorMessage: config.errorMessage || 'Delete failed'
    }),

  // 认证相关（跳过认证）
  auth: {
    post: <T = any>(url: string, config: Omit<RequestConfig, 'method' | 'skipAuth'> = {}) =>
      usePost<T>(url, { ...config, skipAuth: true })
  }
}
