'use client'

/**
 * 语言切换组件
 * 支持中文、英文、日文三种语言切换
 */

import { useState } from 'react'
import { Languages, Check } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { locales, localeNames, type Locale, getUserLocale } from '@/lib/i18n/config'
import { useAuth } from '@/hooks/use-auth-selectors'

interface LanguageSwitcherProps {
  variant?: 'default' | 'ghost' | 'outline'
  size?: 'default' | 'sm' | 'lg'
  showText?: boolean
}

export function LanguageSwitcher({ 
  variant = 'ghost', 
  size = 'default',
  showText = false
}: LanguageSwitcherProps) {
  const { user } = useAuth()
  
  // 从用户设置获取当前语言，如果没有则使用默认语言
  const currentLocale = getUserLocale(user?.language)
  const [selectedLocale, setSelectedLocale] = useState<Locale>(currentLocale)

  /**
   * 处理语言切换
   */
  const handleLanguageChange = async (locale: Locale) => {
    try {
      setSelectedLocale(locale)
      
      // TODO: 这里需要调用API更新用户的语言偏好
      // const backendLanguage = getBackendLanguage(locale)
      // await updateUserLanguage(backendLanguage)
      
      // 刷新页面以应用新语言（简单实现）
      // 在实际应用中，可以使用更优雅的方式，比如next-intl的动态语言切换
      window.location.reload()
      
    } catch (error) {
      console.error('Failed to change language:', error)
      // 恢复到之前的语言
      setSelectedLocale(currentLocale)
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={variant} size={size} className="gap-2">
          <Languages className="h-4 w-4" />
          {showText && (
            <span className="hidden sm:inline">
              {localeNames[selectedLocale]}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-40">
        {locales.map((locale) => (
          <DropdownMenuItem
            key={locale}
            onClick={() => handleLanguageChange(locale)}
            className="flex items-center justify-between"
          >
            <span>{localeNames[locale]}</span>
            {selectedLocale === locale && (
              <Check className="h-4 w-4 text-primary" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

/**
 * 简化版语言切换器（仅显示图标）
 */
export function LanguageSwitcherIcon() {
  return <LanguageSwitcher variant="ghost" size="sm" showText={false} />
}

/**
 * 带文本的语言切换器
 */
export function LanguageSwitcherWithText() {
  return <LanguageSwitcher variant="outline" size="default" showText={true} />
} 