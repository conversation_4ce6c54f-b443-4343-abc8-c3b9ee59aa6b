"use client"

import React from 'react'
import {
  Button,
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
  CardFooter,
  Input,
  Label,
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/components/ui'
import MarkdownContainer from '@/components/markdown-container'

// 示例 Markdown 内容 - 展示所有插件功能
const DEMO_MARKDOWN = `# 增强版 Markdown 渲染器演示

这是一个集成了 **11个 markdown-it 插件** 的企业级 Markdown 渲染组件。

## 🚀 核心特性

### 1. 基础语法支持
- **粗体文本** 和 *斜体文本*
- ~~删除线文本~~
- \`行内代码\`
- [链接示例](https://github.com/markdown-it/markdown-it)

### 2. 代码高亮 (highlight.js)

支持 180+ 编程语言的语法高亮：

\`\`\`javascript
// React Hook 示例
import { useState, useEffect } from 'react'

function useMarkdown(content) {
  const [html, setHtml] = useState('')
  const [loading, setLoading] = useState(false)
  
  useEffect(() => {
    setLoading(true)
    // 异步渲染 markdown
    renderMarkdown(content).then(result => {
      setHtml(result.html)
      setLoading(false)
    })
  }, [content])
  
  return { html, loading }
}
\`\`\`

\`\`\`python
# Python 代码示例
def fibonacci(n):
    """计算斐波那契数列"""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# 使用生成器优化
def fib_generator():
    a, b = 0, 1
    while True:
        yield a
        a, b = b, a + b
\`\`\`

## 3. 📝 插件功能展示

### 缩略语 (markdown-it-abbr)
*[HTML]: HyperText Markup Language
*[W3C]: World Wide Web Consortium

HTML 是由 W3C 制定的标准。

### 上标和下标 (markdown-it-sup, markdown-it-sub)
- E = mc^2^
- H~2~O 是水的化学分子式

### 插入和标记文本 (markdown-it-ins, markdown-it-mark)
- ++插入的文本++
- ==高亮标记的文本==

### 表情符号 (markdown-it-emoji)
- :smile: :heart: :rocket: :fire:
- :thumbsup: :clap: :tada: :sparkles:

### 脚注 (markdown-it-footnote)
这里有一个脚注[^1]，还有另一个脚注[^note]。

[^1]: 这是第一个脚注的内容。
[^note]: 这是命名脚注，可以包含更复杂的内容。

### 定义列表 (markdown-it-deflist)
苹果
: 一种常见的水果
: 通常是红色或绿色的

Markdown
: 轻量级标记语言
: 易于阅读和编写

### 任务列表 (markdown-it-task-lists)
- [x] 完成 markdown-it 基础配置
- [x] 集成代码高亮功能
- [x] 添加所有插件支持
- [ ] 优化性能和用户体验
- [ ] 添加主题切换功能

### 容器 (markdown-it-container)

::: info
这是一个信息容器，用于显示重要的提示信息。
:::

::: warning
这是一个警告容器，用于显示需要注意的内容。
:::

::: danger
这是一个危险容器，用于显示严重的警告信息。
:::

### 高级表格功能

| 功能 | 状态 | 优先级 | 负责人 |
|------|------|--------|--------|
| 代码高亮 | ✅ 完成 | 高 | @developer |
| 表情符号 | ✅ 完成 | 中 | @designer |
| 脚注支持 | ✅ 完成 | 低 | @writer |
| 容器样式 | 🚧 进行中 | 高 | @frontend |

## 4. 🎨 样式特性

### 响应式设计
- 📱 移动设备优化
- 💻 桌面端完美显示
- 🖥️ 大屏幕适配

### 主题支持
- 🌞 明亮主题
- 🌙 深色主题
- 🎨 自定义主题

### 性能优化
- ⚡ 懒加载支持
- 🗜️ 代码压缩
- 📦 模块化加载
- 🚀 快速渲染

## 5. 🔧 技术规格

### 插件列表
1. **markdown-it-abbr** - 缩略语支持
2. **markdown-it-sup** - 上标文本
3. **markdown-it-sub** - 下标文本
4. **markdown-it-ins** - 插入文本
5. **markdown-it-mark** - 标记文本
6. **markdown-it-emoji** - 表情符号
7. **markdown-it-footnote** - 脚注支持
8. **markdown-it-deflist** - 定义列表
9. **markdown-it-task-lists** - 任务列表
10. **markdown-it-container** - 自定义容器
11. **highlight.js** - 代码语法高亮

### 浏览器兼容性
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

### 安全特性
- 🛡️ XSS 防护：内置 HTML 清理
- 🔒 CSP 兼容：支持内容安全策略
- 🚫 脚本阻止：禁用不安全的 HTML 标签

---

*最后更新时间：${new Date().toLocaleString('zh-CN')}*`

function MarkdownDemoPage() {
  return (
    <div className="h-full bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-4">
     <MarkdownContainer markdown={DEMO_MARKDOWN} />
    </div>
  )
} 

export default function UIGalleryPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto space-y-12">
        {/* Page Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
            UI Component Gallery
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Test and debug all UI components for style verification
          </p>
        </div>

        {/* Button Components */}
        <ComponentSection title="Button Components">
          <div className="space-y-6">
            {/* Button Variants */}
            <div>
              <h4 className="font-medium mb-3">Button Variants</h4>
              <div className="flex flex-wrap gap-4">
                <Button variant="default">Default</Button>
                <Button variant="secondary">Secondary</Button>
                <Button variant="outline">Outline</Button>
                <Button variant="ghost">Ghost</Button>
                <Button variant="destructive">Destructive</Button>
                <Button variant="link">Link</Button>
              </div>
            </div>
            
            {/* Button Sizes */}
            <div>
              <h4 className="font-medium mb-3">Button Sizes</h4>
              <div className="flex flex-wrap gap-4 items-center">
                <Button size="sm">Small</Button>
                <Button size="default">Default</Button>
                <Button size="lg">Large</Button>
                <Button size="icon">📄</Button>
              </div>
            </div>

            {/* Button States */}
            <div>
              <h4 className="font-medium mb-3">Button States</h4>
              <div className="flex flex-wrap gap-4">
                <Button disabled>Disabled</Button>
                <Button variant="outline" disabled>Disabled Outline</Button>
                <Button variant="destructive" disabled>Disabled Destructive</Button>
              </div>
            </div>
          </div>
        </ComponentSection>

        {/* Input Components */}
        <ComponentSection title="Input Components">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl">
            <div className="space-y-4">
              <div>
                <Label htmlFor="text-input">Text Input</Label>
                <Input id="text-input" type="text" placeholder="Enter text..." />
              </div>
              <div>
                <Label htmlFor="email-input">Email Input</Label>
                <Input id="email-input" type="email" placeholder="Enter email..." />
              </div>
              <div>
                <Label htmlFor="password-input">Password Input</Label>
                <Input id="password-input" type="password" placeholder="Enter password..." />
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <Label htmlFor="disabled-input">Disabled Input</Label>
                <Input id="disabled-input" type="text" placeholder="Disabled..." disabled />
              </div>
              <div>
                <Label htmlFor="readonly-input">Readonly Input</Label>
                <Input id="readonly-input" type="text" value="Readonly value" readOnly />
              </div>
              <div>
                <Label htmlFor="number-input">Number Input</Label>
                <Input id="number-input" type="number" placeholder="Enter number..." />
              </div>
            </div>
          </div>
        </ComponentSection>

        {/* Card Components */}
        <ComponentSection title="Card Components">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Basic Card */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Card</CardTitle>
                <CardDescription>A simple card with basic content</CardDescription>
              </CardHeader>
              <CardContent>
                <p>This is the main content area of the card. You can place any content here.</p>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">Action</Button>
              </CardFooter>
            </Card>

            {/* Card with Avatar */}
            <Card>
              <CardHeader>
                <CardTitle>Profile Card</CardTitle>
                <CardDescription>Card with user profile information</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4">
                  <Avatar>
                    <AvatarImage src="/api/placeholder/40/40" alt="User" />
                    <AvatarFallback>JD</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">John Doe</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Software Developer</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Stats Card */}
            <Card>
              <CardHeader>
                <CardTitle>Statistics</CardTitle>
                <CardDescription>Performance metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-600">2,847</div>
                <p className="text-sm text-gray-600 dark:text-gray-400">+15.3% from last month</p>
              </CardContent>
            </Card>
          </div>
        </ComponentSection>

        {/* Markdown Demo Section */}
        <ComponentSection title="Markdown Renderer Demo">
          <MarkdownDemoPage />
        </ComponentSection>
      </div>
    </div>
  )
}

function ComponentSection({ title, children }: { title: string; children: React.ReactNode }) {
  return (
    <section className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
        {title}
      </h2>
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        {children}
      </div>
    </section>
  )
} 
