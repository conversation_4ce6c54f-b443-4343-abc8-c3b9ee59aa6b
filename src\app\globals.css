@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));
@import 'highlight.js/styles/github.css';
@plugin "@tailwindcss/typography";
:root {
  /* Border Radius */
  --radius: 0.5rem;

  /* Light Theme - Minimalist AI Design */
  --background: oklch(0.99 0.005 267);
  --foreground: oklch(0.15 0.02 267);

  --card: oklch(0.98 0.01 267);
  --card-foreground: oklch(0.15 0.02 267);

  --popover: oklch(0.99 0.005 267);
  --popover-foreground: oklch(0.15 0.02 267);

  /* Primary: Your specified color */
  --primary: oklch(0.8371 0.0649 267.5453);
  --primary-foreground: oklch(0.05 0.02 267);

  /* Secondary: Your specified color */
  --secondary: oklch(0.9486 0.0228 266.9082);
  --secondary-foreground: oklch(0.2 0.04 267);

  --muted: oklch(0.96 0.015 267);
  --muted-foreground: oklch(0.5 0.02 267);

  --accent: oklch(0.94 0.02 267);
  --accent-foreground: oklch(0.2 0.03 267);

  --destructive: oklch(0.65 0.2 15);
  --destructive-foreground: oklch(0.98 0.01 15);

  --border: oklch(0.9 0.015 267);
  --input: oklch(0.96 0.015 267);
  --ring: oklch(0.8371 0.0649 267.5453);

  /* Chart Colors */
  --chart-1: oklch(0.8371 0.0649 267.5453);
  --chart-2: oklch(0.75 0.08 240);
  --chart-3: oklch(0.7 0.1 200);
  --chart-4: oklch(0.8 0.06 300);
  --chart-5: oklch(0.85 0.05 180);

  /* Sidebar */
  --sidebar: oklch(0.98 0.01 267);
  --sidebar-foreground: oklch(0.15 0.02 267);
  --sidebar-primary: oklch(0.8371 0.0649 267.5453);
  --sidebar-primary-foreground: oklch(0.05 0.02 267);
  --sidebar-accent: oklch(0.94 0.02 267);
  --sidebar-accent-foreground: oklch(0.2 0.03 267);
  --sidebar-border: oklch(0.9 0.015 267);
  --sidebar-ring: oklch(0.8371 0.0649 267.5453);
}

.dark {
  /* Dark Theme - Futuristic AI Design */
  --background: oklch(0.08 0.02 267);
  --foreground: oklch(0.95 0.01 267);

  --card: oklch(0.12 0.025 267);
  --card-foreground: oklch(0.95 0.01 267);

  --popover: oklch(0.1 0.02 267);
  --popover-foreground: oklch(0.95 0.01 267);

  /* Primary: Enhanced for dark mode */
  --primary: oklch(0.8371 0.0649 267.5453);
  --primary-foreground: oklch(0.05 0.02 267);

  /* Secondary: Adapted for dark mode */
  --secondary: oklch(0.25 0.04 267);
  --secondary-foreground: oklch(0.9 0.015 267);

  --muted: oklch(0.18 0.025 267);
  --muted-foreground: oklch(0.65 0.02 267);

  --accent: oklch(0.22 0.035 267);
  --accent-foreground: oklch(0.9 0.015 267);

  --destructive: oklch(0.7 0.25 15);
  --destructive-foreground: oklch(0.95 0.01 15);

  --border: oklch(0.25 0.025 267);
  --input: oklch(0.15 0.025 267);
  --ring: oklch(0.8371 0.0649 267.5453);

  /* Chart Colors - Enhanced for dark mode */
  --chart-1: oklch(0.8371 0.0649 267.5453);
  --chart-2: oklch(0.8 0.1 240);
  --chart-3: oklch(0.75 0.12 200);
  --chart-4: oklch(0.85 0.08 300);
  --chart-5: oklch(0.9 0.07 180);

  /* Sidebar - Dark mode */
  --sidebar: oklch(0.12 0.025 267);
  --sidebar-foreground: oklch(0.95 0.01 267);
  --sidebar-primary: oklch(0.8371 0.0649 267.5453);
  --sidebar-primary-foreground: oklch(0.05 0.02 267);
  --sidebar-accent: oklch(0.22 0.035 267);
  --sidebar-accent-foreground: oklch(0.9 0.015 267);
  --sidebar-border: oklch(0.25 0.025 267);
  --sidebar-ring: oklch(0.8371 0.0649 267.5453);
}

@theme inline {
  /* Radius Utilities */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* Color Mappings for Tailwind */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border;
    outline: var(--ring);
    outline-width: 1px;
    outline-offset: 2px;
  }

  body {
    @apply bg-background text-foreground;
  }
}

