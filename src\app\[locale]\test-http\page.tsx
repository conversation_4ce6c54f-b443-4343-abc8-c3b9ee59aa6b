/**
 * HTTP请求系统测试页面
 * 用于验证新的useHttp系统的功能
 */

'use client'

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useHttp, useHttpPresets } from '@/hooks/useHttp'
import { useAuthHttp } from '@/hooks/useAuthHttp'

interface TestResult {
  id: number
  name: string
  status: 'idle' | 'loading' | 'success' | 'error'
  data?: any
  error?: string
  timestamp?: string
}

export default function TestHttpPage() {
  const [results, setResults] = useState<TestResult[]>([])
  
  // 初始化HTTP hooks
  const http = useHttp()
  const { login } = useAuthHttp.useLogin()
  const getCurrentUser = useAuthHttp.useGetCurrentUser()

  // 添加测试结果
  const addResult = (name: string, status: TestResult['status'], data?: any, error?: string) => {
    const result: TestResult = {
      id: Date.now(),
      name,
      status,
      data,
      error,
      timestamp: new Date().toLocaleTimeString()
    }
    setResults(prev => [result, ...prev.slice(0, 9)]) // 保留最新10条
    return result.id
  }

  // 更新测试结果
  const updateResult = (id: number, updates: Partial<TestResult>) => {
    setResults(prev => prev.map(result => 
      result.id === id ? { ...result, ...updates } : result
    ))
  }

  // 测试用例
  const testCases = [
    {
      name: '获取当前用户 (useHttp.get)',
      description: '测试GET请求，应该自动添加language和authorization头',
      action: async () => {
        const id = addResult('GET /api/user/get_current', 'loading')
        try {
          const { execute } = http.get('/api/user/get_current')
          const data = await execute()
          updateResult(id, { status: 'success', data })
        } catch (error) {
          updateResult(id, { 
            status: 'error', 
            error: error instanceof Error ? error.message : 'Unknown error' 
          })
        }
      }
    },
    {
      name: '登录请求 (useAuthHttp)',
      description: '测试认证相关POST请求，应该跳过authorization头',
      action: async () => {
        const id = addResult('POST /api/user/login', 'loading')
        try {
          await login('password', { username: 'test', password: 'test' })
          updateResult(id, { status: 'success', data: 'Login attempted' })
        } catch (error) {
          updateResult(id, { 
            status: 'error', 
            error: error instanceof Error ? error.message : 'Unknown error' 
          })
        }
      }
    },
    {
      name: '静默GET请求 (useHttpPresets.silent)',
      description: '测试静默请求，不显示错误提示',
      action: async () => {
        const id = addResult('Silent GET /api/test', 'loading')
        try {
          const { execute } = useHttpPresets.silent.get('/api/test')
          const data = await execute()
          updateResult(id, { status: 'success', data })
        } catch (error) {
          updateResult(id, { 
            status: 'error', 
            error: error instanceof Error ? error.message : 'Unknown error' 
          })
        }
      }
    },
    {
      name: '保存操作 (useHttpPresets.save)',
      description: '测试保存操作，应该显示成功/失败提示',
      action: async () => {
        const id = addResult('Save POST /api/save', 'loading')
        try {
          const { execute } = useHttpPresets.save.post('/api/save')
          const data = await execute({ body: { test: 'data' } })
          updateResult(id, { status: 'success', data })
        } catch (error) {
          updateResult(id, { 
            status: 'error', 
            error: error instanceof Error ? error.message : 'Unknown error' 
          })
        }
      }
    },
    {
      name: '直接使用getCurrentUser Hook',
      description: '测试专用的认证Hook',
      action: async () => {
        const id = addResult('useGetCurrentUser Hook', 'loading')
        try {
          const data = await getCurrentUser.execute()
          updateResult(id, { status: 'success', data })
        } catch (error) {
          updateResult(id, { 
            status: 'error', 
            error: error instanceof Error ? error.message : 'Unknown error' 
          })
        }
      }
    }
  ]

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">🚀 useHttp系统测试</h1>
        <p className="text-gray-600">验证新的HTTP请求系统的功能</p>
      </div>

      {/* 系统状态 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            📊 系统状态
            <Badge variant="default">useHttp系统</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="font-medium">请求系统</div>
              <div className="text-green-600">✅ useHttp</div>
            </div>
            <div>
              <div className="font-medium">认证Hook</div>
              <div className="text-green-600">✅ useAuthHttp</div>
            </div>
            <div>
              <div className="font-medium">预设配置</div>
              <div className="text-green-600">✅ useHttpPresets</div>
            </div>
            <div>
              <div className="font-medium">自动请求头</div>
              <div className="text-green-600">✅ language + auth</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 测试用例 */}
      <Card>
        <CardHeader>
          <CardTitle>🧪 测试用例</CardTitle>
          <CardDescription>点击按钮执行不同的HTTP请求测试</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {testCases.map((testCase, index) => (
              <div key={index} className="border rounded-lg p-4">
                <h3 className="font-medium mb-2">{testCase.name}</h3>
                <p className="text-sm text-gray-600 mb-3">{testCase.description}</p>
                <Button 
                  onClick={testCase.action}
                  variant="outline"
                  size="sm"
                  className="w-full"
                >
                  执行测试
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 测试结果 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            📝 测试结果
            <Button 
              onClick={() => setResults([])}
              variant="outline"
              size="sm"
            >
              清空结果
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {results.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              暂无测试结果，点击上方测试按钮开始测试
            </div>
          ) : (
            <div className="space-y-3">
              {results.map((result) => (
                <div key={result.id} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                        {result.name}
                      </code>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge 
                        variant={
                          result.status === 'success' ? 'default' : 
                          result.status === 'error' ? 'destructive' : 
                          result.status === 'loading' ? 'secondary' :
                          'outline'
                        }
                      >
                        {result.status}
                      </Badge>
                      {result.timestamp && (
                        <span className="text-xs text-gray-500">{result.timestamp}</span>
                      )}
                    </div>
                  </div>
                  
                  {/* 成功数据 */}
                  {result.data && (
                    <div className="text-sm mt-2">
                      <div className="font-medium mb-1">响应数据:</div>
                      <div className="bg-green-50 p-2 rounded text-xs font-mono max-h-32 overflow-auto">
                        {typeof result.data === 'string' ? result.data : JSON.stringify(result.data, null, 2)}
                      </div>
                    </div>
                  )}
                  
                  {/* 错误信息 */}
                  {result.error && (
                    <div className="text-sm mt-2">
                      <div className="font-medium mb-1">错误信息:</div>
                      <div className="bg-red-50 p-2 rounded text-xs font-mono text-red-600">
                        {result.error}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
