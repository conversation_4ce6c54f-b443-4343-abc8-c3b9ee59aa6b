import { ImageResponse } from 'next/og'
// import fs from 'fs'
// import path from 'path'

// 图片元数据
export const size = {
  width: 32,
  height: 32,
}
export const contentType = 'image/png'

// 图标生成器
export default function Icon() {
  return new ImageResponse(
    (
      <div
        style={{
          fontSize: 24,
          background: 'linear-gradient(to bottom right, #3751FE, #7966FF)',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          borderRadius: '20%',
          fontWeight: 'bold',
        }}
      >
        Vibe
      </div>
    ),
    {
      ...size,
    }
  )
}
