# Vibe Coding Iframe 集成指南

## 📋 概述

本文档为外部服务提供完整的iframe集成指南，用于在第三方应用中嵌入Vibe Coding的功能页面。

## 🚀 快速开始

### 基本iframe嵌入

```html
<iframe 
  src="https://your-domain.com/zh/embed/dashboard?token=YOUR_JWT_TOKEN"
  width="100%" 
  height="600px"
  frameborder="0"
  style="border: none; border-radius: 8px;"
  allow="clipboard-read; clipboard-write"
>
</iframe>
```

## 🔧 URL 参数配置

### 必需参数

#### `token` (必需)

- **描述**: JWT认证令牌
- **格式**: `?token=YOUR_JWT_TOKEN`
- **示例**: `?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### 可选参数

#### `from-og` (可选)

- **描述**: 标识来自Vue3旧项目的iframe
- **格式**: `?from-og=true`
- **用途**: 用于兼容性处理和特殊认证模式

#### `locale` (可选)

- **描述**: 界面语言设置
- **支持语言**: `zh` (中文), `en` (英文)
- **默认值**: `zh`
- **示例**: `/en/embed/dashboard` (英文界面)

## 📄 可用页面

### 1. Dashboard 页面

- **路径**: `/[locale]/embed/dashboard`
- **功能**: 主要仪表板界面
- **用途**: 数据概览、流水线管理

```html
<iframe src="https://your-domain.com/zh/embed/dashboard?token=YOUR_TOKEN"></iframe>
```

### 2. Report 页面

- **路径**: `/[locale]/embed/report`
- **功能**: 报告和分析界面
- **用途**: 数据报告、统计分析

```html
<iframe src="https://your-domain.com/zh/embed/report?token=YOUR_TOKEN"></iframe>
```

### 3. Test 页面

- **路径**: `/[locale]/embed/test`
- **功能**: 测试和调试界面
- **用途**: 功能测试、集成验证

```html
<iframe src="https://your-domain.com/zh/embed/test?token=YOUR_TOKEN"></iframe>
```

## 🔐 认证机制

### JWT Token 要求

#### Token 格式

```json
{
  "user_id": "用户ID",
  "company_id": "公司ID", 
  "email": "用户邮箱",
  "user_type": 2,
  "exp": 1750764281
}
```

#### Token 获取

1. 通过您的后端服务调用Vibe Coding API获取token
2. 确保token具有访问embed页面的权限
3. Token应包含必要的用户信息和权限

### 认证流程

1. iframe加载时从URL参数获取token
2. 自动调用`/api/user/get_current`验证token
3. 验证成功后渲染页面内容
4. 验证失败显示认证错误页面

## 📡 PostMessage 通信

iframe会向父窗口发送以下消息：

### 页面生命周期消息

#### `PAGE_LOADED`

```javascript
{
  type: 'PAGE_LOADED',
  data: {
    timestamp: 1640995200000,
    url: 'https://your-domain.com/zh/embed/dashboard',
    fromOg: false,
    authMode: 'nextjs-standard'
  }
}
```

#### `IFRAME_READY`

```javascript
{
  type: 'IFRAME_READY', 
  data: {
    timestamp: '2025-01-27T10:30:00.000Z',
    url: 'https://your-domain.com/zh/embed/dashboard',
    fromOg: false,
    authMode: 'nextjs-standard',
    message: 'Next.js标准iframe就绪，用户认证完成'
  }
}
```

### API 调用消息

#### `API_CALL`

```javascript
{
  type: 'API_CALL',
  data: {
    method: 'GET',
    url: '/api/user/get_current',
    timestamp: '2025-01-27T10:30:00.000Z',
    token: '已设置'
  }
}
```

#### `API_SUCCESS`

```javascript
{
  type: 'API_SUCCESS',
  data: {
    url: '/api/user/get_current',
    message: '用户信息获取成功',
    user: 'username'
  }
}
```

#### `API_ERROR`

```javascript
{
  type: 'API_ERROR',
  data: {
    url: '/api/user/get_current', 
    message: '获取用户信息失败',
    tokenStatus: '已设置'
  }
}
```

### 高度调整消息

#### `RESIZE`

```javascript
{
  type: 'RESIZE',
  data: {
    height: 800
  }
}
```

### 错误消息

#### `AUTH_ERROR`

```javascript
{
  type: 'AUTH_ERROR',
  data: {
    message: '未提供有效的认证token',
    tokenStatus: '未设置',
    fromOg: false
  }
}
```

#### `ERROR`

```javascript
{
  type: 'ERROR',
  data: {
    message: 'JavaScript error message',
    filename: 'script.js',
    lineno: 123,
    colno: 45
  }
}
```

## 💻 JavaScript 集成示例

### 基本集成

```html
<!DOCTYPE html>
<html>
<head>
    <title>Vibe Coding 集成示例</title>
    <style>
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="iframe-container">
        <iframe 
            id="vibe-iframe"
            src="https://your-domain.com/zh/embed/dashboard?token=YOUR_TOKEN"
            allow="clipboard-read; clipboard-write">
        </iframe>
    </div>

    <script>
        // 监听iframe消息
        window.addEventListener('message', function(event) {
            // 验证消息来源
            if (event.origin !== 'https://your-domain.com') {
                return;
            }
            
            const { type, data } = event.data;
            
            switch (type) {
                case 'IFRAME_READY':
                    console.log('Iframe已就绪:', data);
                    break;
                    
                case 'RESIZE':
                    // 自动调整iframe高度
                    const iframe = document.getElementById('vibe-iframe');
                    iframe.style.height = data.height + 'px';
                    break;
                    
                case 'API_ERROR':
                case 'AUTH_ERROR':
                    console.error('认证错误:', data);
                    // 处理认证错误，如重新获取token
                    break;
                    
                case 'ERROR':
                    console.error('iframe错误:', data);
                    break;
                    
                default:
                    console.log('收到消息:', type, data);
            }
        });
    </script>
</body>
</html>
```

### 高级集成（React示例）

```jsx
import React, { useEffect, useRef, useState } from 'react';

const VibeIframe = ({ token, page = 'dashboard', locale = 'zh' }) => {
  const iframeRef = useRef(null);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const handleMessage = (event) => {
      // 验证消息来源
      if (event.origin !== 'https://your-domain.com') {
        return;
      }

      const { type, data } = event.data;

      switch (type) {
        case 'IFRAME_READY':
          setIsReady(true);
          setError(null);
          console.log('Vibe iframe已就绪');
          break;

        case 'RESIZE':
          if (iframeRef.current) {
            iframeRef.current.style.height = `${data.height}px`;
          }
          break;

        case 'AUTH_ERROR':
        case 'API_ERROR':
          setError(data.message);
          break;

        case 'ERROR':
          console.error('Iframe错误:', data);
          break;
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  const iframeSrc = `https://your-domain.com/${locale}/embed/${page}?token=${token}`;

  return (
    <div style={{ width: '100%', minHeight: '600px', position: 'relative' }}>
      {error && (
        <div style={{ 
          position: 'absolute', 
          top: 0, 
          left: 0, 
          right: 0, 
          background: '#f8d7da', 
          color: '#721c24', 
          padding: '10px',
          borderRadius: '4px',
          zIndex: 10
        }}>
          错误: {error}
        </div>
      )}
      
      {!isReady && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 5
        }}>
          加载中...
        </div>
      )}

      <iframe
        ref={iframeRef}
        src={iframeSrc}
        style={{
          width: '100%',
          height: '600px',
          border: 'none',
          borderRadius: '8px',
          opacity: isReady ? 1 : 0.5
        }}
        allow="clipboard-read; clipboard-write"
        title="Vibe Coding Embed"
      />
    </div>
  );
};

export default VibeIframe;
```

## 🎨 样式定制

### 推荐CSS样式

```css
.vibe-iframe-container {
  width: 100%;
  height: 600px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.vibe-iframe-container iframe {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vibe-iframe-container {
    height: 500px;
    border-radius: 4px;
  }
}
```

### 高度自适应

iframe会自动发送`RESIZE`消息，建议监听并动态调整高度：

```javascript
window.addEventListener('message', function(event) {
  if (event.data.type === 'RESIZE') {
    const iframe = document.getElementById('your-iframe-id');
    iframe.style.height = event.data.data.height + 'px';
  }
});
```

## ⚠️ 注意事项

### 安全考虑

1. **验证消息来源**: 始终验证`event.origin`
2. **Token安全**: 不要在客户端暴露敏感token信息
3. **HTTPS**: 生产环境必须使用HTTPS

### 性能优化

1. **预加载**: 可以预先加载iframe以提升用户体验
2. **缓存**: 合理设置token过期时间
3. **错误处理**: 实现完善的错误处理和重试机制

### 兼容性

- **现代浏览器**: 支持Chrome 60+, Firefox 55+, Safari 12+
- **移动端**: 支持iOS Safari, Android Chrome
- **PostMessage**: 需要支持HTML5 PostMessage API

## 🔧 故障排除

### 常见问题

#### 1. 认证失败

**症状**: 显示"认证错误"页面
**解决**:

- 检查token是否有效
- 确认token格式正确
- 验证token权限

#### 2. 页面无法加载

**症状**: iframe显示空白或错误页面
**解决**:

- 检查URL格式
- 确认网络连接
- 查看浏览器控制台错误

#### 3. 高度不自适应

**症状**: iframe高度固定，内容被截断
**解决**:

- 监听`RESIZE`消息
- 动态调整iframe高度
- 检查CSS样式设置

### 调试技巧

1. 打开浏览器开发者工具
2. 查看Console中的消息日志
3. 监听PostMessage通信
4. 检查Network请求状态

## 🔄 最佳实践

### 1. Token 管理

```javascript
class TokenManager {
  constructor() {
    this.token = null;
    this.refreshTimer = null;
  }

  setToken(token) {
    this.token = token;
    // 设置token刷新定时器
    this.scheduleRefresh(token);
  }

  scheduleRefresh(token) {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expiresIn = (payload.exp * 1000) - Date.now() - 300000; // 提前5分钟刷新

      if (expiresIn > 0) {
        this.refreshTimer = setTimeout(() => {
          this.refreshToken();
        }, expiresIn);
      }
    } catch (error) {
      console.error('Token解析失败:', error);
    }
  }

  async refreshToken() {
    try {
      const response = await fetch('/api/refresh-token', {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${this.token}` }
      });
      const data = await response.json();
      this.setToken(data.token);

      // 更新iframe中的token
      this.updateIframeToken(data.token);
    } catch (error) {
      console.error('Token刷新失败:', error);
    }
  }

  updateIframeToken(newToken) {
    const iframe = document.getElementById('vibe-iframe');
    if (iframe) {
      const url = new URL(iframe.src);
      url.searchParams.set('token', newToken);
      iframe.src = url.toString();
    }
  }
}
```

### 2. 错误处理和重试机制

```javascript
class IframeManager {
  constructor(containerId, options = {}) {
    this.container = document.getElementById(containerId);
    this.options = {
      maxRetries: 3,
      retryDelay: 2000,
      ...options
    };
    this.retryCount = 0;
  }

  createIframe(src) {
    const iframe = document.createElement('iframe');
    iframe.src = src;
    iframe.style.cssText = `
      width: 100%;
      height: 600px;
      border: none;
      border-radius: 8px;
    `;
    iframe.allow = 'clipboard-read; clipboard-write';

    // 监听加载错误
    iframe.onerror = () => this.handleIframeError();

    return iframe;
  }

  handleIframeError() {
    if (this.retryCount < this.options.maxRetries) {
      this.retryCount++;
      console.log(`iframe加载失败，${this.options.retryDelay}ms后重试 (${this.retryCount}/${this.options.maxRetries})`);

      setTimeout(() => {
        this.reload();
      }, this.options.retryDelay);
    } else {
      this.showErrorMessage('iframe加载失败，请刷新页面重试');
    }
  }

  showErrorMessage(message) {
    this.container.innerHTML = `
      <div style="
        display: flex;
        align-items: center;
        justify-content: center;
        height: 400px;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        color: #6c757d;
        text-align: center;
      ">
        <div>
          <div style="font-size: 48px; margin-bottom: 16px;">⚠️</div>
          <div style="font-size: 18px; margin-bottom: 8px;">加载失败</div>
          <div style="font-size: 14px;">${message}</div>
        </div>
      </div>
    `;
  }
}
```

### 3. 多页面管理

```javascript
class MultiPageIframe {
  constructor(containerId) {
    this.container = document.getElementById(containerId);
    this.currentPage = null;
    this.token = null;
  }

  setToken(token) {
    this.token = token;
  }

  loadPage(page, locale = 'zh') {
    if (!this.token) {
      console.error('请先设置token');
      return;
    }

    const src = `https://your-domain.com/${locale}/embed/${page}?token=${this.token}`;

    // 显示加载状态
    this.showLoading();

    // 创建新iframe
    const iframe = this.createIframe(src);

    // 监听就绪消息
    const messageHandler = (event) => {
      if (event.data.type === 'IFRAME_READY') {
        this.hideLoading();
        this.currentPage = page;
        window.removeEventListener('message', messageHandler);
      }
    };

    window.addEventListener('message', messageHandler);

    // 替换容器内容
    this.container.innerHTML = '';
    this.container.appendChild(iframe);
  }

  showLoading() {
    const loading = document.createElement('div');
    loading.className = 'iframe-loading';
    loading.innerHTML = `
      <div style="
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        z-index: 10;
      ">
        <div style="font-size: 18px; margin-bottom: 10px;">🔄 加载中...</div>
        <div style="color: #6c757d;">正在加载页面内容</div>
      </div>
    `;
    this.container.appendChild(loading);
  }

  hideLoading() {
    const loading = this.container.querySelector('.iframe-loading');
    if (loading) {
      loading.remove();
    }
  }
}
```

## 📊 性能监控

### 加载时间监控

```javascript
class PerformanceMonitor {
  constructor() {
    this.metrics = {};
  }

  startTiming(name) {
    this.metrics[name] = { start: performance.now() };
  }

  endTiming(name) {
    if (this.metrics[name]) {
      this.metrics[name].duration = performance.now() - this.metrics[name].start;
      console.log(`${name}: ${this.metrics[name].duration.toFixed(2)}ms`);
    }
  }

  monitorIframe(iframeId) {
    const iframe = document.getElementById(iframeId);

    this.startTiming('iframe-load');

    window.addEventListener('message', (event) => {
      if (event.data.type === 'IFRAME_READY') {
        this.endTiming('iframe-load');

        // 发送性能数据到分析服务
        this.sendMetrics({
          event: 'iframe_loaded',
          duration: this.metrics['iframe-load'].duration,
          url: iframe.src,
          timestamp: Date.now()
        });
      }
    });
  }

  sendMetrics(data) {
    // 发送到您的分析服务
    fetch('/api/analytics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    }).catch(console.error);
  }
}
```

## 🧪 测试工具

### iframe 连接测试

```html
<!DOCTYPE html>
<html>
<head>
    <title>Vibe Coding iframe 测试工具</title>
    <style>
        .test-container { max-width: 800px; margin: 20px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .test-button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .iframe-container { margin-top: 20px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }
        .log-container { margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; max-height: 300px; overflow-y: auto; }
        .log-entry { margin-bottom: 5px; font-family: monospace; font-size: 12px; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Vibe Coding iframe 测试工具</h1>

        <div class="form-group">
            <label for="domain">域名:</label>
            <input type="text" id="domain" value="https://your-domain.com" placeholder="https://your-domain.com">
        </div>

        <div class="form-group">
            <label for="token">Token:</label>
            <input type="text" id="token" placeholder="输入JWT token">
        </div>

        <div class="form-group">
            <label for="page">页面:</label>
            <select id="page">
                <option value="dashboard">Dashboard</option>
                <option value="report">Report</option>
                <option value="test">Test</option>
            </select>
        </div>

        <div class="form-group">
            <label for="locale">语言:</label>
            <select id="locale">
                <option value="zh">中文</option>
                <option value="en">English</option>
            </select>
        </div>

        <button class="test-button" onclick="testIframe()">测试iframe</button>
        <button class="test-button" onclick="clearLog()">清空日志</button>

        <div class="iframe-container" id="iframe-container" style="display: none;">
            <!-- iframe will be inserted here -->
        </div>

        <div class="log-container">
            <h3>测试日志:</h3>
            <div id="log-output"></div>
        </div>
    </div>

    <script>
        let testStartTime;

        function log(message, type = 'info') {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log-output').innerHTML = '';
        }

        function testIframe() {
            const domain = document.getElementById('domain').value;
            const token = document.getElementById('token').value;
            const page = document.getElementById('page').value;
            const locale = document.getElementById('locale').value;

            if (!token) {
                log('请输入token', 'error');
                return;
            }

            testStartTime = Date.now();
            log('开始测试iframe集成...', 'info');

            const src = `${domain}/${locale}/embed/${page}?token=${token}`;
            log(`加载URL: ${src}`, 'info');

            const container = document.getElementById('iframe-container');
            container.style.display = 'block';
            container.innerHTML = `
                <iframe
                    src="${src}"
                    width="100%"
                    height="600px"
                    frameborder="0"
                    allow="clipboard-read; clipboard-write">
                </iframe>
            `;

            // 监听消息
            window.addEventListener('message', handleTestMessage);
        }

        function handleTestMessage(event) {
            const { type, data } = event.data;
            const duration = Date.now() - testStartTime;

            switch (type) {
                case 'PAGE_LOADED':
                    log(`页面已加载 (${duration}ms)`, 'success');
                    break;

                case 'IFRAME_READY':
                    log(`iframe就绪 (${duration}ms) - ${data.message}`, 'success');
                    break;

                case 'API_CALL':
                    log(`API调用: ${data.method} ${data.url}`, 'info');
                    break;

                case 'API_SUCCESS':
                    log(`API成功: ${data.message}`, 'success');
                    break;

                case 'API_ERROR':
                case 'AUTH_ERROR':
                    log(`认证错误: ${data.message}`, 'error');
                    break;

                case 'RESIZE':
                    log(`高度调整: ${data.height}px`, 'info');
                    break;

                case 'ERROR':
                    log(`错误: ${data.message}`, 'error');
                    break;

                default:
                    log(`收到消息: ${type}`, 'info');
            }
        }
    </script>
</body>
</html>
```

## 📞 技术支持

如需技术支持，请联系：

- 邮箱: <<EMAIL>>
- 文档: <https://docs.vibecoding.com>
- GitHub: <https://github.com/vibecoding/iframe-integration>

---

*文档版本: v1.0*
*更新时间: 2025-01-27*
