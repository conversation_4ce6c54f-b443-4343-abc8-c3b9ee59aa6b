# Vue3 项目嵌入 Next.js iframe 简化集成指南

## 🎯 概述

提供一个极简的方案，在Vue3项目中通过iframe嵌入Next.js页面。无需复杂的SDK，只需一个简单的iframe容器组件即可。

## 📋 核心思路

1. **简单iframe容器**：创建一个Vue3组件来管理iframe
2. **token传递**：通过URL参数传递认证token到iframe  
3. **错误通信**：iframe通过postMessage通知父组件认证错误
4. **业务解耦**：所有业务逻辑都在iframe内部处理

## 🚀 快速实现

### 1. Vue3 iframe容器组件

```vue
<!-- src/components/EmbedIframe.vue -->
<template>
  <div class="embed-iframe-wrapper">
    <!-- 加载状态 -->
    <div v-if="loading" class="embed-loading">
      <div class="loading-spinner"></div>
      <p>{{ loadingText }}</p>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="embed-error">
      <h3>页面加载失败</h3>
      <p>{{ error }}</p>
      <button @click="retry" class="retry-btn">重试</button>
    </div>
    
    <!-- iframe -->
    <iframe
      v-else
      ref="iframeRef"
      :src="iframeSrc"
      :style="iframeStyle"
      frameborder="0"
      @load="onIframeLoad"
      @error="onIframeError"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'

interface Props {
  page: string          // 要加载的页面：dashboard、reports等
  baseUrl?: string      // Next.js应用的基础URL
  width?: string        // iframe宽度
  height?: string       // iframe高度
  loadingText?: string  // 加载文案
  autoResize?: boolean  // 是否自动调整高度
}

interface Emits {
  (e: 'authError', error: string): void
  (e: 'loaded'): void
  (e: 'error', error: string): void
  (e: 'resize', height: number): void
}

const props = withDefaults(defineProps<Props>(), {
  baseUrl: '',
  width: '100%',
  height: '600px',
  loadingText: '正在加载页面...',
  autoResize: true
})

const emit = defineEmits<Emits>()

// 状态管理
const authStore = useAuthStore()
const iframeRef = ref<HTMLIFrameElement>()
const loading = ref(true)
const error = ref('')

// 计算iframe URL
const iframeSrc = computed(() => {
  const base = props.baseUrl || import.meta.env.VITE_NEXTJS_URL || 'http://localhost:3000'
  const token = authStore.token
  
  if (!token) {
    error.value = '未找到认证token'
    return ''
  }
  
  const url = new URL(`/embed/${props.page}`, base)
  url.searchParams.set('token', token)
  url.searchParams.set('source', 'vue3')
  
  return url.toString()
})

// 计算iframe样式
const iframeStyle = computed(() => ({
  width: props.width,
  height: props.height,
  border: 'none',
  display: loading.value || error.value ? 'none' : 'block'
}))

// 生命周期
onMounted(() => {
  window.addEventListener('message', handleMessage)
})

onUnmounted(() => {
  window.removeEventListener('message', handleMessage)
})

// 监听页面变化
watch(() => props.page, () => {
  loading.value = true
  error.value = ''
})

// 处理来自iframe的消息
function handleMessage(event: MessageEvent) {
  const expectedOrigin = props.baseUrl || import.meta.env.VITE_NEXTJS_URL || 'http://localhost:3000'
  if (event.origin !== expectedOrigin) return
  
  const { type, data } = event.data
  
  switch (type) {
    case 'AUTH_ERROR':
      emit('authError', data.message || '认证失败')
      break
    case 'PAGE_LOADED':
      loading.value = false
      error.value = ''
      emit('loaded')
      break
    case 'RESIZE':
      if (props.autoResize && data.height) {
        emit('resize', data.height)
      }
      break
    case 'ERROR':
      error.value = data.message || '页面加载错误'
      loading.value = false
      emit('error', error.value)
      break
  }
}

function onIframeLoad() {
  console.log('iframe基础加载完成')
}

function onIframeError() {
  error.value = '无法连接到嵌入页面'
  loading.value = false
  emit('error', error.value)
}

function retry() {
  loading.value = true
  error.value = ''
  if (iframeRef.value) {
    iframeRef.value.src = iframeSrc.value
  }
}

defineExpose({ retry, iframe: iframeRef })
</script>

<style scoped>
.embed-iframe-wrapper {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.embed-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: #f8f9fa;
  color: #6c757d;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.embed-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: #fff5f5;
  color: #dc3545;
  padding: 20px;
  text-align: center;
}

.retry-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}
</style>
```

### 2. 页面中使用

```vue
<!-- src/views/Dashboard.vue -->
<template>
  <div class="dashboard-page">
    <div class="page-header">
      <h1>仪表板</h1>
      <select v-model="currentPage">
        <option value="dashboard">仪表板</option>
        <option value="reports">报告</option>
        <option value="workspace">工作区</option>
      </select>
    </div>
    
    <EmbedIframe
      :page="currentPage"
      :height="iframeHeight"
      @auth-error="handleAuthError"
      @loaded="handleLoaded"
      @resize="handleResize"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import EmbedIframe from '@/components/EmbedIframe.vue'

const router = useRouter()
const authStore = useAuthStore()
const currentPage = ref('dashboard')
const iframeHeight = ref('600px')

function handleAuthError(error: string) {
  console.error('认证错误:', error)
  authStore.logout()
  alert('登录已过期，请重新登录')
  router.push('/login')
}

function handleLoaded() {
  console.log('页面加载完成')
}

function handleResize(height: number) {
  iframeHeight.value = `${height}px`
}
</script>
```

### 3. 认证状态管理

```typescript
// src/stores/auth.ts
import { defineStore } from 'pinia'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    user: null as any
  }),
  
  getters: {
    isAuthenticated: (state) => !!state.token
  },
  
  actions: {
    setToken(token: string) {
      this.token = token
      localStorage.setItem('token', token)
    },
    
    logout() {
      this.token = ''
      this.user = null
      localStorage.removeItem('token')
    }
  }
})
```

### 4. 环境配置

```bash
# .env.development
VITE_NEXTJS_URL=http://localhost:3000

# .env.production  
VITE_NEXTJS_URL=https://your-nextjs-domain.com
```

## 🔧 Next.js端配置

### 1. 中间件token验证

```typescript
// src/middleware.ts
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  if (request.nextUrl.pathname.startsWith('/embed')) {
    const token = request.nextUrl.searchParams.get('token')
    
    if (!token) {
      return new NextResponse(createAuthErrorPage('缺少认证token'), {
        status: 401,
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
      })
    }
  }
  
  return NextResponse.next()
}

function createAuthErrorPage(message: string): string {
  return `
    <!DOCTYPE html>
    <html>
    <head><meta charset="utf-8"><title>认证错误</title></head>
    <body>
      <script>
        if (window.parent && window.parent !== window) {
          window.parent.postMessage({
            type: 'AUTH_ERROR',
            data: { message: '${message}' }
          }, '*');
        }
      </script>
      <div style="text-align: center; padding: 50px;">
        <h3>认证失败</h3>
        <p>${message}</p>
      </div>
    </body>
    </html>
  `
}

export const config = {
  matcher: '/embed/:path*'
}
```

### 2. 布局文件通信脚本

```typescript
// src/app/embed/layout.tsx
export default function EmbedLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="zh-CN">
      <head><title>嵌入页面</title></head>
      <body>
        {children}
        <script dangerouslySetInnerHTML={{
          __html: `
            // 页面加载完成通知
            window.addEventListener('load', function() {
              if (window.parent && window.parent !== window) {
                window.parent.postMessage({
                  type: 'PAGE_LOADED',
                  data: { timestamp: Date.now() }
                }, '*');
              }
            });
            
            // 自动调整高度
            function notifyResize() {
              if (window.parent && window.parent !== window) {
                const height = Math.max(
                  document.body.scrollHeight,
                  document.documentElement.scrollHeight
                );
                window.parent.postMessage({
                  type: 'RESIZE',
                  data: { height: height + 20 }
                }, '*');
              }
            }
            
            // 监听内容变化
            const observer = new ResizeObserver(notifyResize);
            observer.observe(document.body);
            setInterval(notifyResize, 1000);
          `,
        }} />
      </body>
    </html>
  )
}
```

## 📝 总结

### Vue3项目只需要：
1. 创建EmbedIframe组件
2. 处理认证错误事件  
3. 传递token参数

### Next.js项目只需要：
1. 验证token
2. 通知认证错误
3. 自动调整高度

### 核心优势：
- ✅ 极简实现，无复杂依赖
- ✅ 完全解耦，独立开发
- ✅ 自动容错处理
- ✅ 易于维护扩展

这个方案比SDK方案简单太多，完全符合实际需求！🎉 