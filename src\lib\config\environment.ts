/**
 * 环境配置管理
 * 提供统一的环境检测和配置管理
 */

export type Environment = 'development' | 'staging' | 'production' | 'test'

export interface EnvironmentConfig {
  name: Environment
  displayName: string
  apiBaseUrl: string
  appBaseUrl: string
  allowedEmbedDomains: string[]
  isProduction: boolean
  isDevelopment: boolean
  enableDebug: boolean
  logLevel: 'debug' | 'info' | 'warn' | 'error'
}

/**
 * 环境检测器
 */
class EnvironmentDetector {
  private _config: EnvironmentConfig | null = null

  /**
   * 获取当前环境配置
   */
  get config(): EnvironmentConfig {
    if (!this._config) {
      this._config = this.detectEnvironment()
    }
    return this._config
  }

  /**
   * 检测当前环境
   */
  private detectEnvironment(): EnvironmentConfig {
    // 1. 优先使用显式设置的环境变量
    const explicitEnv = process.env.NODE_ENV as Environment
    const customEnv = process.env.APP_ENV as Environment

    // 2. 基于域名检测环境
    const hostname = this.getHostname()
    const detectedEnv = this.detectEnvironmentByHostname(hostname)

    // 3. 确定最终环境
    const environment = customEnv || explicitEnv || detectedEnv || 'development'

    return this.buildEnvironmentConfig(environment, hostname)
  }

  /**
   * 获取当前主机名
   */
  private getHostname(): string {
    // 服务端环境
    if (typeof window === 'undefined') {
      return process.env.VERCEL_URL || 
             process.env.NEXT_PUBLIC_APP_URL?.replace(/^https?:\/\//, '') || 
             'localhost'
    }
    
    // 客户端环境
    return window.location.hostname
  }

  /**
   * 基于主机名检测环境
   */
  private detectEnvironmentByHostname(hostname: string): Environment {
    // 生产环境域名
    if (hostname.includes('www.goglobalsp.com')) {
      return 'production'
    }
    
    // 开发环境域名
    if (hostname.includes('dev.specific-ai.com')) {
      return 'development'
    }
    
    // 预发布环境域名
    if (hostname.includes('staging.') || hostname.includes('test.')) {
      return 'staging'
    }
    
    // 本地环境
    if (hostname.includes('localhost') || hostname.includes('127.0.0.1') || hostname.includes('0.0.0.0')) {
      return 'development'
    }
    
    // Vercel 预览环境
    if (hostname.includes('vercel.app')) {
      return 'staging'
    }
    
    // 默认为开发环境
    return 'development'
  }

  /**
   * 构建环境配置
   */
  private buildEnvironmentConfig(environment: Environment, hostname: string): EnvironmentConfig {
    const baseConfigs: Record<Environment, Partial<EnvironmentConfig>> = {
      production: {
        name: 'production',
        displayName: '生产环境',
        apiBaseUrl: process.env.API_BASE_URL || 'https://api.goglobalsp.com',
        appBaseUrl: process.env.NEXT_PUBLIC_APP_URL || 'https://www.goglobalsp.com',
        allowedEmbedDomains: [
          'https://www.goglobalsp.com',
          'https://dev.goglobalsp.com',
        ],
        isProduction: true,
        isDevelopment: false,
        enableDebug: false,
        logLevel: 'warn'
      },
      development: {
        name: 'development',
        displayName: '开发环境',
        apiBaseUrl: process.env.API_BASE_URL || 'https://dev-api.specific-ai.com',
        appBaseUrl: process.env.NEXT_PUBLIC_APP_URL || 'https://dev.goglobalsp.com',
        allowedEmbedDomains: [
          'https://dev.goglobalsp.com',
          'https://www.goglobalsp.com',
          'http://localhost:3000',
          'http://localhost:3001',
          'http://localhost:10001',
          'http://127.0.0.1:3000',
          'http://127.0.0.1:3001',
          'http://127.0.0.1:10001',
          'http://**************'
        ],
        isProduction: false,
        isDevelopment: true,
        enableDebug: true,
        logLevel: 'debug'
      },
      staging: {
        name: 'staging',
        displayName: '预发布环境',
        apiBaseUrl: process.env.API_BASE_URL || 'https://staging-api.specific-ai.com',
        appBaseUrl: process.env.NEXT_PUBLIC_APP_URL || 'https://staging.specific-ai.com',
        allowedEmbedDomains: [
          'https://staging.specific-ai.com',
          'https://dev.goglobalsp.com'
        ],
        isProduction: false,
        isDevelopment: false,
        enableDebug: true,
        logLevel: 'info'
      },
      test: {
        name: 'test',
        displayName: '测试环境',
        apiBaseUrl: process.env.API_BASE_URL || 'http://localhost:8080',
        appBaseUrl: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001',
        allowedEmbedDomains: [
          'http://localhost:3000',
          'http://localhost:3001',
          'http://localhost:3002',
          'http://localhost:8080',
          'http://localhost:8081',
          'http://localhost:9000',
          'http://localhost:9001',
          'http://localhost:10000',
          'http://localhost:10001',
          'http://127.0.0.1:3000',
          'http://127.0.0.1:3001',
          'http://127.0.0.1:3002',
          'http://127.0.0.1:8080',
          'http://127.0.0.1:8081',
          'http://127.0.0.1:9000',
          'http://127.0.0.1:9001',
          'http://127.0.0.1:10000',
          'http://127.0.0.1:10001'
        ],
        isProduction: false,
        isDevelopment: true,
        enableDebug: true,
        logLevel: 'debug'
      }
    }

    const baseConfig = baseConfigs[environment]
    
    // 本地环境特殊处理
    if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
      const port = this.extractPort(hostname)
      const protocol = 'http'
      const localUrl = `${protocol}://${hostname}${port ? `:${port}` : ''}`
      
      return {
        ...baseConfig,
        appBaseUrl: localUrl,
        allowedEmbedDomains: [
          ...baseConfig.allowedEmbedDomains || [],
          localUrl,
          `http://localhost:${port || 3001}`,
          `http://127.0.0.1:${port || 3001}`
        ]
      } as EnvironmentConfig
    }

    return baseConfig as EnvironmentConfig
  }

  /**
   * 从主机名中提取端口号
   */
  private extractPort(hostname: string): string | null {
    if (typeof window !== 'undefined') {
      return window.location.port || null
    }
    
    const match = hostname.match(/:(\d+)$/)
    return match ? match[1] : null
  }

  /**
   * 重置配置缓存（用于测试）
   */
  reset(): void {
    this._config = null
  }

  /**
   * 手动设置环境（用于测试）
   */
  setEnvironment(env: Environment): void {
    this._config = this.buildEnvironmentConfig(env, this.getHostname())
  }
}

// 单例实例
export const environmentDetector = new EnvironmentDetector()

/**
 * 获取当前环境配置
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  return environmentDetector.config
}

/**
 * 检查是否为生产环境
 */
export function isProduction(): boolean {
  return environmentDetector.config.isProduction
}

/**
 * 检查是否为开发环境
 */
export function isDevelopment(): boolean {
  return environmentDetector.config.isDevelopment
}

/**
 * 获取API基础URL
 */
export function getApiBaseUrl(): string {
  return environmentDetector.config.apiBaseUrl
}

/**
 * 获取应用基础URL
 */
export function getAppBaseUrl(): string {
  return environmentDetector.config.appBaseUrl
}

/**
 * 获取允许的嵌入域名列表
 */
export function getAllowedEmbedDomains(): string[] {
  return environmentDetector.config.allowedEmbedDomains
}

/**
 * 检查是否启用调试模式
 */
export function isDebugEnabled(): boolean {
  return environmentDetector.config.enableDebug
}

/**
 * 获取日志级别
 */
export function getLogLevel(): string {
  return environmentDetector.config.logLevel
}

/**
 * 环境配置验证器
 */
export class EnvironmentValidator {
  /**
   * 验证环境配置的完整性
   */
  static validate(config: EnvironmentConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!config.apiBaseUrl) {
      errors.push('API base URL is required')
    }

    if (!config.appBaseUrl) {
      errors.push('App base URL is required')
    }

    if (!config.allowedEmbedDomains || config.allowedEmbedDomains.length === 0) {
      errors.push('At least one allowed embed domain is required')
    }

    // 验证URL格式
    try {
      new URL(config.apiBaseUrl)
    } catch {
      errors.push('Invalid API base URL format')
    }

    try {
      new URL(config.appBaseUrl)
    } catch {
      errors.push('Invalid app base URL format')
    }

    // 验证嵌入域名格式
    config.allowedEmbedDomains.forEach((domain, index) => {
      try {
        new URL(domain)
      } catch {
        errors.push(`Invalid embed domain format at index ${index}: ${domain}`)
      }
    })

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 验证当前环境配置
   */
  static validateCurrent(): { isValid: boolean; errors: string[] } {
    return this.validate(getEnvironmentConfig())
  }
}
