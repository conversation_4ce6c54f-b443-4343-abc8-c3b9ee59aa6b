# Next.js 基础前端架构搭建详细指南

## 📋 架构搭建概述

### 目标
搭建一个现代化的Next.js前端架构，为Vue3项目迁移提供坚实的技术基础。重点关注基础架构而非具体业务逻辑。

### 技术选型
```
Next.js 15 + TypeScript + TailwindCSS v4 + Zustand + Radix UI + Framer Motion
```

### 工作量估算
- **总工时**: 40小时 (1周，2人团队)
- **复杂度**: 中等
- **优先级**: 最高 (所有后续工作的基础)

## 🏗️ 架构设计原则

### 1. 模块化设计
- **包结构清晰**: 每个功能模块独立
- **依赖关系明确**: 避免循环依赖
- **可扩展性**: 便于后续添加新功能

### 2. 类型安全
- **TypeScript覆盖率**: 100%
- **严格模式**: 启用所有TypeScript严格检查
- **类型导出**: 统一的类型定义管理

### 3. 开发体验
- **热重载**: 快速开发反馈
- **代码规范**: ESLint + Prettier自动化
- **调试友好**: 完善的开发工具集成

### 4. 性能优化
- **代码分割**: 按需加载
- **缓存策略**: 合理的缓存配置
- **构建优化**: 最小化包体积

## 🎯 架构搭建步骤

### 步骤1: Next.js项目初始化 (8小时)

#### 1.1 创建Next.js项目 (2小时)
```bash
# 创建Next.js 15项目
npx create-next-app@latest specific-ai-nextjs --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"

# 项目结构
specific-ai-nextjs/
├── src/
│   ├── app/                 # App Router页面
│   ├── components/          # 共享组件
│   ├── lib/                 # 工具库
│   ├── hooks/               # 自定义Hooks
│   ├── stores/              # 状态管理
│   ├── types/               # 类型定义
│   └── utils/               # 工具函数
├── public/                  # 静态资源
├── next.config.js           # Next.js配置
├── tailwind.config.js       # TailwindCSS配置
├── tsconfig.json            # TypeScript配置
└── package.json             # 依赖管理
```

#### 1.2 TypeScript配置优化 (2小时)
```json
// tsconfig.json - 严格模式配置
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [{ "name": "next" }],
    
    // 严格检查选项
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    
    // 路径映射
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/stores/*": ["./src/stores/*"],
      "@/types/*": ["./src/types/*"],
      "@/utils/*": ["./src/utils/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

#### 1.3 Next.js配置优化 (2小时)
```javascript
// next.config.js - 生产级配置
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 实验性功能
  experimental: {
    // 优化包导入
    optimizePackageImports: [
      'lucide-react',
      'date-fns',
      'lodash-es'
    ],
    // 启用Turbopack (开发环境)
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },
  
  // 图片优化
  images: {
    domains: ['localhost'],
    formats: ['image/webp', 'image/avif'],
  },
  
  // 压缩配置
  compress: true,
  
  // 构建优化
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 代码分割优化
    config.optimization.splitChunks = {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          enforce: true,
        },
      },
    }
    
    // SVG处理
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    })
    
    return config
  },
  
  // 环境变量
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  
  // 重定向配置
  async redirects() {
    return [
      {
        source: '/dashboard',
        destination: '/workspace',
        permanent: true,
      },
    ]
  },
  
  // 重写配置
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.API_BASE_URL}/:path*`,
      },
    ]
  },
}

module.exports = nextConfig
```

#### 1.4 开发工具配置 (2小时)
```json
// .eslintrc.json - ESLint配置
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-empty-function": "off",
    "prefer-const": "error",
    "no-var": "error"
  },
  "ignorePatterns": ["node_modules/", ".next/", "out/"]
}
```

```json
// .prettierrc - Prettier配置
{
  "semi": false,
  "trailingComma": "es5",
  "singleQuote": true,
  "tabWidth": 2,
  "useTabs": false,
  "printWidth": 100,
  "bracketSpacing": true,
  "arrowParens": "avoid",
  "endOfLine": "lf"
}
```

### 步骤2: TailwindCSS v4集成 (8小时)

#### 2.1 TailwindCSS v4安装配置 (3小时)
```bash
# 安装TailwindCSS v4
npm install tailwindcss@next @tailwindcss/postcss@next
```

```javascript
// tailwind.config.js - v4配置
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      // 自定义颜色系统
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      
      // 自定义间距
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      
      // 自定义字体
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace'],
      },
      
      // 自定义动画
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'spin-slow': 'spin 3s linear infinite',
      },
      
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
    require('@tailwindcss/aspect-ratio'),
  ],
}
```

#### 2.2 CSS变量系统 (3小时)
```css
/* src/app/globals.css - 全局样式和CSS变量 */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* 亮色主题 */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    /* 暗色主题 */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* 滚动条样式 */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-secondary;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-foreground;
  }
}

@layer components {
  /* 自定义组件样式 */
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md font-medium transition-colors;
  }
  
  .card-base {
    @apply bg-card text-card-foreground rounded-lg border shadow-sm;
  }
  
  .input-base {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
}

@layer utilities {
  /* 自定义工具类 */
  .text-balance {
    text-wrap: balance;
  }
  
  .animate-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-out {
    animation: fadeOut 0.5s ease-in-out;
  }
}
```

#### 2.3 主题系统实现 (2小时)
```typescript
// src/lib/theme.ts - 主题管理
'use client'

import { createContext, useContext, useEffect, useState } from 'react'

type Theme = 'dark' | 'light' | 'system'

type ThemeProviderProps = {
  children: React.ReactNode
  defaultTheme?: Theme
  storageKey?: string
}

type ThemeProviderState = {
  theme: Theme
  setTheme: (theme: Theme) => void
}

const initialState: ThemeProviderState = {
  theme: 'system',
  setTheme: () => null,
}

const ThemeProviderContext = createContext<ThemeProviderState>(initialState)

export function ThemeProvider({
  children,
  defaultTheme = 'system',
  storageKey = 'ui-theme',
  ...props
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(
    () => (localStorage?.getItem(storageKey) as Theme) || defaultTheme
  )

  useEffect(() => {
    const root = window.document.documentElement

    root.classList.remove('light', 'dark')

    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)')
        .matches
        ? 'dark'
        : 'light'

      root.classList.add(systemTheme)
      return
    }

    root.classList.add(theme)
  }, [theme])

  const value = {
    theme,
    setTheme: (theme: Theme) => {
      localStorage.setItem(storageKey, theme)
      setTheme(theme)
    },
  }

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  )
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext)

  if (context === undefined)
    throw new Error('useTheme must be used within a ThemeProvider')

  return context
}
```

### 步骤3: 状态管理架构 (8小时)

#### 3.1 Zustand基础配置 (3小时)
```bash
# 安装状态管理相关依赖
npm install zustand immer
npm install -D @types/node
```

```typescript
// src/stores/index.ts - 状态管理入口
export { useAuthStore } from './auth-store'
export { useUIStore } from './ui-store'
export { useAppStore } from './app-store'

// 导出类型
export type { AuthState } from './auth-store'
export type { UIState } from './ui-store'
export type { AppState } from './app-store'
```

```typescript
// src/stores/auth-store.ts - 认证状态管理
import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  role: string
}

export interface AuthState {
  // 状态
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null

  // 方法
  setUser: (user: User) => void
  clearAuth: () => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void

  // 异步方法 (后续实现)
  login: (credentials: any) => Promise<void>
  logout: () => Promise<void>
  validateToken: () => Promise<boolean>
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      immer((set, get) => ({
        // 初始状态
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,

        // 同步方法
        setUser: (user: User) => set(state => {
          state.user = user
          state.isAuthenticated = true
          state.error = null
        }),

        clearAuth: () => set(state => {
          state.user = null
          state.isAuthenticated = false
          state.error = null
        }),

        setLoading: (loading: boolean) => set(state => {
          state.isLoading = loading
        }),

        setError: (error: string | null) => set(state => {
          state.error = error
        }),

        // 异步方法占位符
        login: async (credentials: any) => {
          // TODO: 实现登录逻辑
          console.log('Login method to be implemented')
        },

        logout: async () => {
          // TODO: 实现登出逻辑
          get().clearAuth()
        },

        validateToken: async () => {
          // TODO: 实现token验证逻辑
          return false
        },
      })),
      {
        name: 'auth-storage',
        partialize: (state) => ({
          user: state.user,
          isAuthenticated: state.isAuthenticated
        }),
      }
    ),
    { name: 'auth-store' }
  )
)
```

```typescript
// src/stores/ui-store.ts - UI状态管理
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

export interface UIState {
  // 侧边栏状态
  sidebarOpen: boolean
  sidebarCollapsed: boolean

  // 模态框状态
  modals: Record<string, boolean>

  // 加载状态
  globalLoading: boolean

  // 通知状态
  notifications: Array<{
    id: string
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    message?: string
    duration?: number
  }>

  // 方法
  setSidebarOpen: (open: boolean) => void
  setSidebarCollapsed: (collapsed: boolean) => void
  toggleSidebar: () => void

  openModal: (modalId: string) => void
  closeModal: (modalId: string) => void
  toggleModal: (modalId: string) => void

  setGlobalLoading: (loading: boolean) => void

  addNotification: (notification: Omit<UIState['notifications'][0], 'id'>) => void
  removeNotification: (id: string) => void
  clearNotifications: () => void
}

export const useUIStore = create<UIState>()(
  devtools(
    immer((set, get) => ({
      // 初始状态
      sidebarOpen: true,
      sidebarCollapsed: false,
      modals: {},
      globalLoading: false,
      notifications: [],

      // 侧边栏方法
      setSidebarOpen: (open: boolean) => set(state => {
        state.sidebarOpen = open
      }),

      setSidebarCollapsed: (collapsed: boolean) => set(state => {
        state.sidebarCollapsed = collapsed
      }),

      toggleSidebar: () => set(state => {
        state.sidebarOpen = !state.sidebarOpen
      }),

      // 模态框方法
      openModal: (modalId: string) => set(state => {
        state.modals[modalId] = true
      }),

      closeModal: (modalId: string) => set(state => {
        state.modals[modalId] = false
      }),

      toggleModal: (modalId: string) => set(state => {
        state.modals[modalId] = !state.modals[modalId]
      }),

      // 加载状态方法
      setGlobalLoading: (loading: boolean) => set(state => {
        state.globalLoading = loading
      }),

      // 通知方法
      addNotification: (notification) => set(state => {
        const id = Date.now().toString()
        state.notifications.push({ ...notification, id })
      }),

      removeNotification: (id: string) => set(state => {
        state.notifications = state.notifications.filter(n => n.id !== id)
      }),

      clearNotifications: () => set(state => {
        state.notifications = []
      }),
    })),
    { name: 'ui-store' }
  )
)
```

#### 3.2 应用状态管理 (2小时)
```typescript
// src/stores/app-store.ts - 应用级状态管理
import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

export interface AppConfig {
  apiBaseUrl: string
  version: string
  environment: 'development' | 'staging' | 'production'
  features: Record<string, boolean>
}

export interface AppState {
  // 应用配置
  config: AppConfig

  // 应用状态
  isInitialized: boolean
  isOnline: boolean

  // 用户偏好
  preferences: {
    language: string
    timezone: string
    dateFormat: string
    theme: 'light' | 'dark' | 'system'
  }

  // 方法
  setConfig: (config: Partial<AppConfig>) => void
  setInitialized: (initialized: boolean) => void
  setOnlineStatus: (online: boolean) => void
  updatePreferences: (preferences: Partial<AppState['preferences']>) => void

  // 功能开关
  isFeatureEnabled: (feature: string) => boolean
  toggleFeature: (feature: string) => void
}

export const useAppStore = create<AppState>()(
  devtools(
    persist(
      immer((set, get) => ({
        // 初始状态
        config: {
          apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || '',
          version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
          environment: (process.env.NODE_ENV as any) || 'development',
          features: {},
        },

        isInitialized: false,
        isOnline: true,

        preferences: {
          language: 'zh-CN',
          timezone: 'Asia/Shanghai',
          dateFormat: 'YYYY-MM-DD',
          theme: 'system',
        },

        // 方法实现
        setConfig: (config: Partial<AppConfig>) => set(state => {
          state.config = { ...state.config, ...config }
        }),

        setInitialized: (initialized: boolean) => set(state => {
          state.isInitialized = initialized
        }),

        setOnlineStatus: (online: boolean) => set(state => {
          state.isOnline = online
        }),

        updatePreferences: (preferences) => set(state => {
          state.preferences = { ...state.preferences, ...preferences }
        }),

        // 功能开关
        isFeatureEnabled: (feature: string) => {
          const { config } = get()
          return config.features[feature] ?? false
        },

        toggleFeature: (feature: string) => set(state => {
          state.config.features[feature] = !state.config.features[feature]
        }),
      })),
      {
        name: 'app-storage',
        partialize: (state) => ({
          preferences: state.preferences,
          config: { features: state.config.features }
        }),
      }
    ),
    { name: 'app-store' }
  )
)
```

#### 3.3 状态管理Hooks (3小时)
```typescript
// src/hooks/use-store-selectors.ts - 状态选择器Hooks
import { useAuthStore, useUIStore, useAppStore } from '@/stores'
import { useMemo } from 'react'

// 认证相关选择器
export function useAuth() {
  const auth = useAuthStore()

  return useMemo(() => ({
    user: auth.user,
    isAuthenticated: auth.isAuthenticated,
    isLoading: auth.isLoading,
    error: auth.error,
    login: auth.login,
    logout: auth.logout,
    validateToken: auth.validateToken,
  }), [auth])
}

export function useAuthUser() {
  return useAuthStore(state => state.user)
}

export function useAuthStatus() {
  return useAuthStore(state => ({
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    error: state.error,
  }))
}

// UI相关选择器
export function useUI() {
  const ui = useUIStore()

  return useMemo(() => ({
    sidebarOpen: ui.sidebarOpen,
    sidebarCollapsed: ui.sidebarCollapsed,
    globalLoading: ui.globalLoading,
    notifications: ui.notifications,

    setSidebarOpen: ui.setSidebarOpen,
    setSidebarCollapsed: ui.setSidebarCollapsed,
    toggleSidebar: ui.toggleSidebar,
    setGlobalLoading: ui.setGlobalLoading,
    addNotification: ui.addNotification,
    removeNotification: ui.removeNotification,
  }), [ui])
}

export function useSidebar() {
  return useUIStore(state => ({
    open: state.sidebarOpen,
    collapsed: state.sidebarCollapsed,
    setOpen: state.setSidebarOpen,
    setCollapsed: state.setSidebarCollapsed,
    toggle: state.toggleSidebar,
  }))
}

export function useNotifications() {
  return useUIStore(state => ({
    notifications: state.notifications,
    add: state.addNotification,
    remove: state.removeNotification,
    clear: state.clearNotifications,
  }))
}

// 应用相关选择器
export function useApp() {
  const app = useAppStore()

  return useMemo(() => ({
    config: app.config,
    isInitialized: app.isInitialized,
    isOnline: app.isOnline,
    preferences: app.preferences,

    setInitialized: app.setInitialized,
    setOnlineStatus: app.setOnlineStatus,
    updatePreferences: app.updatePreferences,
    isFeatureEnabled: app.isFeatureEnabled,
    toggleFeature: app.toggleFeature,
  }), [app])
}

export function useAppConfig() {
  return useAppStore(state => state.config)
}

export function useUserPreferences() {
  return useAppStore(state => ({
    preferences: state.preferences,
    update: state.updatePreferences,
  }))
}

export function useFeatureFlags() {
  return useAppStore(state => ({
    isEnabled: state.isFeatureEnabled,
    toggle: state.toggleFeature,
    features: state.config.features,
  }))
}
```

### 步骤4: 基础组件库搭建 (12小时)

#### 4.1 Radix UI集成 (4小时)
```bash
# 安装Radix UI核心组件
npm install @radix-ui/react-slot @radix-ui/react-dialog @radix-ui/react-dropdown-menu
npm install @radix-ui/react-select @radix-ui/react-tooltip @radix-ui/react-toast
npm install @radix-ui/react-popover @radix-ui/react-tabs @radix-ui/react-accordion
npm install @radix-ui/react-alert-dialog @radix-ui/react-avatar @radix-ui/react-badge
npm install @radix-ui/react-button @radix-ui/react-card @radix-ui/react-checkbox
npm install @radix-ui/react-input @radix-ui/react-label @radix-ui/react-separator

# 安装图标库
npm install lucide-react

# 安装工具库
npm install class-variance-authority clsx tailwind-merge
```

```typescript
// src/lib/utils.ts - 工具函数
import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date | string, format: string = 'YYYY-MM-DD'): string {
  // 简单的日期格式化实现
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}
```

#### 4.2 基础UI组件实现 (4小时)
```typescript
// src/components/ui/button.tsx - Button组件
import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button'
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = 'Button'

export { Button, buttonVariants }
```

```typescript
// src/components/ui/input.tsx - Input组件
import * as React from 'react'
import { cn } from '@/lib/utils'

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string
  label?: string
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, error, label, ...props }, ref) => {
    const id = React.useId()

    return (
      <div className="space-y-2">
        {label && (
          <label
            htmlFor={id}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {label}
          </label>
        )}
        <input
          id={id}
          type={type}
          className={cn(
            'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
            error && 'border-destructive focus-visible:ring-destructive',
            className
          )}
          ref={ref}
          {...props}
        />
        {error && (
          <p className="text-sm text-destructive">{error}</p>
        )}
      </div>
    )
  }
)
Input.displayName = 'Input'

export { Input }
```

```typescript
// src/components/ui/card.tsx - Card组件
import * as React from 'react'
import { cn } from '@/lib/utils'

const Card = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      'rounded-lg border bg-card text-card-foreground shadow-sm',
      className
    )}
    {...props}
  />
))
Card.displayName = 'Card'

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex flex-col space-y-1.5 p-6', className)}
    {...props}
  />
))
CardHeader.displayName = 'CardHeader'

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      'text-2xl font-semibold leading-none tracking-tight',
      className
    )}
    {...props}
  />
))
CardTitle.displayName = 'CardTitle'

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn('text-sm text-muted-foreground', className)}
    {...props}
  />
))
CardDescription.displayName = 'CardDescription'

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />
))
CardContent.displayName = 'CardContent'

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex items-center p-6 pt-0', className)}
    {...props}
  />
))
CardFooter.displayName = 'CardFooter'

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }
```

#### 4.3 复合组件实现 (4小时)
```typescript
// src/components/ui/dialog.tsx - Dialog组件
'use client'

import * as React from 'react'
import * as DialogPrimitive from '@radix-ui/react-dialog'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'

const Dialog = DialogPrimitive.Root
const DialogTrigger = DialogPrimitive.Trigger
const DialogPortal = DialogPrimitive.Portal
const DialogClose = DialogPrimitive.Close

const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      'fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
      className
    )}
    {...props}
  />
))
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName

const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        'fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg',
        className
      )}
      {...props}
    >
      {children}
      <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  </DialogPortal>
))
DialogContent.displayName = DialogPrimitive.Content.displayName

const DialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      'flex flex-col space-y-1.5 text-center sm:text-left',
      className
    )}
    {...props}
  />
)
DialogHeader.displayName = 'DialogHeader'

const DialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      'flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2',
      className
    )}
    {...props}
  />
)
DialogFooter.displayName = 'DialogFooter'

const DialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn(
      'text-lg font-semibold leading-none tracking-tight',
      className
    )}
    {...props}
  />
))
DialogTitle.displayName = DialogPrimitive.Title.displayName

const DialogDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    className={cn('text-sm text-muted-foreground', className)}
    {...props}
  />
))
DialogDescription.displayName = DialogPrimitive.Description.displayName

export {
  Dialog,
  DialogPortal,
  DialogOverlay,
  DialogClose,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
}
```

```typescript
// src/components/ui/loading.tsx - Loading组件
import * as React from 'react'
import { cn } from '@/lib/utils'
import { Loader2 } from 'lucide-react'

interface LoadingProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg'
  text?: string
  overlay?: boolean
}

const Loading = React.forwardRef<HTMLDivElement, LoadingProps>(
  ({ className, size = 'md', text, overlay = false, ...props }, ref) => {
    const sizeClasses = {
      sm: 'h-4 w-4',
      md: 'h-6 w-6',
      lg: 'h-8 w-8',
    }

    const content = (
      <div
        ref={ref}
        className={cn(
          'flex items-center justify-center',
          overlay && 'fixed inset-0 z-50 bg-background/80 backdrop-blur-sm',
          className
        )}
        {...props}
      >
        <div className="flex flex-col items-center space-y-2">
          <Loader2 className={cn('animate-spin', sizeClasses[size])} />
          {text && (
            <p className="text-sm text-muted-foreground">{text}</p>
          )}
        </div>
      </div>
    )

    return content
  }
)
Loading.displayName = 'Loading'

export { Loading }
```

### 步骤5: 动画系统集成 (4小时)

#### 5.1 Framer Motion安装配置 (2小时)
```bash
# 安装Framer Motion
npm install framer-motion
```

```typescript
// src/components/ui/motion.tsx - Motion组件封装
'use client'

import { motion, AnimatePresence, type Variants } from 'framer-motion'
import * as React from 'react'

// 预定义动画变体
export const fadeInVariants: Variants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 }
}

export const slideUpVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 }
}

export const slideInVariants: Variants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: 20 }
}

export const scaleVariants: Variants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.95 }
}

// 动画配置
export const defaultTransition = {
  duration: 0.3,
  ease: [0.4, 0.0, 0.2, 1]
}

export const springTransition = {
  type: 'spring',
  damping: 25,
  stiffness: 300
}

// 封装的Motion组件
interface MotionDivProps extends React.HTMLAttributes<HTMLDivElement> {
  variants?: Variants
  initial?: string
  animate?: string
  exit?: string
  transition?: any
}

export const MotionDiv = React.forwardRef<HTMLDivElement, MotionDivProps>(
  ({ variants = fadeInVariants, initial = 'hidden', animate = 'visible', exit = 'exit', transition = defaultTransition, ...props }, ref) => (
    <motion.div
      ref={ref}
      variants={variants}
      initial={initial}
      animate={animate}
      exit={exit}
      transition={transition}
      {...props}
    />
  )
)
MotionDiv.displayName = 'MotionDiv'

// 页面切换动画组件
interface PageTransitionProps {
  children: React.ReactNode
  className?: string
}

export function PageTransition({ children, className }: PageTransitionProps) {
  return (
    <MotionDiv
      variants={slideUpVariants}
      className={className}
      transition={springTransition}
    >
      {children}
    </MotionDiv>
  )
}

// 列表动画组件
interface AnimatedListProps {
  children: React.ReactNode[]
  className?: string
  stagger?: number
}

export function AnimatedList({ children, className, stagger = 0.1 }: AnimatedListProps) {
  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: stagger
      }
    }
  }

  const itemVariants: Variants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={className}
    >
      {children.map((child, index) => (
        <motion.div key={index} variants={itemVariants}>
          {child}
        </motion.div>
      ))}
    </motion.div>
  )
}

export { motion, AnimatePresence }
```

#### 5.2 动画Hooks实现 (2小时)
```typescript
// src/hooks/use-animation.ts - 动画相关Hooks
'use client'

import { useAnimation, useInView } from 'framer-motion'
import { useEffect, useRef } from 'react'

// 滚动触发动画Hook
export function useScrollAnimation(threshold = 0.1) {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, amount: threshold })
  const controls = useAnimation()

  useEffect(() => {
    if (isInView) {
      controls.start('visible')
    }
  }, [isInView, controls])

  return { ref, controls, isInView }
}

// 悬停动画Hook
export function useHoverAnimation() {
  const controls = useAnimation()

  const handleHoverStart = () => {
    controls.start('hover')
  }

  const handleHoverEnd = () => {
    controls.start('initial')
  }

  return {
    controls,
    onHoverStart: handleHoverStart,
    onHoverEnd: handleHoverEnd,
  }
}

// 点击动画Hook
export function useClickAnimation() {
  const controls = useAnimation()

  const handleTap = () => {
    controls.start('tap').then(() => {
      controls.start('initial')
    })
  }

  return {
    controls,
    onTap: handleTap,
  }
}

// 序列动画Hook
export function useSequenceAnimation(sequence: string[], delay = 0.5) {
  const controls = useAnimation()

  const startSequence = async () => {
    for (const step of sequence) {
      await controls.start(step)
      await new Promise(resolve => setTimeout(resolve, delay * 1000))
    }
  }

  return {
    controls,
    startSequence,
  }
}
```

### 步骤6: 项目结构优化 (4小时)

#### 6.1 目录结构规范 (2小时)
```
src/
├── app/                          # Next.js App Router
│   ├── (auth)/                   # 认证相关页面组
│   │   ├── login/
│   │   │   └── page.tsx
│   │   └── layout.tsx
│   ├── (dashboard)/              # 仪表板页面组
│   │   ├── workspace/
│   │   │   └── page.tsx
│   │   ├── dashboard/
│   │   │   └── page.tsx
│   │   └── layout.tsx
│   ├── api/                      # API路由
│   │   └── auth/
│   │       └── route.ts
│   ├── globals.css               # 全局样式
│   ├── layout.tsx                # 根布局
│   ├── loading.tsx               # 全局加载页面
│   ├── error.tsx                 # 全局错误页面
│   ├── not-found.tsx             # 404页面
│   └── page.tsx                  # 首页
├── components/                   # 组件目录
│   ├── ui/                       # 基础UI组件
│   │   ├── button.tsx
│   │   ├── input.tsx
│   │   ├── card.tsx
│   │   ├── dialog.tsx
│   │   ├── loading.tsx
│   │   └── motion.tsx
│   ├── layout/                   # 布局组件
│   │   ├── header.tsx
│   │   ├── sidebar.tsx
│   │   ├── footer.tsx
│   │   └── navigation.tsx
│   ├── forms/                    # 表单组件
│   │   ├── login-form.tsx
│   │   └── contact-form.tsx
│   └── providers/                # 上下文提供者
│       ├── theme-provider.tsx
│       ├── auth-provider.tsx
│       └── query-provider.tsx
├── hooks/                        # 自定义Hooks
│   ├── use-store-selectors.ts
│   ├── use-animation.ts
│   ├── use-local-storage.ts
│   └── use-debounce.ts
├── lib/                          # 工具库
│   ├── utils.ts                  # 通用工具函数
│   ├── theme.ts                  # 主题管理
│   ├── api.ts                    # API客户端
│   ├── auth.ts                   # 认证工具
│   └── constants.ts              # 常量定义
├── stores/                       # 状态管理
│   ├── index.ts                  # 导出入口
│   ├── auth-store.ts             # 认证状态
│   ├── ui-store.ts               # UI状态
│   └── app-store.ts              # 应用状态
├── types/                        # 类型定义
│   ├── index.ts                  # 导出入口
│   ├── auth.ts                   # 认证相关类型
│   ├── api.ts                    # API相关类型
│   └── ui.ts                     # UI相关类型
└── utils/                        # 工具函数
    ├── format.ts                 # 格式化工具
    ├── validation.ts             # 验证工具
    └── date.ts                   # 日期工具
```

#### 6.2 类型定义系统 (2小时)
```typescript
// src/types/index.ts - 类型导出入口
export * from './auth'
export * from './api'
export * from './ui'
export * from './common'
```

```typescript
// src/types/common.ts - 通用类型定义
export interface BaseEntity {
  id: string
  createdAt: string
  updatedAt: string
}

export interface PaginationParams {
  page: number
  limit: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginationResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  code?: number
}

export interface SelectOption {
  label: string
  value: string | number
  disabled?: boolean
  description?: string
}

export type Status = 'idle' | 'loading' | 'success' | 'error'

export interface AsyncState<T = any> {
  data: T | null
  status: Status
  error: string | null
}
```

```typescript
// src/types/auth.ts - 认证相关类型
export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  role: UserRole
  permissions: Permission[]
  createdAt: string
  updatedAt: string
}

export interface UserRole {
  id: string
  name: string
  code: string
  description?: string
}

export interface Permission {
  id: string
  name: string
  code: string
  resource: string
  action: string
}

export interface LoginCredentials {
  email: string
  password: string
  remember?: boolean
}

export interface RegisterData {
  name: string
  email: string
  password: string
  confirmPassword: string
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}
```

```typescript
// src/types/api.ts - API相关类型
export interface ApiConfig {
  baseURL: string
  timeout: number
  headers: Record<string, string>
}

export interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  url: string
  data?: any
  params?: Record<string, any>
  headers?: Record<string, string>
}

export interface ApiError {
  message: string
  code: number
  details?: any
}

export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
```

```typescript
// src/types/ui.ts - UI相关类型
export type Theme = 'light' | 'dark' | 'system'

export type NotificationType = 'success' | 'error' | 'warning' | 'info'

export interface Notification {
  id: string
  type: NotificationType
  title: string
  message?: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

export interface ModalState {
  [key: string]: boolean
}

export interface SidebarState {
  open: boolean
  collapsed: boolean
}

export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

export interface FormFieldError {
  field: string
  message: string
}

export interface FormState<T = any> {
  data: T
  errors: FormFieldError[]
  isSubmitting: boolean
  isDirty: boolean
  isValid: boolean
}
```

## ✅ 验收标准

### 功能验收
- [ ] Next.js项目成功创建并运行
- [ ] TypeScript严格模式无错误
- [ ] TailwindCSS样式系统正常工作
- [ ] 主题切换功能正常
- [ ] 状态管理系统正常运行
- [ ] 基础UI组件渲染正常
- [ ] 动画效果流畅
- [ ] 项目结构清晰规范

### 性能验收
- [ ] 开发服务器启动时间 < 10秒
- [ ] 热重载响应时间 < 2秒
- [ ] 构建时间 < 30秒
- [ ] 包体积优化合理

### 代码质量验收
- [ ] ESLint检查无错误
- [ ] Prettier格式化一致
- [ ] TypeScript覆盖率100%
- [ ] 组件可复用性良好

## 🧪 测试验证

### 开发环境测试
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 格式化代码
npm run format

# 构建项目
npm run build
```

### 功能测试清单
- [ ] 页面正常渲染
- [ ] 主题切换正常
- [ ] 状态管理正常
- [ ] 组件交互正常
- [ ] 动画效果正常
- [ ] 响应式布局正常

## 📚 开发指南

### 组件开发规范
1. **组件命名**: 使用PascalCase
2. **文件命名**: 使用kebab-case
3. **Props接口**: 明确定义TypeScript接口
4. **默认导出**: 组件使用默认导出
5. **样式管理**: 使用TailwindCSS类名

### 状态管理规范
1. **Store分离**: 按功能模块分离Store
2. **类型安全**: 所有状态都有TypeScript类型
3. **持久化**: 合理选择需要持久化的状态
4. **选择器**: 使用选择器优化性能

### 代码提交规范
```bash
# 提交前检查
npm run lint
npm run type-check
npm run build

# 提交格式
git commit -m "feat: 添加基础UI组件"
git commit -m "fix: 修复主题切换问题"
git commit -m "docs: 更新开发文档"
```

## 🚀 下一步工作

### 立即可开始的工作
1. **API客户端集成** - 配置HTTP客户端
2. **路由系统完善** - 添加路由守卫
3. **表单系统** - 集成表单验证
4. **数据获取** - 集成数据获取库

### 团队分工建议
- **开发者A**: 专注UI组件库完善
- **开发者B**: 专注状态管理和API集成
- **开发者C**: 专注页面开发和路由系统

---

*此文档提供了完整的Next.js基础架构搭建指南，为后续的业务逻辑开发奠定了坚实的技术基础。*
```
```
```
