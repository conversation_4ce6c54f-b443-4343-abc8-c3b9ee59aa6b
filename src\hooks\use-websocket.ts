'use client'

import { useCallback, useEffect, useRef, useState } from 'react'
import { io, Socket } from 'socket.io-client'

// 连接状态枚举
export enum WebSocketStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

// 消息类型
export interface WebSocketMessage {
  event: string
  data: any
  timestamp: number
}

// 配置选项
export interface WebSocketConfig {
  url: string
  namespace?: string
  autoConnect?: boolean
  debug?: boolean
  reconnectAttempts?: number
  reconnectInterval?: number
  timeout?: number
  heartbeatInterval?: number
  transports?: ('polling' | 'websocket')[]
}

// 事件回调
export interface WebSocketCallbacks {
  onConnect?: () => void
  onDisconnect?: (reason: string) => void
  onError?: (error: Error) => void
  onMessage?: (event: string, data: any) => void
  onReconnect?: (attempt: number) => void
  onReconnectFailed?: () => void
}

// Hook返回值
export interface UseWebSocketReturn {
  socket: Socket | null
  status: WebSocketStatus
  lastMessage: WebSocketMessage | null
  error: Error | null
  isConnected: boolean
  reconnectAttempts: number
  connect: () => void
  disconnect: () => void
  send: (event: string, data?: any) => void
}

// 默认配置
const DEFAULT_CONFIG: Required<Omit<WebSocketConfig, 'url'>> = {
  namespace: '/',
  autoConnect: true,
  debug: false,
  reconnectAttempts: 5,
  reconnectInterval: 1000,
  timeout: 20000,
  heartbeatInterval: 25000,
  transports: ['websocket', 'polling'],
}

// 过滤的内部事件
const INTERNAL_EVENTS = new Set([
  'connect', 'disconnect', 'connect_error', 'reconnect', 
  'reconnect_attempt', 'reconnect_failed', 'ping', 'pong'
])

/**
 * Socket.IO WebSocket Hook
 * 提供企业级实时通信连接管理
 */
export function useWebSocket(
  config: WebSocketConfig,
  callbacks?: WebSocketCallbacks
): UseWebSocketReturn {
  // 合并配置
  const options = { ...DEFAULT_CONFIG, ...config }
  
  // 状态管理
  const [state, setState] = useState({
    status: WebSocketStatus.DISCONNECTED,
    lastMessage: null as WebSocketMessage | null,
    error: null as Error | null,
    reconnectAttempts: 0,
  })
  
  // refs
  const socketRef = useRef<Socket | null>(null)
  const heartbeatRef = useRef<NodeJS.Timeout | null>(null)
  const isUnmountedRef = useRef(false)
  
  // 状态更新辅助函数
  const updateState = useCallback((updates: Partial<typeof state>) => {
    setState(prev => ({ ...prev, ...updates }))
  }, [])
  
  // 调试日志
  const log = useCallback((message: string, ...args: any[]) => {
    if (options.debug) {
      console.log(`[useWebSocket] ${message}`, ...args)
    }
  }, [options.debug])
  
  // 错误处理
  const handleError = useCallback((error: Error, context: string) => {
    log(`${context}:`, error)
    updateState({ error })
    callbacks?.onError?.(error)
  }, [log, updateState, callbacks])
  
  // 清理心跳
  const clearHeartbeat = useCallback(() => {
    if (heartbeatRef.current) {
      clearInterval(heartbeatRef.current)
      heartbeatRef.current = null
    }
  }, [])
  
  // 启动心跳
  const startHeartbeat = useCallback(() => {
    if (options.heartbeatInterval <= 0) return
    
    clearHeartbeat()
    heartbeatRef.current = setInterval(() => {
      if (socketRef.current?.connected) {
        socketRef.current.emit('ping', { timestamp: Date.now() })
        log('发送心跳')
      }
    }, options.heartbeatInterval)
  }, [options.heartbeatInterval, clearHeartbeat, log])
  
  // 设置Socket事件监听器
  const setupSocketListeners = useCallback((socket: Socket) => {
    // 连接成功
    socket.on('connect', () => {
      if (isUnmountedRef.current) return
      log('连接成功')
      updateState({ 
        status: WebSocketStatus.CONNECTED, 
        error: null, 
        reconnectAttempts: 0 
      })
      startHeartbeat()
      callbacks?.onConnect?.()
    })
    
    // 连接断开
    socket.on('disconnect', (reason: string) => {
      if (isUnmountedRef.current) return
      log('连接断开:', reason)
      clearHeartbeat()
      
      const status = ['io server disconnect', 'io client disconnect'].includes(reason)
        ? WebSocketStatus.DISCONNECTED
        : WebSocketStatus.RECONNECTING
      
      updateState({ status })
      callbacks?.onDisconnect?.(reason)
    })
    
    // 连接错误
    socket.on('connect_error', (err: Error) => {
      if (isUnmountedRef.current) return
      handleError(new Error(`连接失败: ${err.message}`), '连接错误')
      updateState({ status: WebSocketStatus.RECONNECTING })
    })
    
    // 重连成功
    socket.on('reconnect', (attempt: number) => {
      if (isUnmountedRef.current) return
      log('重连成功，尝试次数:', attempt)
      updateState({ 
        status: WebSocketStatus.CONNECTED, 
        reconnectAttempts: attempt 
      })
      callbacks?.onReconnect?.(attempt)
    })
    
    // 重连尝试
    socket.on('reconnect_attempt', (attempt: number) => {
      if (isUnmountedRef.current) return
      log('重连尝试:', attempt)
      updateState({ 
        status: WebSocketStatus.RECONNECTING, 
        reconnectAttempts: attempt 
      })
    })
    
    // 重连失败
    socket.on('reconnect_failed', () => {
      if (isUnmountedRef.current) return
      log('重连失败，已达到最大重连次数')
      updateState({ status: WebSocketStatus.ERROR })
      callbacks?.onReconnectFailed?.()
    })
    
    // 通用消息处理
    socket.onAny((event: string, ...args: any[]) => {
      if (isUnmountedRef.current || INTERNAL_EVENTS.has(event)) return
      
      const messageData: WebSocketMessage = {
        event,
        data: args.length === 1 ? args[0] : args,
        timestamp: Date.now(),
      }
      
      log('收到消息:', messageData)
      updateState({ lastMessage: messageData })
      callbacks?.onMessage?.(event, messageData.data)
    })
    
    // 心跳响应
    socket.on('pong', (data: any) => log('心跳响应:', data))
  }, [updateState, startHeartbeat, clearHeartbeat, callbacks, handleError, log])
  
  // 连接函数
  const connect = useCallback(() => {
    if (isUnmountedRef.current || socketRef.current?.connected) {
      log('跳过连接 - 已连接或组件已卸载')
      return
    }
    
    log('开始连接:', options.url)
    updateState({ status: WebSocketStatus.CONNECTING, error: null })
    
    try {
      const fullUrl = options.namespace !== '/' 
        ? `${options.url}${options.namespace}`
        : options.url
      
      const socket = io(fullUrl, {
        transports: options.transports,
        timeout: options.timeout,
        reconnection: true,
        reconnectionAttempts: options.reconnectAttempts,
        reconnectionDelay: options.reconnectInterval,
        autoConnect: false,
      })
      
      socketRef.current = socket
      setupSocketListeners(socket)
      socket.connect()
      
    } catch (err) {
      const error = err instanceof Error ? err : new Error('连接异常')
      handleError(error, '连接异常')
      updateState({ status: WebSocketStatus.ERROR })
    }
  }, [options, updateState, setupSocketListeners, handleError, log])
  
  // 断开连接
  const disconnect = useCallback(() => {
    log('主动断开连接')
    clearHeartbeat()
    
    if (socketRef.current) {
      socketRef.current.disconnect()
      socketRef.current = null
    }
    
    updateState({ 
      status: WebSocketStatus.DISCONNECTED, 
      reconnectAttempts: 0 
    })
  }, [clearHeartbeat, updateState, log])
  
  // 发送消息
  const send = useCallback((event: string, data?: any) => {
    if (!socketRef.current?.connected) {
      log('未连接，无法发送消息:', event, data)
      return
    }
    
    try {
      log('发送消息:', event, data)
      socketRef.current.emit(event, data)
    } catch (err) {
      handleError(
        err instanceof Error ? err : new Error('发送失败'), 
        '发送消息失败'
      )
    }
  }, [log, handleError])
  
  // 自动连接和清理
  useEffect(() => {
    if (options.autoConnect && options.url) {
      connect()
    }
    
    return () => {
      isUnmountedRef.current = true
      clearHeartbeat()
      if (socketRef.current) {
        socketRef.current.disconnect()
        socketRef.current = null
      }
    }
  }, [options.autoConnect, options.url, connect, clearHeartbeat])
  
  return {
    socket: socketRef.current,
    status: state.status,
    lastMessage: state.lastMessage,
    error: state.error,
    isConnected: state.status === WebSocketStatus.CONNECTED,
    reconnectAttempts: state.reconnectAttempts,
    connect,
    disconnect,
    send,
  }
} 