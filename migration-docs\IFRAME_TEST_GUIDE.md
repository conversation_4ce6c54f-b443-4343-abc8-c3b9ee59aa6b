# 🖼️ Iframe 嵌入测试指南

## 📝 问题分析

### 为什么直接访问 `https://dev.goglobalsp.com/embed/dashboard` 失败？

根据您的 nginx 配置分析，直接访问失败的原因如下：

```nginx
# nginx 配置中的限制
location ~* ^/embed/ {
    # Referer 白名单限制
    if ($http_referer !~* "^https://(www\.goglobalsp\.com|dev\.specific-ai\.com)") {
        return 403 "Invalid referer domain";
    }
    # ... 其他配置
}
```

**核心问题：**

1. **缺少 Referer 头部** - 直接在浏览器访问不会有正确的 Referer
2. **Referer 验证失败** - nginx 要求 Referer 必须来自指定域名
3. **双重验证** - nginx 和 Next.js 中间件都有安全验证

## 🎯 正确的测试方法

### 方法一：使用内置测试工具（推荐）

#### 1. 在服务器上访问测试页面

```bash
# 方式1：使用 HTML 测试工具
https://dev.goglobalsp.com/iframe-test.html

# 方式2：使用 Next.js 调试页面
https://dev.goglobalsp.com/iframe-debug
```

#### 2. 测试步骤

1. 选择环境（开发/生产）
2. 输入有效的 token
3. 点击"加载 Iframe"
4. 观察日志输出

### 方法二：本地开发调试

#### 1. 启动本地开发服务器

```bash
npm run dev
# 服务运行在 http://localhost:3000
```

#### 2. 访问本地测试页面

```bash
# HTML 测试工具
http://localhost:3000/iframe-test.html

# Next.js 调试页面
http://localhost:3000/iframe-debug
```

#### 3. 测试本地 iframe

```bash
# 嵌入页面 URL
http://localhost:3000/embed/dashboard?token=test-token-123
```

### 方法三：使用 curl 测试

#### 测试 nginx 层面的限制

```bash
# 错误示例：没有 Referer（会返回 403）
curl -I https://dev.goglobalsp.com/embed/dashboard

# 正确示例：带有正确的 Referer
curl -I -H "Referer: https://www.goglobalsp.com/" \
  https://dev.goglobalsp.com/embed/dashboard?token=your-token
```

#### 测试 Next.js 中间件

```bash
# 检查中间件响应
curl -v -H "Referer: https://www.goglobalsp.com/" \
  -H "User-Agent: Mozilla/5.0" \
  https://dev.goglobalsp.com/embed/dashboard?token=test-token
```

## 🔧 开发环境配置

### 1. 环境变量设置

```env
# .env.local
NODE_ENV=development
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NEXT_PUBLIC_EMBED_ENABLED=true
NEXT_PUBLIC_DEBUG=true
```

### 2. 中间件开发模式

开发环境下，中间件会：

- 允许 localhost 域名访问
- 允许无 Referer 的内网访问
- 输出详细的调试日志
- 放宽 token 验证要求

### 3. 本地测试 URL 格式

```
# 基本格式
http://localhost:3000/embed/dashboard?token=<your-token>

# 带测试参数
http://localhost:3000/embed/dashboard?token=test-token-123&test=true&timestamp=1234567890
```

## 🌐 生产环境测试

### 1. 创建测试父页面

创建一个位于允许域名的测试页面：

```html
<!-- 在 www.goglobalsp.com 上创建测试页面 -->
<!DOCTYPE html>
<html>
<head>
    <title>Iframe 测试</title>
</head>
<body>
    <h1>Iframe 嵌入测试</h1>
    <iframe 
        src="https://dev.goglobalsp.com/embed/dashboard?token=your-real-token"
        width="100%" 
        height="600"
        frameborder="0">
    </iframe>
</body>
</html>
```

### 2. 部署验证步骤

```bash
# 1. 确认服务运行
curl -I https://dev.goglobalsp.com/api/health

# 2. 检查中间件配置
curl -H "Referer: https://www.goglobalsp.com/" \
  https://dev.goglobalsp.com/embed/dashboard?token=test

# 3. 验证完整流程
# 在 www.goglobalsp.com 上嵌入并测试
```

## 🛠️ 故障排除

### 常见错误及解决方案

#### 1. 403 Forbidden - Invalid referer domain

**原因：** Referer 头部不在允许列表中
**解决：**

- 确保从允许的域名访问
- 检查 nginx 配置中的域名白名单
- 使用测试工具而不是直接访问

#### 2. 403 Forbidden - Missing referer header

**原因：** 缺少 Referer 头部且不是内网访问
**解决：**

- 使用 iframe 嵌入而不是直接访问
- 在开发环境下测试
- 检查 IP 是否在内网白名单中

#### 3. 401 Unauthorized - Missing token

**原因：** 生产环境缺少有效的认证 token
**解决：**

- 确保 URL 中包含有效的 token 参数
- 验证 token 格式是否正确（JWT 格式）
- 检查 token 是否已过期

#### 4. 500 Internal Server Error

**原因：** 服务器内部错误
**解决：**

- 查看服务器日志
- 检查 Next.js 应用是否正常运行
- 验证数据库连接等依赖服务

### 调试工具

#### 1. 浏览器开发者工具

- **Network 面板：** 查看请求响应状态
- **Console 面板：** 查看 JavaScript 错误
- **Application 面板：** 检查 cookie 和存储

#### 2. 服务器日志

```bash
# 查看 Next.js 应用日志
docker logs nextjs-web-ui-pd

# 查看 nginx 日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

#### 3. 中间件调试日志

在开发环境下，中间件会输出详细日志：

```
[EmbedSecurity] /embed/dashboard - IP: 127.0.0.1, Origin: null, Referer: http://localhost:3000/
[EmbedAccess] SUCCESS - /embed/dashboard
```

## 📋 测试清单

### 部署前检查

- [ ] 本地开发环境测试通过
- [ ] iframe 测试工具正常工作
- [ ] PostMessage 通信正常
- [ ] 认证流程验证成功
- [ ] 错误处理机制正常

### 部署后验证

- [ ] nginx 配置正确
- [ ] Next.js 服务正常运行
- [ ] 域名白名单配置正确
- [ ] SSL 证书有效
- [ ] 父页面可以正常嵌入 iframe

### 性能检查

- [ ] 页面加载速度正常
- [ ] iframe 高度自动调整
- [ ] 消息通信延迟在可接受范围
- [ ] 内存使用正常

## 📞 技术支持

如果遇到问题，请提供以下信息：

1. 错误的具体描述和截图
2. 浏览器开发者工具的 Network 面板信息
3. 服务器日志相关部分
4. 测试使用的 URL 和 token
5. 访问环境（本地/开发/生产）

---

**提示：** 为了安全考虑，iframe 嵌入功能必须在正确的父页面环境中测试，直接访问 embed 页面是不被允许的。
