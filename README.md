# Vibe Coding Refactory - 极简 Iframe 嵌入架构

一个极简的iframe嵌入解决方案，专为测试和快速集成设计。

## 🎯 项目目标

提供最简单的iframe嵌入功能，支持：

- 🔨 **开发环境** (dev) - 本地测试
- 🚀 **生产环境** (prod) - 线上部署
- 📱 **单页面** - 只保留dashboard页面
- 🖼️ **iframe测试** - 内置测试工具

## 📁 极简项目结构

```
src/
├── app/
│   ├── embed/
│   │   ├── [page]/page.tsx      # 只支持dashboard页面
│   │   └── layout.tsx           # 极简iframe布局
│   └── layout.tsx               # 主应用布局
├── components/embed/
│   ├── pages/DashboardPage.tsx  # 唯一的页面组件
│   └── EmbedPageWrapper.tsx     # 简单包装器
└── public/
    └── iframe-test.html         # 内置测试工具
```

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

### 3. 测试iframe功能

打开浏览器访问：

- **主应用**: [http://localhost:3000](http://localhost:3000)
- **嵌入页面**: [http://localhost:3000/embed/dashboard?token=test-token](http://localhost:3000/embed/dashboard?token=test-token)
- **测试工具**: [http://localhost:3000/iframe-test.html](http://localhost:3000/iframe-test.html) ⭐
- **UI 组件展示**: [http://localhost:3000/ui-gallery](http://localhost:3000/ui-gallery) 🎨
- **Markdown 展示**: [http://localhost:3000/markdown-demo](http://localhost:3000/markdown-demo) 📝

## 🖼️ Iframe 测试工具

访问 `http://localhost:3000/iframe-test.html` 使用内置的测试工具：

### 功能特性

- ✅ **环境切换** - dev/prod环境一键切换
- ✅ **Token测试** - 测试不同token的认证
- ✅ **实时日志** - 查看postMessage通信日志
- ✅ **自动调整** - 测试iframe高度自动调整
- ✅ **错误处理** - 展示认证和加载错误

### 测试步骤

1. 选择环境 (Development/Production)
2. 输入token (默认: `test-token-123`)
3. 点击"加载 Iframe"
4. 观察消息日志和状态变化

## 🎨 UI 组件展示

访问 `http://localhost:3000/ui-gallery` 查看所有 UI 组件的展示页面：

### 功能特性

- ✅ **完整组件展示** - 展示所有 Button、Input、Card、Badge 等组件
- ✅ **变体演示** - 展示不同尺寸、颜色、状态的组件变体
- ✅ **交互组件** - Dialog、Tooltip、DropdownMenu、Sheet 等交互测试
- ✅ **主题切换** - 支持亮色/暗色主题的组件样式测试
- ✅ **响应式设计** - 适配移动端和桌面端的组件展示
- ✅ **表单示例** - 提供完整的表单组合使用示例

### 组件分类

1. **基础组件**: Button、Input、Label、Separator
2. **布局组件**: Card、Skeleton、Avatar、Badge  
3. **导航组件**: Breadcrumb、Sidebar
4. **交互组件**: Dialog、Tooltip、DropdownMenu、Collapsible、Sheet
5. **表单组合**: 完整的用户信息表单示例

### 开发用途

- 🔍 **样式调试** - 检查组件在不同主题下的表现
- 🎯 **设计验证** - 验证组件设计是否符合预期
- 📱 **响应式测试** - 测试组件在不同屏幕尺寸下的表现
- 🧪 **交互测试** - 验证组件的交互逻辑是否正确

## 📝 Markdown 展示容器

访问 `http://localhost:3000/markdown-demo` 查看企业级 Markdown 渲染组件：

### 功能特性

- ✅ **完整的 CommonMark 支持** - 标准 Markdown 语法渲染
- ✅ **代码语法高亮** - 基于 highlight.js，支持 180+ 编程语言
- ✅ **安全的 HTML 处理** - 防止 XSS 攻击，安全渲染
- ✅ **链接安全增强** - 自动添加 `target="_blank"` 和 `rel="noopener noreferrer"`
- ✅ **图片懒加载** - 自动添加 `loading="lazy"` 优化性能
- ✅ **实时配置** - 动态调整渲染选项和设置

### 组件架构

1. **useMarkdown Hook** - 核心渲染逻辑封装
2. **MarkdownContainer** - 完整的展示容器组件
3. **三个标签页** - 预览/源码/设置 切换
4. **性能监控** - 渲染时间、统计信息展示

### 技术栈

- **markdown-it** - 高性能 Markdown 解析器
- **highlight.js** - 代码语法高亮
- **@tailwindcss/typography** - 美观的排版样式
- **Framer Motion** - 平滑的动画效果

### 使用示例

```tsx
import { MarkdownContainer } from '@/components/markdown-container'

<MarkdownContainer
  content="# Hello **World**!"
  title="我的文档"
  showControls={true}
  showStats={true}
  enableHighlight={true}
  onError={(error) => console.error(error)}
/>
```

## 🔧 环境配置

### 开发环境

复制 `env.example` 为 `.env.local`:

```bash
cp env.example .env.local
```

编辑配置：

```env
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NEXT_PUBLIC_EMBED_ENABLED=true
NEXT_PUBLIC_DEBUG=true
```

### 生产环境

创建 `.env.production`:

```env
NEXT_PUBLIC_APP_ENV=production
NEXT_PUBLIC_BASE_URL=https://your-domain.com
NEXT_PUBLIC_EMBED_ENABLED=true
NEXT_PUBLIC_DEBUG=false
```

## 📡 PostMessage 通信协议

### 从 iframe 发送的消息

```javascript
// 页面加载完成
{ type: 'PAGE_LOADED', data: { timestamp: number } }

// 高度调整
{ type: 'RESIZE', data: { height: number } }

// 认证错误
{ type: 'AUTH_ERROR', data: { message: string } }

// 一般错误
{ type: 'ERROR', data: { message: string } }
```

### 发送给 iframe 的消息

```javascript
// 测试调整大小
{ type: 'TEST_RESIZE', data: {} }
```

## 🔨 开发命令

```bash
# 开发环境
npm run dev

# 生产构建
npm run build

# 启动生产服务器
npm start

# 代码检查
npm run lint

# 类型检查
npm run type-check

# 测试提示
npm run test:iframe
```

## 🌐 嵌入页面URL

```
格式: {BASE_URL}/embed/dashboard?token={TOKEN}

开发: http://localhost:3000/embed/dashboard?token=test-token-123
生产: https://your-domain.com/embed/dashboard?token=real-token
```

## 🔐 安全特性

- **Token验证** - URL参数传递token
- **Origin检查** - postMessage安全验证
- **CSP配置** - 内容安全策略
- **错误隔离** - iframe错误不影响父页面

## 🎨 自定义样式

Dashboard页面使用内联样式，易于自定义：

```jsx
// src/components/embed/pages/DashboardPage.tsx
<div style={{ 
  background: '#f8f9fa', 
  padding: '20px', 
  borderRadius: '8px' 
}}>
  // 自定义内容
</div>
```

## 📝 集成示例

### Vue 3 集成

```vue
<template>
  <iframe 
    :src="iframeUrl" 
    @load="onIframeLoad"
    style="width: 100%; border: none;"
  />
</template>

<script setup>
const token = 'your-token-here'
const iframeUrl = `http://localhost:3000/embed/dashboard?token=${token}`

window.addEventListener('message', (event) => {
  if (event.data.type === 'AUTH_ERROR') {
    console.error('认证失败:', event.data.data.message)
  }
})
</script>
```

### React 集成

```jsx
function EmbedDashboard({ token }) {
  useEffect(() => {
    const handleMessage = (event) => {
      if (event.data.type === 'AUTH_ERROR') {
        console.error('认证失败:', event.data.data.message)
      }
    }
    
    window.addEventListener('message', handleMessage)
    return () => window.removeEventListener('message', handleMessage)
  }, [])

  return (
    <iframe 
      src={`http://localhost:3000/embed/dashboard?token=${token}`}
      style={{ width: '100%', border: 'none' }}
    />
  )
}
```

## 🔧 部署

### Vercel 部署

```bash
npm run build
# 部署到 Vercel
```

### Docker 部署

```bash
npm run build
npm start
```

---

**极简架构，专注测试 🎯**
