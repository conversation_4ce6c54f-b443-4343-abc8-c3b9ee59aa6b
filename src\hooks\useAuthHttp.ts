/**
 * 认证相关HTTP请求Hooks
 * 基于新的useHttp系统实现
 */

import React, { useCallback } from 'react'
import { useHttp, useHttpPresets } from './useHttp'
import type { LoginType, LoginParams, ApiUser as User } from '@/stores/auth-store'

// API响应类型
interface LoginResponse {
  token: string
  user: User
}

/**
 * 认证相关API Hooks
 */
export const useAuthHttp = {
  /**
   * 登录Hook
   */
  useLogin: () => {
    const { execute, ...state } = useHttpPresets.auth.post<LoginResponse>('/api/user/login')

    const login = useCallback(async (type: LoginType, params: LoginParams) => {
      // 根据登录类型转换参数
      let loginData: { user_name: string; password?: string; phone?: string; code?: string }

      if (type === 'password') {
        const passwordParams = params as Extract<LoginParams, { username: string }>
        loginData = {
          user_name: passwordParams.username,
          password: passwordParams.password
        }
      } else if (type === 'sms') {
        const smsParams = params as Extract<LoginParams, { phone: string }>
        loginData = {
          user_name: smsParams.phone,
          phone: smsParams.phone,
          code: smsParams.code
        }
      } else if (type === 'email') {
        const emailParams = params as Extract<LoginParams, { email: string }>
        loginData = {
          user_name: emailParams.email,
          code: emailParams.code
        }
      } else {
        throw new Error(`Unsupported login type: ${type}`)
      }

      return execute({
        body: loginData,
        showSuccess: true,
        successMessage: 'Login successful',
        errorMessage: 'Login failed'
      })
    }, [execute])

    return { ...state, login }
  },

  /**
   * 获取当前用户Hook
   */
  useGetCurrentUser: () => {
    return useHttpPresets.silent.get<User>('/api/user/get_current')
  },

  /**
   * 条件获取当前用户Hook (仅供IframeContainer使用)
   */
  useCurrentUserConditional: (shouldFetch: boolean) => {
    const { execute, ...state } = useHttpPresets.silent.get<User>('/api/user/get_current')
    
    React.useEffect(() => {
      if (shouldFetch && !state.executed && !state.loading) {
        execute().catch(error => {
          console.error('Conditional user fetch failed:', error)
        })
      }
    }, [shouldFetch, state.executed, state.loading, execute])

    return state
  },

  /**
   * 登出Hook
   */
  useLogout: () => {
    const { execute, ...state } = useHttpPresets.silent.post<void>('/api/user/logout')

    const logout = useCallback(async () => {
      try {
        await execute({
          body: {},
          showSuccess: false,
          showError: false
        })
      } catch (error) {
        // 即使API调用失败，也要继续本地清理
        console.warn('Logout API call failed, but continuing with local cleanup:', error)
      }
    }, [execute])

    return { ...state, logout }
  },

  /**
   * 刷新Token Hook
   */
  useRefreshToken: () => {
    return useHttpPresets.silent.post<{ token: string; refreshToken: string }>('/auth/refresh')
  },

  /**
   * 修改密码Hook
   */
  useChangePassword: () => {
    return useHttpPresets.save.put<void>('/auth/change-password')
  },

  /**
   * 忘记密码Hook
   */
  useForgotPassword: () => {
    const http = useHttp()
    return http.post<void>('/auth/forgot-password', {
      showError: true,
      showSuccess: true,
      successMessage: 'Password reset email sent',
      errorMessage: 'Failed to send password reset email'
    })
  },

  /**
   * 重置密码Hook
   */
  useResetPassword: () => {
    return useHttpPresets.save.post<void>('/auth/reset-password')
  }
}

/**
 * 用户管理相关API Hooks
 */
export const useUserHttp = {
  /**
   * 获取用户列表Hook
   */
  useGetUsers: () => {
    const http = useHttp()
    return http.get<User[]>('/api/users', {
      showError: true,
      showSuccess: false
    })
  },

  /**
   * 获取用户详情Hook
   */
  useGetUser: (id: string) => {
    const http = useHttp()
    return http.get<User>(`/api/users/${id}`, {
      showError: true,
      showSuccess: false
    })
  },

  /**
   * 创建用户Hook
   */
  useCreateUser: () => {
    return useHttpPresets.save.post<User>('/api/users')
  },

  /**
   * 更新用户Hook
   */
  useUpdateUser: (id: string) => {
    return useHttpPresets.save.put<User>(`/api/users/${id}`)
  },

  /**
   * 删除用户Hook
   */
  useDeleteUser: (id: string) => {
    return useHttpPresets.delete<void>(`/api/users/${id}`)
  }
}

/**
 * 权限管理相关API Hooks
 */
export const usePermissionHttp = {
  /**
   * 获取菜单配置Hook
   */
  useGetMenuConfig: () => {
    const http = useHttp()
    return http.get<any[]>('/auth/menu-config', {
      showError: true,
      showSuccess: false
    })
  },

  /**
   * 获取功能配置Hook
   */
  useGetFeatureConfig: () => {
    const http = useHttp()
    return http.get<any[]>('/auth/feature-config', {
      showError: true,
      showSuccess: false
    })
  },

  /**
   * 获取用户角色Hook
   */
  useGetUserRoles: () => {
    const http = useHttp()
    return http.get<any[]>('/auth/user-roles', {
      showError: true,
      showSuccess: false
    })
  },

  /**
   * 获取用户权限Hook
   */
  useGetUserPermissions: () => {
    const http = useHttp()
    return http.get<any[]>('/auth/user-permissions', {
      showError: true,
      showSuccess: false
    })
  }
}

/**
 * 组合Hook：完整的认证流程
 */
export function useAuthFlow() {
  const { login, ...loginState } = useAuthHttp.useLogin()
  const { logout, ...logoutState } = useAuthHttp.useLogout()
  const { execute: getCurrentUser, ...getCurrentUserState } = useAuthHttp.useGetCurrentUser()

  return {
    // 状态
    loginState,
    logoutState,
    getCurrentUserState,
    
    // 操作
    login,
    logout,
    getCurrentUser,
    
    // 便捷状态
    isLoading: loginState.loading || logoutState.loading || getCurrentUserState.loading,
    hasError: !!(loginState.error || logoutState.error || getCurrentUserState.error)
  }
}
