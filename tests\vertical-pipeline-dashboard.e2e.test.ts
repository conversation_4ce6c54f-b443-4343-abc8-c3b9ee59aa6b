/**
 * Vertical Pipeline Dashboard E2E 测试
 * 使用Playwright测试实际的滚动和页面抖动问题
 */

import { test, expect, Page } from '@playwright/test'

const TEST_URL = 'http://localhost:3000/zh/embed/dashboard?from-og=true&token=test-token-123'

test.describe('Vertical Pipeline Dashboard - 滚动和抖动测试', () => {
  let page: Page

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage
    await page.goto(TEST_URL)
    await page.waitForLoadState('networkidle')
  })

  test.describe('自动滚动功能验证', () => {
    test('应该在新内容添加时自动滚动到底部', async () => {
      // 等待第一个步骤出现
      await page.waitForSelector('[data-testid="step-card"]', { timeout: 10000 })
      
      // 获取初始滚动位置
      const initialScrollTop = await page.evaluate(() => {
        const container = document.querySelector('[data-testid="scroll-container"]') as HTMLElement
        return container?.scrollTop || 0
      })

      // 等待更多内容添加
      await page.waitForTimeout(5000)

      // 获取最终滚动位置
      const finalScrollTop = await page.evaluate(() => {
        const container = document.querySelector('[data-testid="scroll-container"]') as HTMLElement
        return container?.scrollTop || 0
      })

      // 验证页面已经滚动
      expect(finalScrollTop).toBeGreaterThan(initialScrollTop)
    })

    test('应该在工具状态更新时保持滚动到最新内容', async () => {
      // 等待流水线开始执行
      await page.waitForSelector('.animate-pulse', { timeout: 10000 })
      
      // 等待第一个工具完成
      await page.waitForSelector('[data-status="completed"]', { timeout: 15000 })

      // 检查页面是否滚动到了最新内容
      const isAtBottom = await page.evaluate(() => {
        const container = document.querySelector('[data-testid="scroll-container"]') as HTMLElement
        if (!container) return false
        
        const { scrollTop, scrollHeight, clientHeight } = container
        return scrollTop + clientHeight >= scrollHeight - 50 // 50px tolerance
      })

      expect(isAtBottom).toBe(true)
    })

    test('应该在数据更新时触发滚动', async () => {
      // 等待第一个步骤的工具开始运行
      await page.waitForSelector('[data-status="running"]', { timeout: 10000 })

      // 记录滚动事件
      const scrollEvents = []
      await page.evaluate(() => {
        const container = document.querySelector('[data-testid="scroll-container"]')
        if (container) {
          container.addEventListener('scroll', () => {
            (window as any).scrollEventCount = ((window as any).scrollEventCount || 0) + 1
          })
        }
      })

      // 等待数据更新
      await page.waitForTimeout(8000)

      // 检查是否有滚动事件
      const scrollEventCount = await page.evaluate(() => (window as any).scrollEventCount || 0)
      expect(scrollEventCount).toBeGreaterThan(0)
    })
  })

  test.describe('页面宽度抖动测试', () => {
    test('应该没有横向滚动条', async () => {
      // 检查页面是否有横向滚动
      const hasHorizontalScroll = await page.evaluate(() => {
        const container = document.querySelector('[data-testid="scroll-container"]') as HTMLElement
        if (!container) return false
        return container.scrollWidth > container.clientWidth
      })

      expect(hasHorizontalScroll).toBe(false)
    })

    test('应该在内容更新时保持页面宽度稳定', async () => {
      // 获取初始页面宽度
      const initialWidth = await page.evaluate(() => {
        const container = document.querySelector('[data-testid="scroll-container"]') as HTMLElement
        return container?.clientWidth || 0
      })

      // 等待内容更新
      await page.waitForTimeout(10000)

      // 获取更新后的页面宽度
      const finalWidth = await page.evaluate(() => {
        const container = document.querySelector('[data-testid="scroll-container"]') as HTMLElement
        return container?.clientWidth || 0
      })

      // 验证宽度没有变化
      expect(finalWidth).toBe(initialWidth)
    })

    test('应该正确处理长URL内容', async () => {
      // 等待包含URL列表的步骤出现
      await page.waitForSelector('[data-testid="url-list"]', { timeout: 20000 })

      // 检查URL内容是否正确换行
      const urlElements = await page.locator('[data-testid="url-item"]').all()
      
      for (const urlElement of urlElements) {
        const boundingBox = await urlElement.boundingBox()
        const containerBox = await page.locator('[data-testid="scroll-container"]').boundingBox()
        
        if (boundingBox && containerBox) {
          // 验证URL元素没有超出容器宽度
          expect(boundingBox.x + boundingBox.width).toBeLessThanOrEqual(containerBox.x + containerBox.width)
        }
      }
    })
  })

  test.describe('滚动按钮功能测试', () => {
    test('应该在用户不在底部时显示滚动按钮', async () => {
      // 等待内容加载
      await page.waitForSelector('[data-testid="step-card"]', { timeout: 10000 })
      
      // 滚动到顶部
      await page.evaluate(() => {
        const container = document.querySelector('[data-testid="scroll-container"]') as HTMLElement
        if (container) {
          container.scrollTop = 0
        }
      })

      // 等待滚动按钮出现
      await page.waitForSelector('[data-testid="scroll-to-bottom-button"]', { timeout: 5000 })

      // 验证按钮可见
      const button = page.locator('[data-testid="scroll-to-bottom-button"]')
      await expect(button).toBeVisible()
    })

    test('应该在点击滚动按钮时滚动到底部', async () => {
      // 等待内容加载并滚动到顶部
      await page.waitForSelector('[data-testid="step-card"]', { timeout: 10000 })
      await page.evaluate(() => {
        const container = document.querySelector('[data-testid="scroll-container"]') as HTMLElement
        if (container) container.scrollTop = 0
      })

      // 点击滚动按钮
      await page.waitForSelector('[data-testid="scroll-to-bottom-button"]', { timeout: 5000 })
      await page.click('[data-testid="scroll-to-bottom-button"]')

      // 等待滚动完成
      await page.waitForTimeout(1000)

      // 验证已滚动到底部
      const isAtBottom = await page.evaluate(() => {
        const container = document.querySelector('[data-testid="scroll-container"]') as HTMLElement
        if (!container) return false
        
        const { scrollTop, scrollHeight, clientHeight } = container
        return scrollTop + clientHeight >= scrollHeight - 50
      })

      expect(isAtBottom).toBe(true)
    })
  })

  test.describe('性能和稳定性测试', () => {
    test('应该在快速内容更新时保持性能', async () => {
      const startTime = Date.now()
      
      // 等待完整的流水线执行
      await page.waitForSelector('[data-testid="step-card"]', { timeout: 10000 })
      await page.waitForTimeout(30000) // 等待多个步骤完成

      const endTime = Date.now()
      const duration = endTime - startTime

      // 验证页面响应时间合理
      expect(duration).toBeLessThan(35000) // 应该在35秒内完成
    })

    test('应该在循环重新开始时正确清空和重置', async () => {
      // 等待第一轮流水线完成
      await page.waitForSelector('[data-testid="step-card"]', { timeout: 10000 })
      await page.waitForTimeout(40000) // 等待完整流水线完成

      // 验证页面被清空
      await page.waitForFunction(() => {
        const steps = document.querySelectorAll('[data-testid="step-card"]')
        return steps.length === 0
      }, { timeout: 10000 })

      // 等待新一轮开始
      await page.waitForSelector('[data-testid="step-card"]', { timeout: 10000 })

      // 验证新一轮从第一个步骤开始
      const firstStepNumber = await page.locator('[data-testid="step-number"]').first().textContent()
      expect(firstStepNumber).toBe('1')
    })
  })

  test.describe('边界情况测试', () => {
    test('应该在网络慢的情况下正常工作', async () => {
      // 模拟慢网络
      await page.route('**/*', route => {
        setTimeout(() => route.continue(), 100) // 100ms delay
      })

      await page.reload()
      await page.waitForLoadState('networkidle')

      // 验证功能仍然正常
      await page.waitForSelector('[data-testid="step-card"]', { timeout: 15000 })
      const stepCard = page.locator('[data-testid="step-card"]').first()
      await expect(stepCard).toBeVisible()
    })

    test('应该在窗口大小变化时保持布局稳定', async () => {
      // 测试不同的窗口大小
      const viewports = [
        { width: 1920, height: 1080 },
        { width: 1366, height: 768 },
        { width: 768, height: 1024 }
      ]

      for (const viewport of viewports) {
        await page.setViewportSize(viewport)
        await page.waitForTimeout(1000)

        // 验证没有横向滚动
        const hasHorizontalScroll = await page.evaluate(() => {
          const container = document.querySelector('[data-testid="scroll-container"]') as HTMLElement
          return container ? container.scrollWidth > container.clientWidth : false
        })

        expect(hasHorizontalScroll).toBe(false)
      }
    })
  })
})
