version: '3.8'

services:
  vibe-nextjs:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vibe-nextjs-app
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_APP_ENV=production
      - NEXT_PUBLIC_BASE_URL=https://dev.goglobalsp.com
      - NEXT_PUBLIC_EMBED_ENABLED=true
      - NEXT_PUBLIC_DEBUG=false
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  app-network:
    driver: bridge

# Optional: Volume for persistent data if needed
# volumes:
#   app-data:
#     driver: local 