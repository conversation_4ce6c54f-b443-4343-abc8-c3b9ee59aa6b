/**
 * 静态资源修复中间件
 * 解决iframe环境下JavaScript文件返回HTML的问题
 */

import { NextRequest, NextResponse } from 'next/server'

export function staticResourceMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // 检查是否是静态资源请求
  if (pathname.startsWith('/_next/static/')) {
    // 确保正确的Content-Type头部
    const response = NextResponse.next()
    
    if (pathname.endsWith('.js')) {
      response.headers.set('Content-Type', 'application/javascript; charset=utf-8')
      response.headers.set('Cache-Control', 'public, max-age=31536000, immutable')
      response.headers.set('Access-Control-Allow-Origin', '*')
    } else if (pathname.endsWith('.css')) {
      response.headers.set('Content-Type', 'text/css; charset=utf-8')
      response.headers.set('Cache-Control', 'public, max-age=31536000, immutable')
      response.headers.set('Access-Control-Allow-Origin', '*')
    } else if (pathname.endsWith('.woff2')) {
      response.headers.set('Content-Type', 'font/woff2')
      response.headers.set('Cache-Control', 'public, max-age=31536000, immutable')
      response.headers.set('Access-Control-Allow-Origin', '*')
    }
    
    return response
  }
  
  return NextResponse.next()
}
