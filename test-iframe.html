<!DOCTYPE html>
<html>
<head>
    <title>测试iframe访问受限</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .description {
            margin-bottom: 15px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>iframe访问受限测试</h1>
        
        <div class="test-section">
            <h2>测试1：需要认证的页面（根路径）</h2>
            <div class="description">
                这个iframe应该显示"访问受限"页面（React组件层面检测）
            </div>
            <iframe src="http://localhost:3000/zh"></iframe>
        </div>
        
        <div class="test-section">
            <h2>测试2：embed页面（有token）</h2>
            <div class="description">
                这个iframe应该正常显示embed页面内容
            </div>
            <iframe src="http://localhost:3000/zh/embed/dashboard?from-og=true&token=test-token-123"></iframe>
        </div>
        
        <div class="test-section">
            <h2>测试3：embed页面（无token）</h2>
            <div class="description">
                这个iframe应该显示认证错误页面
            </div>
            <iframe src="http://localhost:3000/zh/embed/dashboard?from-og=true"></iframe>
        </div>
    </div>
</body>
</html>
