/**
 * 守卫上下文构建器
 * 从请求中提取用户信息和权限数据
 */

import { NextRequest } from 'next/server'
import { getRouteConfig } from '../route-config'
import { validateTokenOnServer } from '../auth/token-validator'
import type { GuardContext } from './types'
import type { ApiUser as User, UserPermission, UserRole, MenuConfig, FeatureConfig } from '@/stores/auth-store'

/**
 * 构建守卫上下文
 * @param request Next.js请求对象
 * @returns 守卫上下文
 */
export async function buildGuardContext(request: NextRequest): Promise<GuardContext> {
  const pathname = request.nextUrl.pathname
  const searchParams = request.nextUrl.searchParams
  const routeConfig = getRouteConfig(pathname)

  // 从cookie获取认证token
  const token = request.cookies.get('auth-token')?.value

  // 初始化用户数据
  let user: User | null = null
  let userPermissions: UserPermission[] = []
  let userRoles: UserRole[] = []
  let menuConfig: MenuConfig[] = []
  let featureConfig: FeatureConfig[] = []

  // 如果有token，尝试验证并获取用户信息
  if (token) {
    try {
      const authData = await validateTokenOnServer(token)
      if (authData) {
        user = authData.user
        userPermissions = authData.permissions
        userRoles = authData.roles
        menuConfig = authData.menuConfig
        featureConfig = authData.featureConfig
      }
    } catch (error) {
      console.error('Failed to validate token in middleware:', error)
      // Token验证失败，保持用户为null状态
    }
  }

  return {
    request,
    pathname,
    searchParams,
    routeConfig,
    user,
    userPermissions,
    userRoles,
    menuConfig,
    featureConfig
  }
}

/**
 * 从请求头构建上下文（用于API路由）
 * @param request Next.js请求对象
 * @returns 简化的守卫上下文
 */
export async function buildApiContext(request: NextRequest): Promise<Partial<GuardContext>> {
  const pathname = request.nextUrl.pathname
  
  // 从Authorization头获取token
  const authHeader = request.headers.get('authorization')
  const token = authHeader?.startsWith('Bearer ') ? authHeader.slice(7) : null

  let user: User | null = null
  let userPermissions: UserPermission[] = []
  let userRoles: UserRole[] = []

  if (token) {
    try {
      const authData = await validateTokenOnServer(token)
      if (authData) {
        user = authData.user
        userPermissions = authData.permissions
        userRoles = authData.roles
      }
    } catch (error) {
      console.error('Failed to validate API token:', error)
    }
  }

  return {
    request,
    pathname,
    user,
    userPermissions,
    userRoles
  }
}

/**
 * 缓存上下文构建器
 * 在同一个请求中缓存上下文，避免重复构建
 * 修复：移除setTimeout，使用Map的LRU策略
 */
export class ContextCache {
  private cache = new Map<string, { context: GuardContext; timestamp: number }>()
  private readonly maxCacheSize = 100
  private readonly cacheTimeout = 5 * 60 * 1000 // 5分钟

  async getContext(request: NextRequest): Promise<GuardContext> {
    const cacheKey = this.generateCacheKey(request)

    // 检查缓存
    const cached = this.cache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      // 更新访问时间（LRU）
      this.cache.delete(cacheKey)
      this.cache.set(cacheKey, cached)
      return cached.context
    }

    // 构建新的上下文
    const context = await buildGuardContext(request)

    // 存储到缓存
    this.setCache(cacheKey, context)

    return context
  }

  private generateCacheKey(request: NextRequest): string {
    const token = request.cookies.get('auth-token')?.value || 'anonymous'
    const pathname = request.nextUrl.pathname
    return `${token}-${pathname}`
  }

  private setCache(key: string, context: GuardContext): void {
    // 清理过期缓存
    this.cleanExpiredCache()

    // 如果缓存已满，删除最旧的条目（LRU）
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value
      if (firstKey) {
        this.cache.delete(firstKey)
      }
    }

    this.cache.set(key, { context, timestamp: Date.now() })
  }

  private cleanExpiredCache(): void {
    const now = Date.now()
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.cacheTimeout) {
        this.cache.delete(key)
      }
    }
  }

  clear(): void {
    this.cache.clear()
  }
}

// 全局上下文缓存实例
export const contextCache = new ContextCache()

/**
 * 快速上下文构建器（用于性能敏感场景）
 * 只提取必要的信息，跳过复杂的权限数据获取
 * 优化：更好的token验证和错误处理
 */
export async function buildLightContext(request: NextRequest): Promise<Partial<GuardContext>> {
  const pathname = request.nextUrl.pathname
  const routeConfig = getRouteConfig(pathname)
  const token = request.cookies.get('auth-token')?.value

  // 只检查token是否存在和有效，不获取完整用户信息
  let hasValidToken = false
  let userId: string | null = null

  if (token) {
    try {
      // 检查token格式（JWT格式：header.payload.signature）
      const parts = token.split('.')
      if (parts.length === 3) {
        // 解析payload
        const payload = JSON.parse(atob(parts[1]))

        // 检查过期时间
        if (payload.exp && Date.now() < payload.exp * 1000) {
          hasValidToken = true
          userId = payload.sub || payload.userId || payload.id
        }
      }
    } catch (error) {
      // Token格式错误或解析失败
      console.debug('[ContextBuilder] Invalid token format:', error)
      hasValidToken = false
    }
  }

  return {
    request,
    pathname,
    routeConfig,
    user: hasValidToken ? ({ id: userId || 'unknown' } as User) : null
  }
}
