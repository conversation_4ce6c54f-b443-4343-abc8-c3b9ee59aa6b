# 环境配置和测试指南

## 🌍 环境配置

### 支持的环境

| 环境 | Base URL | 描述 | 用途 |
|------|----------|------|------|
| **本地环境** | `http://localhost:3001` | 开发调试环境 | 本地开发和测试 |
| **开发环境** | `https://dev.goglobalsp.com` | 开发服务器 | 功能测试和集成测试 |
| **生产环境** | `https://www.goglobalsp.com` | 生产服务器 | 正式环境使用 |

### 域名白名单配置

系统已配置以下域名白名单：

```typescript
const ALLOWED_DOMAINS = [
  'https://www.goglobalsp.com',      // 生产环境
  'https://dev.goglobalsp.com'      // 开发环境
]
```

本地开发环境自动允许 `localhost`、`127.0.0.1` 等本地域名。

## 🧪 测试页面

### 测试Token

使用以下测试Token进行功能验证：

```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************.rgoBoEdQeUXU2G3dZSqQ49uidJp_fRkbSNuJbDzAJWE
```

**Token信息：**
- 用户ID: `684597...`
- 公司ID: `684597...`
- 邮箱: `<EMAIL>`
- 用户类型: `3`
- 过期时间: `2025-04-22 23:58:59`

### 可用的测试页面

#### 1. 测试页面 (推荐)
- **路径**: `/embed/test`
- **功能**: 完整的系统测试和环境检测
- **URL示例**:
  ```
  本地: http://localhost:3001/embed/test?token=your-token
  开发: https://dev.goglobalsp.com/embed/test?token=your-token
  生产: https://www.goglobalsp.com/embed/test?token=your-token
  ```

#### 2. 仪表板页面
- **路径**: `/embed/dashboard`
- **功能**: 数据仪表板展示
- **权限**: 读取权限

#### 3. 报告页面
- **路径**: `/embed/reports`
- **功能**: 报告查看和分析
- **权限**: 读取权限

#### 4. 工作区页面
- **路径**: `/embed/workspace`
- **功能**: 项目管理和协作
- **权限**: 读取和写入权限

## 🔧 快速测试

### 方法1: 使用内置测试页面

1. **启动开发服务器**
   ```bash
   npm run dev
   ```

2. **访问测试页面**
   ```
   http://localhost:3001/test-embed.html
   ```

3. **选择环境和页面进行测试**

### 方法2: 直接访问嵌入页面

使用提供的测试Token直接访问：

```bash
# 本地环境测试
curl "http://localhost:3001/embed/test?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

# 开发环境测试（需要部署后）
curl "https://dev.goglobalsp.com/embed/test?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### 方法3: 使用测试脚本

```bash
# 运行自动化测试
npm run test:embed

# 测试生产环境（需要部署后）
TEST_BASE_URL=https://dev.goglobalsp.com npm run test:embed
```

## 🔍 功能验证清单

### 基础功能测试

- [ ] **页面加载**: 嵌入页面能正常加载
- [ ] **Token认证**: Token验证通过
- [ ] **环境检测**: 正确识别当前环境
- [ ] **域名验证**: 只允许白名单域名访问

### 通信功能测试

- [ ] **高度调整**: iframe高度自动调整
- [ ] **消息传递**: 父子页面双向通信
- [ ] **心跳检测**: 连接状态监控
- [ ] **事件响应**: 页面事件正确传递

### 安全功能测试

- [ ] **域名限制**: 非白名单域名被拒绝
- [ ] **Token验证**: 无效Token被拒绝
- [ ] **安全头部**: 正确设置CSP等安全头部
- [ ] **跨域控制**: CORS策略正确配置

## 🚨 常见问题排查

### 1. 页面无法加载

**症状**: iframe显示空白或错误页面

**排查步骤**:
1. 检查Token是否有效且未过期
2. 确认域名在白名单中
3. 查看浏览器控制台错误信息
4. 验证服务器是否正常运行

### 2. 认证失败

**症状**: 返回401或403错误

**排查步骤**:
1. 验证Token格式是否正确（JWT格式）
2. 检查Token是否过期
3. 确认API服务器连接正常
4. 查看服务器日志

### 3. 通信异常

**症状**: iframe高度不调整或消息传递失败

**排查步骤**:
1. 检查PostMessage事件监听
2. 验证域名匹配
3. 查看浏览器控制台消息
4. 确认iframe已完全加载

## 📊 测试数据

### 测试用户信息

```json
{
  "user_id": "684597...",
  "company_id": "684597...",
  "email": "<EMAIL>",
  "user_type": 3,
  "exp": 1750644739
}
```

### 测试场景

1. **正常访问**: 使用有效Token和白名单域名
2. **无效Token**: 使用过期或格式错误的Token
3. **非法域名**: 从非白名单域名访问
4. **通信测试**: 验证iframe双向通信
5. **高度调整**: 测试自动高度调整功能

## 🔄 环境切换

### 开发环境配置

```env
NODE_ENV=development
API_BASE_URL=https://dev-api.specific-ai.com
ALLOWED_EMBED_DOMAINS=https://dev.goglobalsp.com
NEXT_PUBLIC_APP_URL=https://dev.goglobalsp.com
```

### 生产环境配置

```env
NODE_ENV=production
API_BASE_URL=https://api.goglobalsp.com
ALLOWED_EMBED_DOMAINS=https://www.goglobalsp.com,https://dev.goglobalsp.com
NEXT_PUBLIC_APP_URL=https://www.goglobalsp.com
```

## 📝 测试报告模板

```markdown
## 测试报告

**测试时间**: 2024-01-XX XX:XX:XX
**测试环境**: [本地/开发/生产]
**测试人员**: [姓名]

### 测试结果

| 功能 | 状态 | 备注 |
|------|------|------|
| 页面加载 | ✅/❌ | |
| Token认证 | ✅/❌ | |
| 环境检测 | ✅/❌ | |
| 高度调整 | ✅/❌ | |
| 消息通信 | ✅/❌ | |
| 安全验证 | ✅/❌ | |

### 问题记录

1. [问题描述]
   - 复现步骤: 
   - 错误信息: 
   - 解决方案: 

### 总结

[测试总结和建议]
```

---

*此文档提供了完整的环境配置和测试指南，确保嵌入系统在各种环境下正常工作。*
