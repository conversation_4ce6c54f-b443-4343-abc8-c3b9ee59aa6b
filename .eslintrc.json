{"extends": ["next/core-web-vitals"], "rules": {"prefer-const": "error", "no-var": "error"}, "ignorePatterns": ["node_modules/", ".next/", "out/", "build/", "dist/", "public/*.html", "docs/", "migration-docs/", "**.md", "**/README*", "**/Todo*", "**/Dockerfile*", "**/docker-compose*", "**/.<PERSON><PERSON><PERSON><PERSON>", "**/deploy.sh", "**/quick_deploy.sh", "**/build_push_image.sh", "**/package-lock.json", "**/pnpm-lock.yaml", "**/tsconfig.tsbuildinfo", "**/.cursor/", "**/.vscode/", "**/.idea/", "**/*.log", "**/coverage/"]}