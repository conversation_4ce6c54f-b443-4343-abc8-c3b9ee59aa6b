"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ToolCard } from "./tool-card"
import type { StepData } from "./vertical-pipeline-dashboard"
import { Clock, CheckCircle, AlertCircle, Loader2, Calendar } from "lucide-react"

interface StepCardProps {
  step: StepData
  index: number
}

const statusConfig = {
  pending: {
    color: "bg-slate-50 text-slate-600 border-slate-200",
    icon: Clock,
    borderColor: "border-slate-200",
    bgColor: "bg-slate-50",
  },
  running: {
    color: "bg-blue-50 text-blue-700 border-blue-200",
    icon: Loader2,
    borderColor: "border-blue-300",
    bgColor: "bg-blue-50",
  },
  completed: {
    color: "bg-emerald-50 text-emerald-700 border-emerald-200",
    icon: CheckCircle,
    borderColor: "border-emerald-300",
    bgColor: "bg-emerald-50",
  },
  error: {
    color: "bg-red-50 text-red-700 border-red-200",
    icon: AlertCircle,
    borderColor: "border-red-300",
    bgColor: "bg-red-50",
  },
}

export function StepCard({ step, index }: StepCardProps) {
  const config = statusConfig[step.status]
  const StatusIcon = config.icon

  const completedTools = step.tools.filter((tool) => tool.status === "completed").length
  const totalTools = step.tools.length
  const progressPercentage = totalTools > 0 ? (completedTools / totalTools) * 100 : 0

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString("en-US", {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    })
  }

  return (
    <motion.div layout className="w-full max-w-3xl mx-auto" whileHover={{ scale: 1.01 }} transition={{ duration: 0.2 }}>
      <Card className={`bg-white shadow-lg border-0 ${config.borderColor} transition-all duration-300`}>
        <CardHeader className="pb-4">
          {/* Step Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-4">
              <motion.div
                className={`w-12 h-12 rounded-full ${config.bgColor} flex items-center justify-center shadow-sm`}
                whileHover={{ scale: 1.1 }}
                transition={{ duration: 0.2 }}
              >
                <span className="text-lg font-bold text-slate-700">{index + 1}</span>
              </motion.div>
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="text-xl font-semibold text-slate-900">{step.title}</h3>
                  <Badge variant="outline" className={`${config.color} font-medium px-3 py-1`}>
                    <StatusIcon className={`w-4 h-4 mr-2 ${step.status === "running" ? "animate-spin" : ""}`} />
                    {step.status}
                  </Badge>
                </div>
                <p className="text-slate-600 leading-relaxed">{step.description}</p>
              </div>
            </div>
          </div>

          {/* Step Metadata */}
          <div className="flex items-center justify-between text-sm text-slate-500 mb-4">
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              <span>Created: {formatTime(step.createdAt)}</span>
            </div>
            {totalTools > 0 && (
              <div className="flex items-center gap-4">
                <span className="font-medium">
                  Progress: {completedTools}/{totalTools} tools
                </span>
                <div className="w-24">
                  <Progress value={progressPercentage} className="h-2" />
                </div>
              </div>
            )}
          </div>
        </CardHeader>

        {/* Tools Section */}
        {step.tools.length > 0 && (
          <CardContent className="pt-0">
            <div className="space-y-4">
              <h4 className="text-sm font-semibold text-slate-700 uppercase tracking-wide">Tools & Results</h4>
              <div className="space-y-3">
                {step.tools.map((tool, toolIndex) => (
                  <motion.div
                    key={tool.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: toolIndex * 0.1, duration: 0.4 }}
                  >
                    <ToolCard tool={tool} />
                  </motion.div>
                ))}
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Enhanced Pulsing Effect for Running Steps */}
      {step.status === "running" && (
        <>
          <motion.div
            className="absolute inset-0 rounded-xl border-2 border-blue-300/50 pointer-events-none"
            animate={{
              opacity: [0.3, 0.8, 0.3],
              scale: [1, 1.005, 1],
            }}
            transition={{
              duration: 2.5,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut",
            }}
          />
          <motion.div
            className="absolute inset-0 rounded-xl bg-blue-400/5 pointer-events-none"
            animate={{
              opacity: [0, 0.3, 0],
            }}
            transition={{
              duration: 2.5,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut",
            }}
          />
        </>
      )}
    </motion.div>
  )
}
