"use client"

import { motion } from "framer-motion"
import { StepCard } from "./step-card"
import { EnhancedConnector } from "./enhanced-connector"
import { useEffect, useState } from "react"

export type ToolOutput = {
  id: string
  name: string
  component_id: "TextRender" | "UrlListRender" | "SummaryRender"
  status: "pending" | "running" | "completed" | "error"
  provider?: string
  outputType?: string
  outputSnippet?: string
  outputPayload?: any
  startedAt?: string
  finishedAt?: string
  data?: any
}

export type StepData = {
  id: string
  title: string
  description: string
  status: "pending" | "running" | "completed" | "error"
  tools: ToolOutput[]
  createdAt: string
}

interface PipelineDashboardProps {
  steps: StepData[]
}

export function PipelineDashboard({ steps }: PipelineDashboardProps) {
  const [screenSize, setScreenSize] = useState({ width: 0, height: 0 })

  useEffect(() => {
    const updateScreenSize = () => {
      setScreenSize({ width: window.innerWidth, height: window.innerHeight })
    }

    updateScreenSize()
    window.addEventListener("resize", updateScreenSize)
    return () => window.removeEventListener("resize", updateScreenSize)
  }, [])

  // Calculate columns based on screen width
  const getColumnsPerRow = () => {
    if (screenSize.width >= 1536) return 4 // 2xl
    if (screenSize.width >= 1280) return 3 // xl
    if (screenSize.width >= 1024) return 3 // lg
    if (screenSize.width >= 768) return 2 // md
    return 1 // sm and below
  }

  const columnsPerRow = getColumnsPerRow()

  // Calculate S-shaped positioning
  const getStepPosition = (index: number) => {
    const row = Math.floor(index / columnsPerRow)
    const col = index % columnsPerRow
    const isEvenRow = row % 2 === 0

    return {
      row,
      col: isEvenRow ? col : columnsPerRow - 1 - col,
      isEvenRow,
      isLastInRow: col === columnsPerRow - 1,
      isFirstInRow: col === 0,
    }
  }

  return (
    <div className="relative">
      {/* Desktop and Tablet S-shaped Layout */}
      <div className="hidden md:block">
        <div
          className="grid gap-8 relative"
          style={{
            gridTemplateColumns: `repeat(${columnsPerRow}, 1fr)`,
            gridAutoRows: "auto",
          }}
        >
          {steps.map((step, index) => {
            const position = getStepPosition(index)

            return (
              <motion.div
                key={step.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.15, duration: 0.6, ease: "easeOut" }}
                className="relative"
                style={{
                  gridColumn: position.col + 1,
                  gridRow: position.row + 1,
                }}
              >
                <StepCard step={step} index={index} />

                {/* Enhanced Connectors */}
                {index < steps.length - 1 && (
                  <EnhancedConnector
                    isActive={step.status === "completed"}
                    currentPosition={position}
                    nextPosition={getStepPosition(index + 1)}
                    columnsPerRow={columnsPerRow}
                  />
                )}
              </motion.div>
            )
          })}
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="md:hidden space-y-8">
        {steps.map((step, index) => (
          <motion.div
            key={step.id}
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1, duration: 0.5 }}
            className="relative"
          >
            <StepCard step={step} index={index} />
            {index < steps.length - 1 && (
              <div className="flex justify-center my-6">
                <EnhancedConnector
                  isActive={step.status === "completed"}
                  currentPosition={{ row: index, col: 0, isEvenRow: true, isLastInRow: false, isFirstInRow: true }}
                  nextPosition={{ row: index + 1, col: 0, isEvenRow: true, isLastInRow: false, isFirstInRow: true }}
                  columnsPerRow={1}
                />
              </div>
            )}
          </motion.div>
        ))}
      </div>
    </div>
  )
}
