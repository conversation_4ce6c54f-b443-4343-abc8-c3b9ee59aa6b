# Next.js iframe 嵌入系统 Docker 部署指南

## 部署概述

本指南详细说明如何使用 Docker 部署 Next.js iframe 嵌入系统，包括单容器部署、环境配置、监控和故障排除。

## 部署架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Docker 容器                              │
│  ┌─────────────────────────────────────────────────────┐    │
│  │              Next.js 应用                           │    │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │    │
│  │  │ 嵌入页面    │  │ 中间件系统  │  │ API 端点    │  │    │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                             │
│  端口映射: 3000:3000                                        │
│  环境变量: API_BASE_URL, ALLOWED_EMBED_DOMAINS             │
│  健康检查: /api/embed/health                                │
└─────────────────────────────────────────────────────────────┘
```

## 1. Dockerfile 配置

### 1.1 生产环境 Dockerfile

创建 `Dockerfile`：

```dockerfile
# 多阶段构建 - 依赖安装阶段
FROM node:18-alpine AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# 复制包管理文件
COPY package.json package-lock.json* pnpm-lock.yaml* ./
RUN \
  if [ -f pnpm-lock.yaml ]; then \
    corepack enable pnpm && pnpm install --frozen-lockfile; \
  elif [ -f package-lock.json ]; then \
    npm ci; \
  else \
    echo "No lockfile found" && exit 1; \
  fi

# 构建阶段
FROM node:18-alpine AS builder
WORKDIR /app

# 复制依赖
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# 设置构建环境变量
ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_ENV production

# 构建应用
RUN \
  if [ -f pnpm-lock.yaml ]; then \
    corepack enable pnpm && pnpm build; \
  else \
    npm run build; \
  fi

# 运行时阶段
FROM node:18-alpine AS runner
WORKDIR /app

# 创建非 root 用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 设置运行时环境变量
ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# 复制构建产物
COPY --from=builder /app/public ./public

# 复制 Next.js 构建输出
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# 创建日志目录
RUN mkdir -p /app/logs && chown nextjs:nodejs /app/logs

# 切换到非 root 用户
USER nextjs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD node healthcheck.js

# 启动应用
CMD ["node", "server.js"]
```

### 1.2 健康检查脚本

创建 `healthcheck.js`：

```javascript
/**
 * Docker 健康检查脚本
 */

const http = require('http')

const options = {
  hostname: 'localhost',
  port: process.env.PORT || 3000,
  path: '/api/embed/health',
  method: 'GET',
  timeout: 5000
}

const request = http.request(options, (res) => {
  if (res.statusCode === 200) {
    console.log('Health check passed')
    process.exit(0)
  } else {
    console.log(`Health check failed with status: ${res.statusCode}`)
    process.exit(1)
  }
})

request.on('error', (err) => {
  console.log(`Health check error: ${err.message}`)
  process.exit(1)
})

request.on('timeout', () => {
  console.log('Health check timeout')
  request.destroy()
  process.exit(1)
})

request.end()
```

### 1.3 .dockerignore 配置

创建 `.dockerignore`：

```
# 依赖目录
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js 构建输出
.next
out

# 环境变量文件
.env*.local
.env.production

# 开发文件
.git
.gitignore
README.md
Dockerfile
.dockerignore

# 测试文件
coverage
.nyc_output
*.test.js
*.spec.js

# 日志文件
logs
*.log

# 临时文件
.DS_Store
Thumbs.db

# IDE 文件
.vscode
.idea
*.swp
*.swo

# 文档
docs
migration-docs
```

## 2. Docker Compose 配置

### 2.1 基础 docker-compose.yml

创建 `docker-compose.yml`：

```yaml
version: '3.8'

services:
  nextjs-embed:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    container_name: nextjs-embed-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
      - API_BASE_URL=${API_BASE_URL}
      - ALLOWED_EMBED_DOMAINS=${ALLOWED_EMBED_DOMAINS}
      - EMBED_TOKEN_SECRET=${EMBED_TOKEN_SECRET}
      - NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL}
    volumes:
      - ./logs:/app/logs
      - /etc/localtime:/etc/localtime:ro
    networks:
      - embed-network
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.nextjs-embed.rule=Host(`your-domain.com`)"
      - "traefik.http.routers.nextjs-embed.tls=true"
      - "traefik.http.services.nextjs-embed.loadbalancer.server.port=3000"

networks:
  embed-network:
    driver: bridge

volumes:
  logs:
    driver: local
```

### 2.2 生产环境 docker-compose.prod.yml

创建 `docker-compose.prod.yml`：

```yaml
version: '3.8'

services:
  nextjs-embed:
    image: your-registry/nextjs-embed:latest
    container_name: nextjs-embed-prod
    restart: always
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - API_BASE_URL=${API_BASE_URL}
      - ALLOWED_EMBED_DOMAINS=${ALLOWED_EMBED_DOMAINS}
      - EMBED_TOKEN_SECRET=${EMBED_TOKEN_SECRET}
      - NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL}
    volumes:
      - logs:/app/logs
      - /etc/localtime:/etc/localtime:ro
    networks:
      - embed-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx 反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: nextjs-embed-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - logs:/var/log/nginx
    depends_on:
      - nextjs-embed
    networks:
      - embed-network

networks:
  embed-network:
    driver: bridge

volumes:
  logs:
    driver: local
```

## 3. 环境配置

### 3.1 环境变量文件

创建 `.env.production`：

```env
# 应用配置
NODE_ENV=production
PORT=3000
HOSTNAME=0.0.0.0

# API 配置
API_BASE_URL=https://your-api-server.com
NEXT_PUBLIC_APP_URL=https://your-nextjs-app.com

# 嵌入系统配置
ALLOWED_EMBED_DOMAINS=https://www.goglobalsp.com,https://dev.goglobalsp.com
EMBED_TOKEN_SECRET=your-super-secret-key-here

# 安全配置
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=https://your-nextjs-app.com

# 日志配置
LOG_LEVEL=info
LOG_FILE=/app/logs/app.log

# 性能配置
NEXT_TELEMETRY_DISABLED=1
```

### 3.2 开发环境配置

创建 `.env.development`：

```env
# 开发环境配置
NODE_ENV=development
PORT=3000
HOSTNAME=localhost

# API 配置
API_BASE_URL=http://localhost:8080
NEXT_PUBLIC_APP_URL=http://localhost:3000

# 嵌入系统配置
ALLOWED_EMBED_DOMAINS=http://localhost:8081,http://localhost:3001
EMBED_TOKEN_SECRET=dev-secret-key

# 调试配置
DEBUG=true
LOG_LEVEL=debug
```

## 4. 部署脚本

### 4.1 构建脚本

创建 `scripts/build.sh`：

```bash
#!/bin/bash

# 构建脚本
set -e

echo "🚀 开始构建 Next.js 嵌入系统..."

# 检查环境
if [ ! -f ".env.production" ]; then
    echo "❌ 缺少 .env.production 文件"
    exit 1
fi

# 加载环境变量
source .env.production

# 构建 Docker 镜像
echo "📦 构建 Docker 镜像..."
docker build -t nextjs-embed:latest .

# 标记镜像
if [ ! -z "$DOCKER_REGISTRY" ]; then
    echo "🏷️  标记镜像..."
    docker tag nextjs-embed:latest $DOCKER_REGISTRY/nextjs-embed:latest
    docker tag nextjs-embed:latest $DOCKER_REGISTRY/nextjs-embed:$(date +%Y%m%d-%H%M%S)
fi

echo "✅ 构建完成"
```

### 4.2 部署脚本

创建 `scripts/deploy.sh`：

```bash
#!/bin/bash

# 部署脚本
set -e

echo "🚀 开始部署 Next.js 嵌入系统..."

# 检查 Docker 和 Docker Compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装"
    exit 1
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose -f docker-compose.prod.yml down

# 拉取最新镜像（如果使用镜像仓库）
if [ ! -z "$DOCKER_REGISTRY" ]; then
    echo "📥 拉取最新镜像..."
    docker pull $DOCKER_REGISTRY/nextjs-embed:latest
fi

# 启动服务
echo "🚀 启动服务..."
docker-compose -f docker-compose.prod.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 健康检查
echo "🏥 执行健康检查..."
if curl -f http://localhost:3000/api/embed/health; then
    echo "✅ 部署成功，服务正常运行"
else
    echo "❌ 部署失败，服务未正常启动"
    docker-compose -f docker-compose.prod.yml logs
    exit 1
fi

# 清理旧镜像
echo "🧹 清理旧镜像..."
docker image prune -f

echo "🎉 部署完成"
```

### 4.3 备份脚本

创建 `scripts/backup.sh`：

```bash
#!/bin/bash

# 备份脚本
set -e

BACKUP_DIR="/backup/nextjs-embed"
DATE=$(date +%Y%m%d-%H%M%S)

echo "💾 开始备份..."

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份日志
echo "📋 备份日志文件..."
tar -czf $BACKUP_DIR/logs-$DATE.tar.gz logs/

# 备份配置文件
echo "⚙️  备份配置文件..."
tar -czf $BACKUP_DIR/config-$DATE.tar.gz \
    .env.production \
    docker-compose.prod.yml \
    nginx.conf

# 导出 Docker 镜像
echo "🐳 导出 Docker 镜像..."
docker save nextjs-embed:latest | gzip > $BACKUP_DIR/image-$DATE.tar.gz

# 清理旧备份（保留最近7天）
echo "🧹 清理旧备份..."
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "✅ 备份完成: $BACKUP_DIR"
```

## 5. 监控和日志

### 5.1 日志配置

创建 `scripts/logs.sh`：

```bash
#!/bin/bash

# 日志查看脚本

case "$1" in
    "app")
        echo "📋 查看应用日志..."
        docker-compose -f docker-compose.prod.yml logs -f nextjs-embed
        ;;
    "nginx")
        echo "🌐 查看 Nginx 日志..."
        docker-compose -f docker-compose.prod.yml logs -f nginx
        ;;
    "all")
        echo "📋 查看所有日志..."
        docker-compose -f docker-compose.prod.yml logs -f
        ;;
    "tail")
        echo "📋 查看最新日志..."
        docker-compose -f docker-compose.prod.yml logs --tail=100 -f
        ;;
    *)
        echo "使用方法: $0 {app|nginx|all|tail}"
        exit 1
        ;;
esac
```

### 5.2 监控脚本

创建 `scripts/monitor.sh`：

```bash
#!/bin/bash

# 监控脚本

echo "📊 Next.js 嵌入系统监控报告"
echo "================================"

# 容器状态
echo "🐳 容器状态:"
docker-compose -f docker-compose.prod.yml ps

echo ""

# 资源使用情况
echo "💻 资源使用情况:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

echo ""

# 健康检查
echo "🏥 健康检查:"
if curl -s -f http://localhost:3000/api/embed/health > /dev/null; then
    echo "✅ 服务正常"
else
    echo "❌ 服务异常"
fi

echo ""

# 磁盘使用情况
echo "💾 磁盘使用情况:"
df -h | grep -E "(Filesystem|/dev/)"

echo ""

# 最近的错误日志
echo "🚨 最近的错误日志:"
docker-compose -f docker-compose.prod.yml logs --tail=10 | grep -i error || echo "无错误日志"
```

---

*本部署指南提供了完整的 Docker 部署方案，包括 Dockerfile、Docker Compose 配置、环境变量设置、部署脚本和监控工具。*
