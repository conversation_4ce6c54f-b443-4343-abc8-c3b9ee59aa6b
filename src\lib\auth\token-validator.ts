/**
 * Token验证服务
 * 在服务端验证JWT token并获取用户信息
 */

import type { AuthData } from '../middleware/types'
import type { ApiUser as User, UserPermission, UserRole, MenuConfig, FeatureConfig } from '@/stores/auth-store'
import { getApiBaseUrl, getEnvironmentConfig, isDebugEnabled } from '../config/environment'

/**
 * Token验证结果接口
 */
interface TokenValidationResult {
  success: boolean
  data?: AuthData | undefined
  error?: string | undefined
  statusCode?: number | undefined
}

/**
 * 在服务端验证token并获取用户数据
 * @param token JWT token
 * @returns 认证数据或null
 */
export async function validateTokenOnServer(token: string): Promise<AuthData | null> {
  const result = await validateTokenWithDetails(token)
  return result.success ? result.data || null : null
}

/**
 * 详细的token验证，返回完整的验证结果
 * @param token JWT token
 * @returns 详细的验证结果
 */
export async function validateTokenWithDetails(token: string): Promise<TokenValidationResult> {
  try {
    // 1. 预验证检查
    const preValidation = preValidateToken(token)
    if (!preValidation.isValid) {
      return {
        success: false,
        error: preValidation.error || 'Token pre-validation failed',
        statusCode: 400
      }
    }

    // 2. 获取环境配置
    const config = getEnvironmentConfig()
    const apiBaseUrl = getApiBaseUrl()

    if (isDebugEnabled()) {
      console.log(`[TokenValidator] Validating token in ${config.name} environment`)
      console.log(`[TokenValidator] API Base URL: ${apiBaseUrl}`)
    }

    // 3. 构建请求URL
    const apiUrl = `${apiBaseUrl}/api/user/get_current`

    // 4. 调用真实的后端API
    const response = await callAuthenticationAPI(apiUrl, token)

    if (!response.success) {
      return {
        success: false,
        error: response.error || 'API request failed',
        statusCode: response.statusCode || 500
      }
    }

    // 5. 验证响应数据
    const validationResult = validateAPIResponse(response.data)
    if (!validationResult.isValid) {
      return {
        success: false,
        error: validationResult.error || 'Response validation failed',
        statusCode: 422
      }
    }

    // 6. 转换为AuthData格式
    const authData = convertUserToAuthData(response.data.data)

    if (isDebugEnabled()) {
      console.log(`[TokenValidator] Token validation successful for user: ${authData.user.email}`)
    }

    return {
      success: true,
      data: authData
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error('[TokenValidator] Token validation failed:', errorMessage)

    return {
      success: false,
      error: `Token validation failed: ${errorMessage}`,
      statusCode: 500
    }
  }
}

/**
 * 预验证token格式和基本信息
 */
function preValidateToken(token: string): { isValid: boolean; error?: string | undefined } {
  // 检查token格式
  if (!isValidTokenFormat(token)) {
    return { isValid: false, error: 'Invalid token format' }
  }

  // 检查token是否过期
  if (isTokenExpired(token)) {
    return { isValid: false, error: 'Token has expired' }
  }

  return { isValid: true }
}

/**
 * 调用认证API
 */
async function callAuthenticationAPI(apiUrl: string, token: string): Promise<{
  success: boolean
  data?: any
  error?: string | undefined
  statusCode?: number | undefined
}> {
  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10秒超时

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'authorization': token,
        'language': 'chinese',
        'Content-Type': 'application/json',
        'User-Agent': 'NextJS-EmbedSystem/1.0',
        'Accept': 'application/json'
      },
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      await response.text().catch(() => 'Unknown error')
      return {
        success: false,
        error: `API request failed: ${response.status} ${response.statusText}`,
        statusCode: response.status
      }
    }

    const data = await response.json()
    return {
      success: true,
      data,
      statusCode: response.status
    }

  } catch (error) {
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return {
          success: false,
          error: 'Request timeout',
          statusCode: 408
        }
      }

      return {
        success: false,
        error: `Network error: ${error.message}`,
        statusCode: 0
      }
    }

    return {
      success: false,
      error: 'Unknown network error',
      statusCode: 0
    }
  }
}

/**
 * 验证API响应数据格式
 */
function validateAPIResponse(responseData: any): { isValid: boolean; error?: string | undefined } {
  if (!responseData) {
    return { isValid: false, error: 'Empty response data' }
  }

  if (!responseData.success) {
    return {
      isValid: false,
      error: responseData.message || 'API returned failure status'
    }
  }

  if (!responseData.data) {
    return { isValid: false, error: 'Missing user data in response' }
  }

  const userData = responseData.data

  // 验证必需的用户字段
  const requiredFields = ['id', 'email', 'user_type']
  for (const field of requiredFields) {
    if (!userData[field]) {
      return {
        isValid: false,
        error: `Missing required user field: ${field}`
      }
    }
  }

  // 验证公司数据
  if (!userData.company) {
    return { isValid: false, error: 'Missing company data' }
  }

  return { isValid: true }
}

/**
 * 检查token是否过期
 * @param token JWT token
 * @returns 是否过期
 */
export function isTokenExpired(token: string): boolean {
  try {
    const parts = token.split('.')
    if (parts.length !== 3) {
      return true
    }

    const payload = JSON.parse(atob(parts[1]))
    
    if (!payload.exp) {
      return true
    }

    return Date.now() >= payload.exp * 1000
  } catch {
    return true
  }
}

/**
 * 从token中提取用户ID（不验证签名）
 * @param token JWT token
 * @returns 用户ID或null
 */
export function extractUserIdFromToken(token: string): string | null {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    return payload.sub || payload.userId || payload.id || null
  } catch {
    return null
  }
}

/**
 * 转换用户数据为AuthData格式
 * @param user 用户数据
 * @returns AuthData格式的数据
 */
function convertUserToAuthData(user: User): AuthData {
  // 从用户数据中提取权限配置
  const menuConfig: MenuConfig[] = Object.entries(user.company.menu).map(([id, status]) => ({
    id,
    status: Boolean(status)
  }))

  const featureConfig: FeatureConfig[] = Object.entries(user.company.feature).map(([id, status]) => ({
    id,
    status: Boolean(status)
  }))

  // 根据用户类型设置角色
  const roles: UserRole[] = [
    {
      id: user.id,
      roleCode: user.user_type === 1 ? 'admin' : user.user_type === 2 ? 'premium' : 'user',
      roleName: user.user_type === 1 ? 'Administrator' : user.user_type === 2 ? 'Premium User' : 'User'
    }
  ]

  // 根据公司配置设置权限
  const permissions: UserPermission[] = []

  // PDF相关权限
  if (user.company.config.permissions.pdf_review) {
    permissions.push({ id: 'pdf_review', code: 'pdf_review', name: 'PDF Review', resource: 'pdf', action: 'review' })
  }
  if (user.company.config.permissions.pdf_download) {
    permissions.push({ id: 'pdf_download', code: 'pdf_download', name: 'PDF Download', resource: 'pdf', action: 'download' })
  }
  if (user.company.config.permissions.pdf_setting) {
    permissions.push({ id: 'pdf_setting', code: 'pdf_setting', name: 'PDF Setting', resource: 'pdf', action: 'setting' })
  }

  // 基础权限
  permissions.push(
    { id: 'read', code: 'read', name: 'Read', resource: 'general', action: 'read' },
    { id: 'write', code: 'write', name: 'Write', resource: 'general', action: 'write' }
  )

  return {
    user,
    permissions,
    roles,
    menuConfig,
    featureConfig
  }
}

/**
 * 验证token格式
 * @param token JWT token
 * @returns 是否为有效格式
 */
export function isValidTokenFormat(token: string): boolean {
  if (!token || typeof token !== 'string') {
    return false
  }

  const parts = token.split('.')
  if (parts.length !== 3) {
    return false
  }

  try {
    // 尝试解析header和payload
    JSON.parse(atob(parts[0]))
    JSON.parse(atob(parts[1]))
    return true
  } catch {
    return false
  }
}

/**
 * 刷新token（如果支持）
 * @param refreshToken 刷新token
 * @returns 新的认证数据或null
 */
export async function refreshTokenOnServer(refreshToken: string): Promise<AuthData | null> {
  try {
    const apiBaseUrl = process.env.API_BASE_URL || process.env.NEXT_PUBLIC_API_BASE_URL
    if (!apiBaseUrl) {
      return null
    }

    const response = await fetch(`${apiBaseUrl}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ refreshToken }),
      signal: AbortSignal.timeout(5000)
    })

    if (!response.ok) {
      return null
    }

    const data = await response.json()
    return data.success ? data.data : null
  } catch (error) {
    console.error('Token refresh failed:', error)
    return null
  }
}
