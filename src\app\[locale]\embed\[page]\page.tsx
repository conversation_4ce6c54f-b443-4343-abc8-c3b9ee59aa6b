/**
 * Embed Page - 国际化版本
 * 极简的嵌入页面路由 - 支持国际化，认证和通信由IframeContainer处理
 */

'use client'

import React from 'react'
import { useTranslations } from 'next-intl'
import EmbedPageWrapper from '@/components/embed/EmbedPageWrapper'
import DashboardPage from '@/components/embed/pages/DashboardPage'
import ReportPage from '@/components/embed/pages/ReportPage'
import TestPage from '@/components/embed/pages/TestPage'
import Error from '@/app/error'

interface EmbedPageProps {
  params: Promise<{ 
    locale: string
    page: string 
  }>
}

export default function EmbedPage({ params }: EmbedPageProps) {
  const [page, setPage] = React.useState<string>('')
  const [isLoading, setIsLoading] = React.useState(true)
  const t = useTranslations('embed')

  // 安全地解析异步params，避免use()导致的渲染问题
  React.useEffect(() => {
    params.then(resolvedParams => {
      setPage(resolvedParams.page)
      setIsLoading(false)
    }).catch(error => {
      console.error('Failed to resolve params:', error)
      setPage('dashboard') // 默认fallback
      setIsLoading(false)
    })
  }, [params])

  // 避免在参数解析期间渲染
  if (isLoading) {
    return (
      <div style={{ 
        padding: '20px', 
        textAlign: 'center',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div>
          <div style={{ fontSize: '18px', marginBottom: '10px' }}>
            🔧 {t('initializing', { defaultValue: '初始化中...' })}
          </div>
          <div style={{ color: '#6c757d' }}>
            {t('loading', { defaultValue: '正在加载页面' })}
          </div>
        </div>
      </div>
    )
  }

  // 支持dashboard、report和test页面
  const renderPageComponent = () => {
    if (page === 'dashboard') {
      return <DashboardPage />
    }
    if (page === 'report') {
      return <ReportPage />
    }
    if (page === 'test') {
      return <TestPage />
    }
    return <Error error={null} reset={() => {}} />
  }

  return (
    <div style={{ height: '100vh', overflow: 'hidden' }} className="embed-page-container">
      <EmbedPageWrapper>
        {renderPageComponent()}
      </EmbedPageWrapper>
    </div>
  )
}
