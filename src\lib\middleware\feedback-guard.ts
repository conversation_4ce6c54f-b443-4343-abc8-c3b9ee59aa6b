/**
 * 反馈守卫
 * 处理特定路径的重定向和最终的路由调整
 */

import { NextRequest, NextResponse } from 'next/server'
import { buildGuardContext } from './context-builder'
import type { MiddlewareFunction } from './types'

/**
 * 反馈守卫中间件
 * 处理简单的路径重定向和最终的路由调整
 */
export const feedbackGuard: MiddlewareFunction = async (
  request: NextRequest,
  next: () => Promise<NextResponse>
): Promise<NextResponse> => {
  const context = await buildGuardContext(request)
  const { pathname } = context

  // 处理特定路径重定向
  const redirectRules = getRedirectRules()
  
  for (const rule of redirectRules) {
    if (rule.match(pathname)) {
      const redirectPath = rule.getRedirectPath(pathname)
      console.info(`[FeedbackGuard] Redirect rule applied: ${pathname} -> ${redirectPath}`)
      
      const redirectUrl = new URL(redirectPath, request.url)
      
      // 保留查询参数
      if (rule.preserveQuery) {
        request.nextUrl.searchParams.forEach((value, key) => {
          redirectUrl.searchParams.set(key, value)
        })
      }
      
      return NextResponse.redirect(redirectUrl)
    }
  }

  return await next()
}

/**
 * 重定向规则接口
 */
interface RedirectRule {
  name: string
  match: (pathname: string) => boolean
  getRedirectPath: (pathname: string) => string
  preserveQuery?: boolean
  permanent?: boolean
}

/**
 * 获取重定向规则列表
 */
function getRedirectRules(): RedirectRule[] {
  return [
    // 🚫 仪表板重定向规则已删除（无workspace页面）
    // {
    //   name: 'dashboard-to-workspace',
    //   match: (pathname) => pathname === '/dashboard',
    //   getRedirectPath: () => '/workspace',
    //   preserveQuery: true,
    //   permanent: false
    // },

    // 旧的管理员路径重定向
    {
      name: 'old-admin-redirect',
      match: (pathname) => pathname.startsWith('/admin-panel'),
      getRedirectPath: (pathname) => pathname.replace('/admin-panel', '/admin'),
      preserveQuery: true,
      permanent: true
    },

    // 旧的用户路径重定向
    {
      name: 'old-user-redirect',
      match: (pathname) => pathname.startsWith('/user-management'),
      getRedirectPath: (pathname) => pathname.replace('/user-management', '/users'),
      preserveQuery: true,
      permanent: true
    },

    // 旧的设置路径重定向
    {
      name: 'old-settings-redirect',
      match: (pathname) => pathname.startsWith('/preferences'),
      getRedirectPath: (pathname) => pathname.replace('/preferences', '/settings'),
      preserveQuery: true,
      permanent: true
    },

    // 🚫 根路径重定向规则已删除（无workspace页面，根路径已为Chat页面）
    // {
    //   name: 'root-to-workspace',
    //   match: (pathname) => pathname === '/',
    //   getRedirectPath: () => '/workspace',
    //   preserveQuery: false,
    //   permanent: false
    // },

    // API文档重定向
    {
      name: 'api-docs-redirect',
      match: (pathname) => pathname === '/docs' || pathname === '/documentation',
      getRedirectPath: () => '/help/api',
      preserveQuery: false,
      permanent: false
    }
  ]
}

/**
 * 创建自定义反馈守卫
 * @param customRules 自定义重定向规则
 * @param options 配置选项
 * @returns 自定义反馈守卫
 */
export function createFeedbackGuard(
  customRules: RedirectRule[] = [],
  options: {
    enableLogging?: boolean
    skipDefaultRules?: boolean
  } = {}
): MiddlewareFunction {
  const { enableLogging = false, skipDefaultRules = false } = options

  return async (request: NextRequest, next: () => Promise<NextResponse>) => {
    const context = await buildGuardContext(request)
    const { pathname } = context

    // 合并默认规则和自定义规则
    const allRules = skipDefaultRules ? customRules : [...getRedirectRules(), ...customRules]

    for (const rule of allRules) {
      if (rule.match(pathname)) {
        const redirectPath = rule.getRedirectPath(pathname)
        
        if (enableLogging) {
          console.log(`[CustomFeedbackGuard] Rule '${rule.name}': ${pathname} -> ${redirectPath}`)
        }

        const redirectUrl = new URL(redirectPath, request.url)

        if (rule.preserveQuery) {
          request.nextUrl.searchParams.forEach((value, key) => {
            redirectUrl.searchParams.set(key, value)
          })
        }

        return NextResponse.redirect(redirectUrl, rule.permanent ? 301 : 302)
      }
    }

    return await next()
  }
}

/**
 * 条件重定向守卫
 * 基于用户状态或其他条件进行重定向
 */
export function createConditionalRedirectGuard(
  conditions: Array<{
    name: string
    condition: (context: any) => boolean
    getRedirectPath: (context: any) => string
    preserveQuery?: boolean
  }>
): MiddlewareFunction {
  return async (request: NextRequest, next: () => Promise<NextResponse>) => {
    const context = await buildGuardContext(request)

    for (const { name, condition, getRedirectPath, preserveQuery = false } of conditions) {
      if (condition(context)) {
        const redirectPath = getRedirectPath(context)
        console.info(`[ConditionalRedirect] Condition '${name}' met: ${context.pathname} -> ${redirectPath}`)

        const redirectUrl = new URL(redirectPath, request.url)

        if (preserveQuery) {
          request.nextUrl.searchParams.forEach((value, key) => {
            redirectUrl.searchParams.set(key, value)
          })
        }

        return NextResponse.redirect(redirectUrl)
      }
    }

    return await next()
  }
}

/**
 * 反馈守卫工具函数
 */
export const feedbackGuardUtils = {
  /**
   * 创建简单的路径重定向规则
   */
  createPathRedirect(
    fromPath: string, 
    toPath: string, 
    options: { preserveQuery?: boolean; permanent?: boolean } = {}
  ): RedirectRule {
    return {
      name: `redirect-${fromPath}-to-${toPath}`,
      match: (pathname) => pathname === fromPath,
      getRedirectPath: () => toPath,
      preserveQuery: options.preserveQuery || false,
      permanent: options.permanent || false
    }
  },

  /**
   * 创建前缀重定向规则
   */
  createPrefixRedirect(
    fromPrefix: string, 
    toPrefix: string, 
    options: { preserveQuery?: boolean; permanent?: boolean } = {}
  ): RedirectRule {
    return {
      name: `redirect-prefix-${fromPrefix}-to-${toPrefix}`,
      match: (pathname) => pathname.startsWith(fromPrefix),
      getRedirectPath: (pathname) => pathname.replace(fromPrefix, toPrefix),
      preserveQuery: options.preserveQuery || false,
      permanent: options.permanent || false
    }
  },

  /**
   * 创建正则表达式重定向规则
   */
  createRegexRedirect(
    pattern: RegExp, 
    replacement: string, 
    options: { preserveQuery?: boolean; permanent?: boolean } = {}
  ): RedirectRule {
    return {
      name: `regex-redirect-${pattern.source}`,
      match: (pathname) => pattern.test(pathname),
      getRedirectPath: (pathname) => pathname.replace(pattern, replacement),
      preserveQuery: options.preserveQuery || false,
      permanent: options.permanent || false
    }
  },

  /**
   * 验证重定向规则
   */
  validateRedirectRule(rule: RedirectRule): boolean {
    return (
      typeof rule.name === 'string' &&
      typeof rule.match === 'function' &&
      typeof rule.getRedirectPath === 'function'
    )
  },

  /**
   * 测试重定向规则
   */
  testRedirectRule(rule: RedirectRule, testPaths: string[]): Array<{
    path: string
    matches: boolean
    redirectPath?: string
  }> {
    return testPaths.map(path => {
      const matches = rule.match(path)
      const result: { path: string; matches: boolean; redirectPath?: string } = {
        path,
        matches
      }
      if (matches) {
        result.redirectPath = rule.getRedirectPath(path)
      }
      return result
    })
  },

  /**
   * 创建基于用户状态的条件重定向
   */
  createUserStatusRedirect(
    condition: (user: any) => boolean,
    getRedirectPath: (user: any, pathname: string) => string
  ) {
    return {
      name: 'user-status-redirect',
      condition: (context: any) => context.user && condition(context.user),
      getRedirectPath: (context: any) => getRedirectPath(context.user, context.pathname),
      preserveQuery: true
    }
  }
}
