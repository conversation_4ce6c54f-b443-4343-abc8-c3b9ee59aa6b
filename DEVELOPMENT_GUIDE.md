# Vibe Coding Refactory - 开发指南

> 🚀 企业级AI应用前端重构项目开发手册  
> 基于 Next.js 15 + React 18 + TypeScript 的iframe嵌入解决方案

## 📋 目录

- [项目概览](#项目概览)
- [快速开始](#快速开始)
- [核心架构](#核心架构)
- [新页面开发流程](#新页面开发流程)
- [认证与路由机制](#认证与路由机制)
- [关键配置文件](#关键配置文件)
- [开发最佳实践](#开发最佳实践)
- [常见问题解决](#常见问题解决)

---

## 📖 项目概览

### 技术栈
- **框架**: Next.js 15 (App Router)
- **UI库**: React 18 + TypeScript
- **状态管理**: Zustand + Immer
- **样式**: TailwindCSS 4 + Radix UI
- **认证**: JWT Token
- **部署**: Docker + Standalone

### 核心功能
- 🖼️ **iframe嵌入系统** - 独立认证和PostMessage通信
- 🎨 **UI组件展示** - 公开访问的组件库
- 🔐 **安全认证** - 多层域名和IP访问控制
- 📱 **响应式设计** - 移动端友好的现代UI

---

## 🚀 快速开始

### 环境要求
```bash
Node.js >= 18.0.0
pnpm >= 8.0.0
```

### 安装与启动
```bash
# 1. 安装依赖
pnpm install

# 2. 启动开发服务器
pnpm run dev

# 3. 访问应用
http://localhost:3000
```

### 环境变量配置
```bash
# .env.local
NEXT_PUBLIC_API_BASE_URL=https://dev.goglobalsp.com
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_EMBED_ENABLED=true
NEXT_PUBLIC_EXCLUDE_UI_GALLERY=false
```

---

## 🏗️ 核心架构

### 目录结构
```
src/
├── app/                    # App Router 页面
│   ├── embed/[page]/      # iframe嵌入页面 (独立认证)
│   ├── ui-gallery/        # UI组件展示页面 (公开访问)
│   ├── (auth)/login/      # 登录页面 (公开访问)
│   ├── api/               # API路由和代理
│   ├── error.tsx          # 全局错误页面
│   ├── loading.tsx        # 全局加载页面
│   ├── not-found.tsx      # 404页面
│   └── layout.tsx         # 根布局
├── components/            # 可复用组件
│   ├── ui/               # 基础UI组件
│   ├── providers/        # Context Provider
│   └── ...               # 业务组件
├── lib/                  # 工具库和配置
│   ├── config/          # 配置文件
│   ├── middleware/      # 中间件
│   ├── stores/         # Zustand状态管理
│   └── utils/          # 工具函数
└── styles/             # 样式文件
```

### 页面访问权限

| 路径 | 访问权限 | 认证方式 | 说明 |
|------|----------|----------|------|
| `/embed/[page]` | 独立认证 | Token验证 | iframe嵌入页面 |
| `/ui-gallery` | 公开访问 | 无需认证 | UI组件展示 |
| `/login` | 公开访问 | 重定向已登录用户 | 登录页面 |
| `/api/*` | API路由 | 根据端点而定 | 后端代理 |

---

## 🛠️ 新页面开发流程

### 🎯 开发前必读

**在开始开发新页面之前，请先了解：**

1. **认证机制** - 确定页面是否需要登录验证
2. **路由结构** - 选择合适的目录放置页面
3. **布局继承** - 了解现有的layout配置
4. **状态管理** - 确定是否需要全局状态

### 📝 Step 1: 确定页面类型

#### 🔓 公开页面 (无需登录)
```bash
# 适用场景: 营销页面、帮助文档、公共展示
src/app/your-page/page.tsx
```

#### 🔐 需要认证的页面
```bash
# 适用场景: 用户仪表板、个人设置
src/app/(dashboard)/your-page/page.tsx  # 需要先创建(dashboard)分组
```

#### 🖼️ iframe嵌入页面
```bash
# 适用场景: 第三方嵌入、独立应用
src/app/embed/your-page/page.tsx
```

### 📝 Step 2: 创建基础页面文件

#### 公开页面示例
```typescript
// src/app/your-page/page.tsx
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: '您的页面标题',
  description: '页面描述',
}

export default function YourPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">您的页面标题</h1>
      <p className="text-gray-600">页面内容...</p>
    </div>
  )
}
```

#### 需要认证的页面示例
```typescript
// src/app/(dashboard)/your-page/page.tsx
'use client'

import { useAuth } from '@/lib/stores/auth-store'
import { useEffect } from 'react'
import { redirect } from 'next/navigation'

export default function ProtectedPage() {
  const { user, isAuthenticated } = useAuth()

  useEffect(() => {
    if (!isAuthenticated) {
      redirect('/login')
    }
  }, [isAuthenticated])

  if (!isAuthenticated) {
    return <div>Loading...</div>
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">欢迎, {user?.name}</h1>
      {/* 页面内容 */}
    </div>
  )
}
```

#### iframe嵌入页面示例
```typescript
// src/app/embed/your-page/page.tsx
'use client'

import { IframeContainer } from '@/components/embed/iframe-container'
import { useSearchParams } from 'next/navigation'

export default function EmbedPage() {
  const searchParams = useSearchParams()
  const token = searchParams.get('token')

  return (
    <IframeContainer token={token}>
      <div className="p-4">
        <h1 className="text-2xl font-bold mb-4">嵌入页面内容</h1>
        {/* 您的嵌入内容 */}
      </div>
    </IframeContainer>
  )
}
```

### 📝 Step 3: 配置路由和布局

#### 自定义布局 (可选)
```typescript
// src/app/your-page/layout.tsx
export default function YourPageLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm">
        {/* 页面专用导航 */}
      </nav>
      <main className="container mx-auto">
        {children}
      </main>
    </div>
  )
}
```

#### 动态路由 (如果需要)
```typescript
// src/app/your-page/[id]/page.tsx
interface Props {
  params: Promise<{ id: string }>
}

export default async function DynamicPage({ params }: Props) {
  const { id } = await params
  
  return (
    <div>
      <h1>页面 ID: {id}</h1>
    </div>
  )
}
```

### 📝 Step 4: 添加样式和组件

#### 使用现有UI组件
```typescript
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'

export default function StyledPage() {
  return (
    <Card className="max-w-md mx-auto">
      <CardHeader>
        <CardTitle>示例表单</CardTitle>
      </CardHeader>
      <CardContent>
        <Input placeholder="输入内容..." className="mb-4" />
        <Button className="w-full">提交</Button>
      </CardContent>
    </Card>
  )
}
```

#### 创建页面专用组件
```typescript
// src/components/your-page/your-component.tsx
interface YourComponentProps {
  title: string
  content: string
}

export function YourComponent({ title, content }: YourComponentProps) {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-xl font-semibold mb-2">{title}</h2>
      <p className="text-gray-600">{content}</p>
    </div>
  )
}
```

### 📝 Step 5: 集成状态管理 (如果需要)

#### 使用现有的Auth Store
```typescript
'use client'

import { useAuth } from '@/lib/stores/auth-store'

export default function AuthAwarePage() {
  const { user, login, logout, isAuthenticated } = useAuth()

  return (
    <div>
      {isAuthenticated ? (
        <div>
          <p>欢迎, {user?.name}</p>
          <button onClick={logout}>登出</button>
        </div>
      ) : (
        <button onClick={() => login('token')}>登录</button>
      )}
    </div>
  )
}
```

#### 创建页面专用状态 (可选)
```typescript
// src/lib/stores/your-page-store.ts
import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'

interface YourPageState {
  data: any[]
  loading: boolean
  fetchData: () => Promise<void>
}

export const useYourPageStore = create<YourPageState>()(
  immer((set, get) => ({
    data: [],
    loading: false,
    fetchData: async () => {
      set((state) => {
        state.loading = true
      })
      
      try {
        // API调用
        const response = await fetch('/api/your-data')
        const data = await response.json()
        
        set((state) => {
          state.data = data
          state.loading = false
        })
      } catch (error) {
        set((state) => {
          state.loading = false
        })
      }
    },
  }))
)
```

---

## 🔐 认证与路由机制

### 认证流程

#### 1. AuthProvider 全局检查
```typescript
// src/components/providers/auth-provider.tsx
// 自动检查以下路径的认证状态:
// ✅ 跳过: /embed/*, /ui-gallery, /login
// 🔐 检查: 其他所有路径
```

#### 2. 中间件安全验证
```typescript
// src/lib/middleware/embed-security.ts
// 处理域名白名单、IP控制、token验证
```

#### 3. 页面级保护
```typescript
// 使用ProtectedRoute组件或自定义useEffect检查
```

### 路由配置

#### 添加新的公开路径
```typescript
// 在 AuthProvider 中添加跳过路径
if (pathname.startsWith('/embed/') || 
    pathname.startsWith('/ui-gallery') ||
    pathname.startsWith('/your-public-page')) {
  // 跳过认证检查
}
```

#### 添加新的受保护路径
```typescript
// 页面中添加认证检查
'use client'

import { useAuth } from '@/lib/stores/auth-store'
import { useEffect } from 'react'
import { redirect } from 'next/navigation'

export default function ProtectedPage() {
  const { isAuthenticated } = useAuth()

  useEffect(() => {
    if (!isAuthenticated) {
      redirect('/login')
    }
  }, [isAuthenticated])

  // 页面内容...
}
```

---

## ⚙️ 关键配置文件

### Next.js 配置
```typescript
// next.config.ts - 主要配置项:
- output: 'standalone'           // Docker部署
- assetPrefix                    // 静态资源前缀
- env                           // 环境变量
- headers()                     // CORS和安全头
- redirects()                   // 重定向规则
- webpack()                     // 构建优化
```

### 中间件配置
```typescript
// src/middleware.ts - 路由拦截:
- embed路径处理
- ui-gallery公开访问
- 安全验证和日志记录
```

### 环境配置
```bash
# 开发环境变量示例
NEXT_PUBLIC_API_BASE_URL=https://dev.goglobalsp.com
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_EMBED_ENABLED=true
NEXT_PUBLIC_EXCLUDE_UI_GALLERY=false
```

---

## ✨ 开发最佳实践

### 🎯 服务端渲染 vs 客户端渲染规范

在Next.js 15 App Router中，**默认所有组件都是服务端组件（Server Components）**。只有在特定情况下才需要添加 `'use client'` 指令来创建客户端组件。

#### 🖥️ 服务端渲染 (Server Components) - 默认选择

**何时使用** (不需要 `'use client'` 指令):
```typescript
// ✅ 默认服务端组件 - 推荐用于：
// 1. 静态内容展示
// 2. 数据获取和渲染
// 3. SEO重要的页面
// 4. 初始页面加载

// src/app/about/page.tsx
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: '关于我们',
  description: 'SEO友好的页面描述',
}

// 🚀 异步服务端组件 - 可以直接获取数据
export default async function AboutPage() {
  // ✅ 服务端直接获取数据
  const data = await fetch('https://api.example.com/data')
  const content = await data.json()

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">关于我们</h1>
      <p className="text-gray-600">{content.description}</p>
      {/* ✅ 静态内容，无需客户端交互 */}
    </div>
  )
}
```

**服务端组件优势**:
- 🚀 **更快的初始加载** - 服务端预渲染HTML
- 📱 **更好的SEO** - 搜索引擎可以直接索引内容  
- 🔋 **更少的客户端JS** - 减少浏览器负担
- 🔒 **更安全** - 敏感数据不会泄露到客户端

#### 🖱️ 客户端渲染 (Client Components) - 按需使用

**何时必须使用** `'use client'` 指令:
```typescript
// ❗ 以下情况必须添加 'use client'：

// 1. 使用React状态和生命周期
'use client'
import { useState, useEffect } from 'react'

export default function InteractivePage() {
  const [count, setCount] = useState(0)
  
  useEffect(() => {
    // 客户端生命周期逻辑
  }, [])

  return <button onClick={() => setCount(count + 1)}>点击: {count}</button>
}

// 2. 使用浏览器API
'use client'
import { useRouter } from 'next/navigation'

export default function NavigationPage() {
  const router = useRouter()
  
  const handleClick = () => {
    router.push('/other-page')
  }

  return <button onClick={handleClick}>导航</button>
}

// 3. 使用事件处理器
'use client'
export default function FormPage() {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // 表单处理逻辑
  }

  return (
    <form onSubmit={handleSubmit}>
      <button type="submit">提交</button>
    </form>
  )
}

// 4. 使用Context或自定义Hooks
'use client'
import { useAuth } from '@/lib/stores/auth-store'

export default function AuthPage() {
  const { user, login } = useAuth()
  
  return <div>用户: {user?.name}</div>
}
```

#### 🏗️ 混合架构 - 最佳实践

**推荐模式**: 页面保持服务端，交互组件使用客户端
```typescript
// ✅ 页面级别 - 服务端组件 (无 'use client')
// src/app/dashboard/page.tsx
import { UserProfile } from './user-profile'
import { InteractiveChart } from './interactive-chart'

export default async function DashboardPage() {
  // 🚀 服务端获取数据
  const userData = await getUserData()
  const chartData = await getChartData()

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">仪表板</h1>
      
      {/* ✅ 静态用户信息 - 服务端组件 */}
      <UserProfile user={userData} />
      
      {/* 🖱️ 交互式图表 - 客户端组件 */}
      <InteractiveChart data={chartData} />
    </div>
  )
}

// ✅ 静态展示组件 - 服务端组件 (无 'use client')
// src/app/dashboard/user-profile.tsx
interface UserProfileProps {
  user: User
}

export function UserProfile({ user }: UserProfileProps) {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-xl font-semibold">{user.name}</h2>
      <p className="text-gray-600">{user.email}</p>
    </div>
  )
}

// 🖱️ 交互式组件 - 客户端组件 (需要 'use client')
// src/app/dashboard/interactive-chart.tsx
'use client'
import { useState } from 'react'

interface InteractiveChartProps {
  data: ChartData[]
}

export function InteractiveChart({ data }: InteractiveChartProps) {
  const [selectedRange, setSelectedRange] = useState('week')

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      {/* 🖱️ 客户端交互 */}
      <select 
        value={selectedRange} 
        onChange={(e) => setSelectedRange(e.target.value)}
      >
        <option value="week">本周</option>
        <option value="month">本月</option>
      </select>
      {/* 图表渲染... */}
    </div>
  )
}
```

#### 📋 决策流程图

```
开始开发组件
      ↓
需要用户交互？ ─── 否 ──→ 使用服务端组件 (默认)
      ↓ 是
使用React状态？ ─── 否 ──→ 考虑是否可以用服务端
      ↓ 是              
使用浏览器API？ ─── 否 ──→ 考虑提升状态到父组件
      ↓ 是
需要事件处理？ ─── 否 ──→ 重新评估需求
      ↓ 是
     添加 'use client' 指令
```

#### ⚖️ 性能考量对比

| 特性 | 服务端组件 | 客户端组件 |
|------|-----------|-----------|
| **初始加载速度** | 🚀 更快 | ⚠️ 较慢 |
| **SEO优化** | ✅ 完美 | ❌ 需要额外配置 |
| **交互性** | ❌ 无 | ✅ 完整支持 |
| **JS包大小** | 🎯 更小 | 📦 更大 |
| **服务器负载** | ⚠️ 稍高 | ✅ 更低 |
| **缓存策略** | ✅ 更好 | ⚠️ 复杂 |

#### 🎯 项目中的具体应用场景

**🖥️ 服务端组件使用场景**:
```typescript
// 1. 静态页面
src/app/about/page.tsx              // 关于页面
src/app/privacy/page.tsx            // 隐私政策
src/app/ui-gallery/page.tsx         // UI组件展示

// 2. 数据展示页面
src/app/reports/page.tsx            // 报告页面
src/app/analytics/page.tsx          // 分析页面

// 3. 布局组件
src/app/layout.tsx                  // 根布局
src/app/dashboard/layout.tsx        // 仪表板布局
```

**🖱️ 客户端组件使用场景**:
```typescript
// 1. 认证相关
src/app/login/page.tsx              // 登录表单
src/components/providers/auth-provider.tsx

// 2. 表单和交互
src/components/forms/                // 所有表单组件
src/components/interactive/          // 交互式组件

// 3. iframe和特殊页面
src/app/embed/[page]/page.tsx       // iframe嵌入页面
```

#### 📝 代码审查检查清单

在代码审查时，检查以下几点：

**✅ 服务端组件检查清单**:
- [ ] 组件只做数据展示，无用户交互
- [ ] 无 `useState`、`useEffect` 等客户端Hook
- [ ] 无事件处理器 (`onClick`、`onChange` 等)
- [ ] 可以标记为 `async` 进行数据获取
- [ ] Meta标签配置正确 (SEO)

**✅ 客户端组件检查清单**:
- [ ] 组件确实需要客户端功能
- [ ] 正确添加 `'use client'` 指令
- [ ] 避免在客户端组件中直接获取大量数据
- [ ] 考虑是否可以拆分为更小的客户端组件
- [ ] 性能影响评估 (JS包大小)

### 🎨 代码规范

#### 1. 文件命名
```bash
# 页面文件
page.tsx, layout.tsx, loading.tsx, error.tsx

# 组件文件 (kebab-case)
user-profile.tsx, data-table.tsx

# 工具文件 (camelCase)
authUtils.ts, apiHelpers.ts
```

#### 2. 组件结构
```typescript
// 推荐的组件结构
import { type ComponentProps } from 'react'

interface YourComponentProps {
  title: string
  optional?: boolean
}

export function YourComponent({ title, optional = false }: YourComponentProps) {
  // 状态和逻辑
  
  return (
    <div className="component-styles">
      {/* JSX内容 */}
    </div>
  )
}
```

#### 3. 状态管理
```typescript
// 优先级: React State > Zustand > Context
// 1. 局部状态 - useState, useReducer
// 2. 全局状态 - Zustand stores
// 3. 主题/认证 - React Context
```

### 🔧 性能优化

#### 1. 组件懒加载
```typescript
import dynamic from 'next/dynamic'

const HeavyComponent = dynamic(() => import('./heavy-component'), {
  loading: () => <p>Loading...</p>,
  ssr: false
})
```

#### 2. 图片优化
```typescript
import Image from 'next/image'

<Image
  src="/your-image.jpg"
  alt="描述"
  width={500}
  height={300}
  priority // 首屏图片
/>
```

#### 3. API调用优化
```typescript
// 使用SWR或React Query进行数据获取
import useSWR from 'swr'

function DataComponent() {
  const { data, error, isLoading } = useSWR('/api/data', fetch)
  
  if (isLoading) return <div>Loading...</div>
  if (error) return <div>Error!</div>
  
  return <div>{JSON.stringify(data)}</div>
}
```

### 🧪 测试策略

#### 1. 组件测试
```typescript
// __tests__/your-component.test.tsx
import { render, screen } from '@testing-library/react'
import { YourComponent } from '../your-component'

test('renders component correctly', () => {
  render(<YourComponent title="Test" />)
  expect(screen.getByText('Test')).toBeInTheDocument()
})
```

#### 2. API测试
```typescript
// 使用Jest Mock进行API测试
jest.mock('next/navigation', () => ({
  useSearchParams: () => new URLSearchParams('token=test')
}))
```

### 🖼️ iframe嵌入开发规范

作为项目的核心功能，iframe嵌入需要特别注意安全性和兼容性。

#### 📋 iframe页面开发流程

**1. 创建嵌入页面**
```typescript
// src/app/embed/your-feature/page.tsx
'use client'

import { IframeContainer } from '@/components/embed/iframe-container'
import { useSearchParams } from 'next/navigation'
import { useEffect } from 'react'

export default function EmbedYourFeature() {
  const searchParams = useSearchParams()
  const token = searchParams.get('token')
  const theme = searchParams.get('theme') || 'light'
  const parentOrigin = searchParams.get('origin')

  useEffect(() => {
    // 🔒 向父窗口发送就绪信号
    if (parentOrigin) {
      window.parent.postMessage({
        type: 'EMBED_READY',
        payload: { page: 'your-feature' }
      }, parentOrigin)
    }
  }, [parentOrigin])

  return (
    <IframeContainer 
      token={token} 
      allowedOrigins={['*.goglobalsp.com', 'localhost:*']}
    >
      <div className={`iframe-content theme-${theme}`}>
        <h1>嵌入功能页面</h1>
        {/* 功能内容 */}
      </div>
    </IframeContainer>
  )
}
```

**2. PostMessage通信规范**
```typescript
// 📤 发送消息到父窗口
const sendToParent = (type: string, payload: any) => {
  const message = {
    type: `EMBED_${type}`,
    payload,
    timestamp: Date.now(),
    source: 'vibe-coding-refactory'
  }
  
  window.parent.postMessage(message, '*') // 生产环境要指定具体域名
}

// 📥 接收来自父窗口的消息
useEffect(() => {
  const handleMessage = (event: MessageEvent) => {
    // 🔒 验证消息来源
    if (!event.origin.includes('goglobalsp.com')) return
    
    const { type, payload } = event.data
    
    switch (type) {
      case 'PARENT_RESIZE':
        // 处理父窗口调整大小
        break
      case 'PARENT_THEME_CHANGE':
        // 处理主题变更
        setTheme(payload.theme)
        break
    }
  }

  window.addEventListener('message', handleMessage)
  return () => window.removeEventListener('message', handleMessage)
}, [])
```

**3. iframe样式和响应式设计**
```css
/* styles/iframe.css */
.iframe-content {
  /* 🎯 确保iframe内容自适应 */
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 16px;
  box-sizing: border-box;
  
  /* 🎨 主题适配 */
  &.theme-light {
    background: #ffffff;
    color: #1a1a1a;
  }
  
  &.theme-dark {
    background: #1a1a1a; 
    color: #ffffff;
  }
}

/* 📱 移动端适配 */
@media (max-width: 768px) {
  .iframe-content {
    padding: 12px;
    font-size: 14px;
  }
}
```

### 🗄️ 状态管理规范 (Zustand + Immer)

项目使用Zustand作为状态管理库，结合Immer实现不可变更新。

#### 📝 Store创建规范

```typescript
// src/lib/stores/feature-store.ts
import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { persist } from 'zustand/middleware'

interface FeatureState {
  // 📊 状态定义
  data: FeatureData[]
  loading: boolean
  error: string | null
  filters: FilterOptions
  
  // 🔄 动作定义
  fetchData: () => Promise<void>
  updateFilters: (filters: Partial<FilterOptions>) => void
  clearError: () => void
  reset: () => void
}

export const useFeatureStore = create<FeatureState>()(
  persist(
    immer((set, get) => ({
      // 🏁 初始状态
      data: [],
      loading: false,
      error: null,
      filters: {
        category: 'all',
        dateRange: 'week'
      },

      // 📥 异步数据获取
      fetchData: async () => {
        set((state) => {
          state.loading = true
          state.error = null
        })

        try {
          const response = await fetch('/api/feature-data')
          
          if (!response.ok) {
            throw new Error(`API Error: ${response.status}`)
          }
          
          const data = await response.json()
          
          set((state) => {
            state.data = data
            state.loading = false
          })
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Unknown error'
            state.loading = false
          })
        }
      },

      // 🔧 状态更新
      updateFilters: (newFilters) => {
        set((state) => {
          // ✅ Immer让这种写法是安全的
          Object.assign(state.filters, newFilters)
        })
        
        // 🔄 过滤器更新后重新获取数据
        get().fetchData()
      },

      // 🧹 清理操作
      clearError: () => {
        set((state) => {
          state.error = null
        })
      },

      reset: () => {
        set((state) => {
          state.data = []
          state.loading = false
          state.error = null
          state.filters = {
            category: 'all',
            dateRange: 'week'
          }
        })
      }
    })),
    {
      name: 'feature-store', // 🔑 持久化键名
      partialize: (state) => ({ 
        filters: state.filters // 🎯 只持久化特定状态
      })
    }
  )
)
```

#### 🎯 Store使用最佳实践

```typescript
// ✅ 正确的使用方式
function FeatureComponent() {
  // 🎯 只订阅需要的状态片段
  const { data, loading, error } = useFeatureStore(
    (state) => ({
      data: state.data,
      loading: state.loading,
      error: state.error
    })
  )
  
  // 🔄 获取动作函数
  const { fetchData, updateFilters } = useFeatureStore()

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return (
    <div>
      {loading && <LoadingSpinner />}
      {error && <ErrorMessage message={error} />}
      {data.map(item => <ItemCard key={item.id} item={item} />)}
    </div>
  )
}

// ❌ 避免的反模式
function BadComponent() {
  // ❌ 订阅整个store会导致不必要的重渲染
  const store = useFeatureStore()
  
  // ❌ 在渲染期间执行副作用
  store.fetchData()
  
  return <div>...</div>
}
```

### 🌐 API代理和数据获取规范

项目使用Next.js API路由作为后端代理，统一处理API调用。

#### 📡 API代理开发规范

```typescript
// src/app/api/your-endpoint/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'

// 🔒 API基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://dev.goglobalsp.com'
const API_TIMEOUT = 30000 // 30秒超时

export async function GET(request: NextRequest) {
  try {
    // 🔑 获取认证token
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization required' },
        { status: 401 }
      )
    }

    // 🔄 构建后端请求
    const backendUrl = new URL('/api/your-endpoint', API_BASE_URL)
    
    // 📋 传递查询参数
    const searchParams = request.nextUrl.searchParams
    searchParams.forEach((value, key) => {
      backendUrl.searchParams.set(key, value)
    })

    const response = await fetch(backendUrl.toString(), {
      method: 'GET',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
        'User-Agent': 'vibe-coding-refactory/1.0',
      },
      signal: AbortSignal.timeout(API_TIMEOUT)
    })

    // 🔍 错误处理
    if (!response.ok) {
      console.error(`Backend API error: ${response.status} ${response.statusText}`)
      
      return NextResponse.json(
        { 
          error: 'Backend service unavailable',
          code: response.status
        },
        { status: response.status }
      )
    }

    const data = await response.json()
    
    // 📊 成功响应
    return NextResponse.json(data, {
      headers: {
        'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=300',
      }
    })
    
  } catch (error) {
    console.error('API Proxy Error:', error)
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // 📥 获取请求体
    const body = await request.json()
    
    // 🔍 基础验证
    if (!body || typeof body !== 'object') {
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      )
    }

    // 🔄 转发到后端
    const response = await fetch(`${API_BASE_URL}/api/your-endpoint`, {
      method: 'POST',
      headers: {
        'Authorization': request.headers.get('authorization') || '',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
      signal: AbortSignal.timeout(API_TIMEOUT)
    })

    const data = await response.json()
    
    return NextResponse.json(data, { 
      status: response.status,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      }
    })
    
  } catch (error) {
    console.error('API POST Error:', error)
    
    return NextResponse.json(
      { error: 'Request failed' },
      { status: 500 }
    )
  }
}
```

#### 🎯 客户端数据获取规范

```typescript
// src/lib/api/api-client.ts
class ApiClient {
  private baseUrl = '/api'
  private defaultHeaders = {
    'Content-Type': 'application/json',
  }

  // 🔑 设置认证token
  setAuthToken(token: string) {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`
  }

  // 📥 GET请求
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = new URL(`${this.baseUrl}${endpoint}`, window.location.origin)
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.set(key, String(value))
        }
      })
    }

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: this.defaultHeaders,
    })

    if (!response.ok) {
      throw new ApiError(`GET ${endpoint} failed`, response.status)
    }

    return response.json()
  }

  // 📤 POST请求
  async post<T>(endpoint: string, data?: any): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: this.defaultHeaders,
      body: data ? JSON.stringify(data) : undefined,
    })

    if (!response.ok) {
      throw new ApiError(`POST ${endpoint} failed`, response.status)
    }

    return response.json()
  }
}

// 🔧 自定义错误类
class ApiError extends Error {
  constructor(message: string, public status: number) {
    super(message)
    this.name = 'ApiError'
  }
}

// 🌐 导出单例实例
export const apiClient = new ApiClient()
```

### 🔒 安全开发规范

基于项目的安全架构，制定开发中的安全规范。

#### 🛡️ 域名白名单管理

```typescript
// src/lib/security/domain-validator.ts
const ALLOWED_DOMAINS = {
  production: [
    'goglobalsp.com',
    '*.goglobalsp.com',
    'specific-ai.com',
    '*.specific-ai.com'
  ],
  development: [
    'localhost',
    '127.0.0.1',
    '*.localhost',
    'localhost:*'
  ]
}

export function validateOrigin(origin: string): boolean {
  const env = process.env.NODE_ENV
  const allowedDomains = env === 'production' 
    ? ALLOWED_DOMAINS.production 
    : [...ALLOWED_DOMAINS.production, ...ALLOWED_DOMAINS.development]

  return allowedDomains.some(domain => {
    // 🔍 支持通配符匹配
    if (domain.includes('*')) {
      const pattern = domain.replace(/\*/g, '.*')
      return new RegExp(`^${pattern}$`).test(origin)
    }
    
    return origin === domain || origin.endsWith(`.${domain}`)
  })
}

// 🚫 IP访问控制
export function validateIPAccess(ip: string): boolean {
  const allowedIPs = process.env.ALLOWED_IPS?.split(',') || []
  const blockedIPs = process.env.BLOCKED_IPS?.split(',') || []

  // 🔒 检查黑名单
  if (blockedIPs.includes(ip)) {
    return false
  }

  // ✅ 检查白名单（如果配置了）
  if (allowedIPs.length > 0) {
    return allowedIPs.includes(ip)
  }

  return true // 默认允许
}
```

#### 🔐 Token验证规范

```typescript
// src/lib/auth/token-validator.ts
import jwt from 'jsonwebtoken'

interface TokenPayload {
  userId: string
  role: string
  permissions: string[]
  exp: number
}

export async function validateToken(token: string): Promise<TokenPayload | null> {
  try {
    // 🔍 基础格式检查
    if (!token || !token.startsWith('Bearer ')) {
      return null
    }

    const actualToken = token.replace('Bearer ', '')
    
    // 🔑 JWT验证
    const secret = process.env.JWT_SECRET
    if (!secret) {
      throw new Error('JWT_SECRET not configured')
    }

    const decoded = jwt.verify(actualToken, secret) as TokenPayload
    
    // ⏰ 检查过期时间
    if (decoded.exp * 1000 < Date.now()) {
      return null
    }

    return decoded
    
  } catch (error) {
    console.error('Token validation error:', error)
    return null
  }
}

// 🔒 权限检查
export function hasPermission(
  userPermissions: string[], 
  requiredPermission: string
): boolean {
  return userPermissions.includes(requiredPermission) || 
         userPermissions.includes('admin')
}
```

### 🎨 UI组件开发规范

基于Radix UI + TailwindCSS的组件设计系统。

#### 📦 组件开发结构

```typescript
// src/components/ui/custom-button.tsx
import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'

// 🎯 按钮变体定义
const buttonVariants = cva(
  // 基础样式
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground shadow hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        outline: "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline"
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9"
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default"
    }
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
```

#### 🧪 组件测试规范

```typescript
// __tests__/components/ui/button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from '@/components/ui/button'

describe('Button Component', () => {
  it('renders correctly with default props', () => {
    render(<Button>Click me</Button>)
    
    const button = screen.getByRole('button', { name: /click me/i })
    expect(button).toBeInTheDocument()
    expect(button).toHaveClass('bg-primary')
  })

  it('applies variant styles correctly', () => {
    render(<Button variant="destructive">Delete</Button>)
    
    const button = screen.getByRole('button', { name: /delete/i })
    expect(button).toHaveClass('bg-destructive')
  })

  it('handles click events', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('is disabled when disabled prop is true', () => {
    render(<Button disabled>Disabled</Button>)
    
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    expect(button).toHaveClass('disabled:opacity-50')
  })
})
```

### 🐳 Docker部署规范

项目使用Docker进行容器化部署，支持standalone模式。

#### 🏗️ Dockerfile最佳实践

```dockerfile
# Dockerfile.production
# 🏗️ 多阶段构建优化镜像大小
FROM node:18-alpine AS base
WORKDIR /app

# 📦 安装依赖阶段
FROM base AS deps
RUN apk add --no-cache libc6-compat
COPY package.json pnpm-lock.yaml ./
RUN corepack enable pnpm && pnpm install --frozen-lockfile --production=false

# 🔨 构建阶段
FROM base AS builder
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# 🎯 设置构建时环境变量
ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_ENV production

# 📊 执行构建
RUN corepack enable pnpm && pnpm run build

# 🚀 运行时阶段
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# 👤 创建非root用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 📁 复制构建产物和必要文件
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# 🎯 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

CMD ["node", "server.js"]
```

#### 📋 Docker Compose配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.production
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=https://dev.goglobalsp.com
      - NEXT_PUBLIC_APP_ENV=production
    env_file:
      - .env.production
    restart: unless-stopped
    networks:
      - app-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.vibe-app.rule=Host(`your-domain.com`)"
      - "traefik.http.services.vibe-app.loadbalancer.server.port=3000"

  # 🔄 Redis缓存 (可选)
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    volumes:
      - redis_data:/data
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  redis_data:
```

#### 🔧 部署脚本

```bash
#!/bin/bash
# scripts/deploy.sh

set -e

echo "🚀 开始部署 vibe-coding-refactory..."

# 📊 检查环境变量
if [ -z "$ENVIRONMENT" ]; then
  echo "❌ 请设置 ENVIRONMENT 环境变量 (development|staging|production)"
  exit 1
fi

# 🏗️ 构建镜像
echo "📦 构建Docker镜像..."
docker build -t vibe-coding-refactory:$ENVIRONMENT .

# 🔍 运行测试
echo "🧪 运行测试..."
docker run --rm vibe-coding-refactory:$ENVIRONMENT npm test

# 🚀 部署到环境
echo "🚀 部署到 $ENVIRONMENT 环境..."
case $ENVIRONMENT in
  "production")
    docker-compose -f docker-compose.prod.yml up -d
    ;;
  "staging")
    docker-compose -f docker-compose.staging.yml up -d
    ;;
  *)
    docker-compose up -d
    ;;
esac

echo "✅ 部署完成！"
```

### 🌍 环境配置管理规范

支持多环境配置和动态环境变量管理。

#### 📝 环境配置文件结构

```bash
# 环境配置文件结构
.env.local          # 本地开发环境（Git忽略）
.env.development    # 开发环境
.env.staging        # 测试环境
.env.production     # 生产环境
.env.example        # 环境变量模板
```

```bash
# .env.example - 环境变量模板
# 🔑 认证配置
JWT_SECRET=your-super-secret-jwt-key
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000

# 🌐 API配置
NEXT_PUBLIC_API_BASE_URL=https://dev.goglobalsp.com
NEXT_PUBLIC_APP_ENV=development
API_TIMEOUT=30000

# 🖼️ iframe嵌入配置
NEXT_PUBLIC_EMBED_ENABLED=true
ALLOWED_DOMAINS=goglobalsp.com,specific-ai.com
EMBED_TOKEN_SECRET=your-embed-token-secret

# 🔒 安全配置
ALLOWED_IPS=
BLOCKED_IPS=
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=60000

# 📊 监控和日志
SENTRY_DSN=
LOG_LEVEL=info
ANALYTICS_ID=

# 🗄️ 数据库配置（如果需要）
DATABASE_URL=
REDIS_URL=

# 🚀 部署配置
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NEXT_PUBLIC_EXCLUDE_UI_GALLERY=false
```

#### 🔧 环境配置验证

```typescript
// src/lib/config/env-validator.ts
import { z } from 'zod'

const envSchema = z.object({
  // 🔑 必需的环境变量
  JWT_SECRET: z.string().min(32, 'JWT_SECRET must be at least 32 characters'),
  NEXT_PUBLIC_API_BASE_URL: z.string().url('Invalid API base URL'),
  
  // 🎯 可选的环境变量
  NEXT_PUBLIC_APP_ENV: z.enum(['development', 'staging', 'production']).default('development'),
  API_TIMEOUT: z.string().transform(Number).default('30000'),
  LOG_LEVEL: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
  
  // 🔒 安全配置
  ALLOWED_DOMAINS: z.string().optional(),
  RATE_LIMIT_MAX: z.string().transform(Number).default('100'),
})

export type Environment = z.infer<typeof envSchema>

// 🔍 验证环境变量
export function validateEnvironment(): Environment {
  try {
    return envSchema.parse(process.env)
  } catch (error) {
    console.error('❌ 环境变量验证失败:')
    if (error instanceof z.ZodError) {
      error.errors.forEach(err => {
        console.error(`  - ${err.path.join('.')}: ${err.message}`)
      })
    }
    process.exit(1)
  }
}

// 🌍 导出验证后的环境配置
export const env = validateEnvironment()
```

### 📊 错误处理和日志规范

统一的错误处理和结构化日志记录。

#### 🚨 全局错误处理

```typescript
// src/lib/error/error-handler.ts
export class AppError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code: string = 'INTERNAL_ERROR',
    public isOperational: boolean = true
  ) {
    super(message)
    this.name = 'AppError'
    Error.captureStackTrace(this, this.constructor)
  }
}

// 🎯 常见错误类型
export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 400, 'VALIDATION_ERROR')
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR')
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR')
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 404, 'NOT_FOUND_ERROR')
  }
}

// 🔄 错误处理中间件
export function handleApiError(error: unknown): NextResponse {
  // 📊 记录错误
  console.error('API Error:', error)

  if (error instanceof AppError) {
    return NextResponse.json(
      {
        error: error.message,
        code: error.code,
        timestamp: new Date().toISOString(),
      },
      { status: error.statusCode }
    )
  }

  // 🔍 未知错误
  return NextResponse.json(
    {
      error: 'Internal server error',
      code: 'INTERNAL_ERROR',
      timestamp: new Date().toISOString(),
    },
    { status: 500 }
  )
}
```

#### 📝 结构化日志记录

```typescript
// src/lib/logger/logger.ts
import winston from 'winston'

// 🎯 日志级别定义
const LOG_LEVELS = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3,
}

// 🎨 日志格式化
const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    return JSON.stringify({
      timestamp,
      level,
      message,
      service: 'vibe-coding-refactory',
      environment: process.env.NODE_ENV,
      ...meta,
    })
  })
)

// 🏗️ 创建logger实例
export const logger = winston.createLogger({
  levels: LOG_LEVELS,
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  transports: [
    // 📺 控制台输出
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    }),
    
    // 📁 文件输出（生产环境）
    ...(process.env.NODE_ENV === 'production' ? [
      new winston.transports.File({
        filename: 'logs/error.log',
        level: 'error',
      }),
      new winston.transports.File({
        filename: 'logs/combined.log',
      }),
    ] : []),
  ],
})

// 🎯 专用日志函数
export const apiLogger = {
  request: (req: NextRequest, duration?: number) => {
    logger.info('API Request', {
      method: req.method,
      url: req.url,
      userAgent: req.headers.get('user-agent'),
      ip: req.headers.get('x-forwarded-for') || 'unknown',
      duration,
    })
  },
  
  error: (error: Error, context?: Record<string, any>) => {
    logger.error('API Error', {
      message: error.message,
      stack: error.stack,
      ...context,
    })
  },
  
  auth: (event: string, userId?: string, details?: Record<string, any>) => {
    logger.info('Auth Event', {
      event,
      userId,
      ...details,
    })
  },
}
```

### 📈 性能监控和优化规范

生产环境的性能监控和优化策略。

#### 🔍 性能监控设置

```typescript
// src/lib/monitoring/performance.ts
import { NextRequest } from 'next/server'

// 📊 性能指标收集
export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: Map<string, number[]> = new Map()

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  // ⏱️ 记录API响应时间
  recordApiDuration(endpoint: string, duration: number) {
    const existing = this.metrics.get(endpoint) || []
    existing.push(duration)
    
    // 🧹 保持最近100个记录
    if (existing.length > 100) {
      existing.shift()
    }
    
    this.metrics.set(endpoint, existing)
  }

  // 📈 获取性能统计
  getStats(endpoint: string) {
    const durations = this.metrics.get(endpoint) || []
    if (durations.length === 0) return null

    const sorted = [...durations].sort((a, b) => a - b)
    return {
      count: durations.length,
      avg: durations.reduce((a, b) => a + b, 0) / durations.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      p50: sorted[Math.floor(sorted.length * 0.5)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)],
    }
  }

  // 📊 获取所有统计信息
  getAllStats() {
    const stats: Record<string, any> = {}
    for (const [endpoint, _] of this.metrics) {
      stats[endpoint] = this.getStats(endpoint)
    }
    return stats
  }
}

// 🎯 性能中间件
export function performanceMiddleware(
  handler: (req: NextRequest) => Promise<Response>
) {
  return async (req: NextRequest): Promise<Response> => {
    const start = Date.now()
    const monitor = PerformanceMonitor.getInstance()
    
    try {
      const response = await handler(req)
      const duration = Date.now() - start
      
      // 📊 记录性能指标
      monitor.recordApiDuration(req.nextUrl.pathname, duration)
      
      // 📈 添加性能头部
      response.headers.set('X-Response-Time', `${duration}ms`)
      
      return response
    } catch (error) {
      const duration = Date.now() - start
      monitor.recordApiDuration(`${req.nextUrl.pathname}_error`, duration)
      throw error
    }
  }
}
```

#### 🏆 性能优化检查清单

```typescript
// src/lib/monitoring/performance-checks.ts

export const performanceChecks = {
  // 🖼️ 图片优化检查
  imageOptimization: {
    // ✅ 使用Next.js Image组件
    // ✅ 设置适当的width和height
    // ✅ 使用priority属性标记首屏图片
    // ✅ 选择合适的图片格式 (WebP, AVIF)
    // ✅ 使用placeholder属性改善用户体验
  },

  // 📦 Bundle分析
  bundleOptimization: {
    // ✅ 使用@next/bundle-analyzer分析包大小
    // ✅ 实现代码分割和懒加载
    // ✅ 移除未使用的依赖
    // ✅ 使用tree-shaking优化
    // ✅ 压缩和最小化资源
  },

  // 🔄 缓存策略
  cachingStrategy: {
    // ✅ 设置适当的Cache-Control头部
    // ✅ 使用SWR或React Query进行数据缓存
    // ✅ 实现静态资源长期缓存
    // ✅ 使用CDN加速静态资源
  },

  // 🚀 运行时性能
  runtimePerformance: {
    // ✅ 避免不必要的重渲染
    // ✅ 使用React.memo和useMemo
    // ✅ 优化状态管理器的订阅
    // ✅ 实现虚拟滚动（大列表）
    // ✅ 使用Web Workers处理重计算
  },
}

// 📊 性能预算配置
export const performanceBudget = {
  // 📏 页面大小限制
  maxPageSize: '500KB',
  maxJSBundle: '200KB',
  maxCSSBundle: '50KB',
  maxImageSize: '100KB',
  
  // ⏱️ 时间预算
  maxFirstContentfulPaint: '1.5s',
  maxLargestContentfulPaint: '2.5s',
  maxFirstInputDelay: '100ms',
  maxCumulativeLayoutShift: '0.1',
  
  // 🔄 API响应时间
  maxApiResponseTime: '500ms',
  maxDatabaseQueryTime: '100ms',
}
```

### 🛡️ 中间件系统详解

项目采用多层中间件架构，确保安全性、性能和功能的统一管理。

#### 📋 中间件架构概览

```
请求流程:
浏览器请求 → Next.js Middleware → Embed Security → AuthProvider → 页面组件

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   src/         │    │  src/lib/       │    │ src/components/ │    │   页面组件      │
│   middleware.ts │ ─► │  middleware/    │ ─► │ providers/      │ ─► │   React页面     │
│   (路由拦截)    │    │  embed-security │    │ auth-provider   │    │   (业务逻辑)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

#### 🚦 1. 主中间件 (src/middleware.ts)

**作用**: 作为整个应用的入口控制器，负责路由拦截和初始安全检查。

```typescript
// src/middleware.ts
import { NextRequest, NextResponse } from 'next/server'
import { embedSecurityMiddleware } from '@/lib/middleware/embed-security'

export function middleware(request: NextRequest): NextResponse {
  const { pathname } = request.nextUrl
  
  console.log(`[Middleware] 处理请求: ${pathname}`)
  
  // 🎯 所有请求都经过安全中间件处理
  return embedSecurityMiddleware(request)
}

// 📋 配置匹配规则
export const config = {
  // ✅ 匹配所有路径，除了静态资源
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)',
  ],
}
```

**工作原理**:
1. **路径匹配**: 使用matcher配置拦截除静态资源外的所有请求
2. **日志记录**: 记录每个请求的路径信息用于调试
3. **委托处理**: 将所有请求委托给embed-security中间件处理
4. **性能优化**: 排除静态资源避免不必要的处理开销

**配置说明**:
- `matcher` 数组定义了需要中间件处理的路径模式
- 排除 `_next/static`、`_next/image` 等Next.js内部路径
- 排除 `favicon.ico`、`robots.txt` 等静态文件

#### 🔒 2. 嵌入安全中间件 (src/lib/middleware/embed-security.ts)

**作用**: 核心安全控制层，处理域名白名单、IP访问控制、iframe嵌入验证等安全策略。

```typescript
// src/lib/middleware/embed-security.ts 关键功能解析

/**
 * 🎯 主要功能:
 * 1. 域名白名单验证
 * 2. IP访问控制
 * 3. iframe嵌入安全检查
 * 4. API健康检查处理
 * 5. 访问日志记录
 */

export function embedSecurityMiddleware(request: NextRequest): NextResponse {
  const { pathname } = request.nextUrl
  const origin = request.headers.get('origin')
  const referer = request.headers.get('referer')
  const clientIP = EmbedAccessLogger.getClientIP(request)

  console.log(`[EmbedSecurity] ${pathname} - IP: ${clientIP}, Origin: ${origin}`)

  // 🎯 路径分发处理
  if (pathname.startsWith('/embed/')) {
    return handleEmbedPath(request, origin, referer)  // iframe嵌入处理
  }

  if (pathname.startsWith('/ui-gallery')) {
    return NextResponse.next()  // UI展示页面公开访问
  }

  if (pathname === '/api/health') {
    return handleHealthCheck(request)  // 健康检查端点
  }

  return handleOtherPaths(request)  // 其他路径处理
}
```

**核心处理函数详解**:

##### 📍 handleEmbedPath - iframe嵌入路径处理

```typescript
function handleEmbedPath(
  request: NextRequest, 
  origin: string | null, 
  referer: string | null
): NextResponse {
  const clientIP = EmbedAccessLogger.getClientIP(request)
  const isInternalAccess = isInternalIP(clientIP)
  
  // 🔍 开发环境：允许本地访问
  if (process.env.NODE_ENV === 'development') {
    if (isLocalhost(origin) || isLocalhost(referer) || isInternalAccess) {
      console.log(`[EmbedSecurity] 开发环境允许访问: ${origin || referer}`)
      return NextResponse.next()
    }
  }

  // 🔒 生产环境：严格域名验证
  if (process.env.NODE_ENV === 'production') {
    if (!isInternalAccess && !validateDomain(origin || referer || '')) {
      console.log(`[EmbedSecurity] 🚫 拒绝未授权域名: ${origin || referer}`)
      
      return new NextResponse('Access Denied', { 
        status: 403,
        headers: {
          'X-Denied-Reason': 'Invalid domain',
          'X-Timestamp': new Date().toISOString(),
        }
      })
    }
  }

  // ✅ 验证通过，记录访问日志
  EmbedAccessLogger.logAccess({
    ip: clientIP,
    origin,
    referer,
    path: request.nextUrl.pathname,
    userAgent: request.headers.get('user-agent'),
    timestamp: new Date(),
    allowed: true
  })

  return NextResponse.next()
}
```

**功能说明**:
- **环境区分**: 开发环境宽松，生产环境严格
- **IP白名单**: 内网IP始终允许访问
- **域名验证**: 生产环境必须通过域名白名单验证
- **访问日志**: 记录所有访问尝试用于安全审计

##### 🏥 handleHealthCheck - 健康检查处理

```typescript
function handleHealthCheck(request: NextRequest): NextResponse {
  const clientIP = EmbedAccessLogger.getClientIP(request)
  
  // 🔍 简单的健康状态检查
  const healthStatus = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV,
    uptime: process.uptime(),
  }

  // 📊 内网访问返回详细信息
  if (isInternalIP(clientIP)) {
    return NextResponse.json({
      ...healthStatus,
      detailed: {
        memory: process.memoryUsage(),
        pid: process.pid,
        nodeVersion: process.version,
      }
    })
  }

  // 🔒 外网访问返回基本信息
  return NextResponse.json({
    status: healthStatus.status,
    timestamp: healthStatus.timestamp,
  })
}
```

**功能说明**:
- **基础健康检查**: 返回服务状态和时间戳
- **内网详细信息**: 内网IP可获取内存、进程等详细信息
- **外网基础信息**: 外网只能获取基本的健康状态

##### 🛡️ handleOtherPaths - 其他路径处理

```typescript
function handleOtherPaths(request: NextRequest): NextResponse {
  const clientIP = EmbedAccessLogger.getClientIP(request)
  const isInternalAccess = isInternalIP(clientIP)

  // 🏠 内网访问：始终允许
  if (isInternalAccess) {
    console.log(`[EmbedSecurity] 内网访问允许: ${clientIP}`)
    return NextResponse.next()
  }

  // 🌍 外网访问：根据环境策略
  if (process.env.NODE_ENV === 'production') {
    // 🔒 生产环境：拒绝外网直接访问
    console.log(`[EmbedSecurity] 🚫 生产环境拒绝外网访问: ${clientIP}`)
    
    return new NextResponse('Access Denied - Use Embed', { 
      status: 403,
      headers: {
        'X-Access-Policy': 'embed-only',
        'X-Suggested-Path': '/embed/dashboard',
      }
    })
  }

  // 🔧 开发环境：允许外网访问
  console.log(`[EmbedSecurity] 开发环境允许外网访问: ${clientIP}`)
  return NextResponse.next()
}
```

**功能说明**:
- **内网优先**: 内网IP始终允许访问所有路径
- **生产限制**: 生产环境外网用户只能通过iframe嵌入访问
- **开发友好**: 开发环境允许直接访问便于调试

#### 👤 3. 认证提供者中间件 (src/components/providers/auth-provider.tsx)

**作用**: 应用层认证控制，管理用户登录状态、token验证和路由保护。

```typescript
// src/components/providers/auth-provider.tsx 核心逻辑

/**
 * 🎯 主要功能:
 * 1. 全局认证状态管理
 * 2. 路径级别的认证控制
 * 3. Token自动验证和刷新
 * 4. 登录状态持久化
 */

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const [isInitialized, setIsInitialized] = useState(false)
  const { validateToken, clearAuth } = useAuth()

  useEffect(() => {
    const initAuth = async () => {
      // 🔓 跳过不需要认证的路径
      if (pathname.startsWith('/embed/') || 
          pathname.startsWith('/ui-gallery') ||
          pathname.startsWith('/login')) {
        console.log('🔓 AuthProvider: 跳过认证检查 -', pathname)
        setIsInitialized(true)
        return
      }

      // 🔒 执行认证检查
      setIsInitialized(false)
      try {
        const isValid = await validateToken()
        if (!isValid) {
          console.log('❌ Token验证失败，清除认证状态')
          clearAuth()
        }
      } catch (error) {
        console.error('Auth initialization failed:', error)
        clearAuth()
      } finally {
        setIsInitialized(true)
      }
    }

    initAuth()
  }, [validateToken, clearAuth, pathname])

  // 🔄 加载状态处理
  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return <>{children}</>
}
```

**路径认证策略**:

| 路径模式 | 认证要求 | 处理方式 | 说明 |
|----------|----------|----------|------|
| `/embed/*` | 独立Token | 跳过全局认证 | iframe内部使用独立token验证 |
| `/ui-gallery` | 无需认证 | 跳过全局认证 | 公开的UI组件展示页面 |
| `/login` | 无需认证 | 跳过全局认证 | 登录页面本身不需要认证 |
| 其他路径 | 需要JWT | 执行token验证 | 所有其他页面需要有效登录 |

**认证流程详解**:

```typescript
// 🔍 Token验证流程
const validateToken = async (): Promise<boolean> => {
  try {
    // 1️⃣ 从localStorage获取token
    const token = localStorage.getItem('auth_token')
    if (!token) {
      console.log('🔍 未找到认证token')
      return false
    }

    // 2️⃣ 验证token格式和有效性
    const response = await fetch('/api/auth/verify', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    })

    if (!response.ok) {
      console.log('❌ Token验证请求失败:', response.status)
      return false
    }

    // 3️⃣ 解析用户信息
    const userData = await response.json()
    
    // 4️⃣ 更新认证状态
    setUser(userData)
    setIsAuthenticated(true)
    
    console.log('✅ Token验证成功:', userData.name)
    return true
    
  } catch (error) {
    console.error('🔥 Token验证异常:', error)
    return false
  }
}
```

#### 🔄 中间件执行顺序

```
1. 🚦 src/middleware.ts
   ├─ 路径匹配检查
   ├─ 请求日志记录
   └─ 委托给embed-security

2. 🔒 embed-security.ts
   ├─ IP和域名验证
   ├─ 路径分发处理
   └─ 安全策略执行

3. 👤 AuthProvider (仅客户端)
   ├─ 认证状态检查
   ├─ Token验证
   └─ 用户信息管理

4. 📄 页面组件
   └─ 业务逻辑执行
```

#### 🛠️ 中间件调试指南

**启用调试日志**:
```bash
# 开发环境启用详细日志
DEBUG=middleware:* pnpm run dev

# 或使用环境变量
MIDDLEWARE_DEBUG=true pnpm run dev
```

**日志格式解读**:
```bash
[Middleware] 处理请求: /embed/dashboard           # 主中间件
[EmbedSecurity] /embed/dashboard - IP: *************  # 安全中间件
🔓 AuthProvider: 跳过认证检查 - /embed/dashboard      # 认证提供者
```

**常见调试场景**:

1. **iframe访问被拒绝**:
   ```bash
   # 检查域名白名单配置
   console.log('ALLOWED_DOMAINS:', process.env.ALLOWED_DOMAINS)
   
   # 验证origin头部
   console.log('Request Origin:', request.headers.get('origin'))
   ```

2. **认证循环重定向**:
   ```bash
   # 检查认证跳过路径
   console.log('Auth skip paths:', pathname.startsWith('/embed/'))
   
   # 验证token有效性
   console.log('Token valid:', await validateToken())
   ```

3. **IP访问控制问题**:
   ```bash
   # 检查IP获取逻辑
   console.log('Client IP:', EmbedAccessLogger.getClientIP(request))
   
   # 验证内网判断
   console.log('Is internal:', isInternalIP(clientIP))
   ```

### 🔧 Git版本控制规范

统一的代码版本管理和协作规范。

#### 📝 提交信息规范

```bash
# Conventional Commits 规范
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]

# 📋 Type类型说明:
feat:     新功能 (feature)
fix:      修复bug
docs:     文档修改
style:    格式化、缺少分号等；不影响代码逻辑
refactor: 重构代码；既不新增功能，也不是修复bug
perf:     性能优化
test:     增加测试
chore:    构建过程或辅助工具的变动
ci:       CI/CD相关修改

# ✅ 提交信息示例:
feat(auth): 添加JWT token验证中间件
fix(iframe): 修复PostMessage跨域通信问题
docs: 更新API文档和使用示例
refactor(store): 重构Zustand状态管理结构
perf(api): 优化API响应缓存策略
```

#### 🌿 分支管理策略

```bash
# 🌿 Git Flow分支模型
main         # 🏷️ 生产环境分支，始终可部署
develop      # 🔄 开发分支，集成最新功能
feature/*    # ✨ 功能分支
hotfix/*     # 🚨 紧急修复分支
release/*    # 🚀 发布分支

# 📋 分支命名规范:
feature/iframe-security-enhancement
feature/ui-gallery-component-search
fix/api-proxy-timeout-handling
hotfix/critical-auth-vulnerability
release/v1.2.0

# 🔄 工作流程:
# 1. 从develop创建feature分支
git checkout develop
git checkout -b feature/your-feature-name

# 2. 开发完成后提交到feature分支
git add .
git commit -m "feat(scope): 添加新功能描述"

# 3. 推送并创建Pull Request
git push origin feature/your-feature-name

# 4. 代码审查通过后合并到develop
# 5. 定期从develop创建release分支进行测试
# 6. 测试通过后合并到main并打标签
```

---

## 🚨 常见问题解决

### ❌ 问题1: 页面无法访问 (401/403)

**症状**: 新页面显示认证错误或访问被拒绝

**解决方案**:
```typescript
// 1. 检查AuthProvider配置
// src/components/providers/auth-provider.tsx
if (pathname.startsWith('/your-page')) {
  // 添加公开访问跳过
}

// 2. 检查中间件配置
// src/lib/middleware/embed-security.ts
if (pathname.startsWith('/your-page')) {
  return NextResponse.next()
}
```

### ❌ 问题2: 样式不生效

**症状**: TailwindCSS类名不起作用

**解决方案**:
```bash
# 1. 检查tailwind.config.js中的content配置
content: [
  './src/**/*.{js,ts,jsx,tsx,mdx}',
]

# 2. 重启开发服务器
pnpm run dev

# 3. 清理缓存
pnpm run clean
```

### ❌ 问题3: API调用失败

**症状**: /api路由返回错误或超时

**解决方案**:
```typescript
// 1. 检查API基础URL配置
// .env.local
NEXT_PUBLIC_API_BASE_URL=https://dev.goglobalsp.com

// 2. 检查代理配置
// src/app/api/[...proxy]/route.ts
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL

// 3. 检查CORS配置
// next.config.ts headers()
```

### ❌ 问题4: 热更新不工作

**症状**: 代码修改后页面不自动刷新

**解决方案**:
```bash
# 1. 检查文件保存
# 确保文件确实保存了

# 2. 重启开发服务器
pnpm run dev

# 3. 清理Next.js缓存
rm -rf .next
pnpm run dev

# 4. 检查文件权限
ls -la src/
```

### ❌ 问题5: TypeScript错误

**症状**: 类型检查失败或导入错误

**解决方案**:
```typescript
// 1. 检查tsconfig.json paths配置
"paths": {
  "@/*": ["./src/*"]
}

// 2. 重启TypeScript服务器
// VSCode: Cmd+Shift+P -> "TypeScript: Restart TS Server"

// 3. 检查类型导入
import { type ComponentProps } from 'react'
```

---

## 📞 获取帮助

### 开发工具
- **开发服务器**: `pnpm run dev`
- **构建测试**: `pnpm run build`
- **类型检查**: `pnpm run type-check`
- **代码格式化**: `pnpm run format`

### 调试模式
```bash
# 启用详细日志
DEBUG=* pnpm run dev

# 启用MCP调试
MCP_DEBUG=true pnpm run dev
```

### 文档参考
- [Next.js 15 文档](https://nextjs.org/docs)
- [TailwindCSS 文档](https://tailwindcss.com/docs)
- [Zustand 文档](https://zustand-demo.pmnd.rs/)
- [Radix UI 文档](https://www.radix-ui.com/)

---

## 🎉 总结

按照本指南，您可以：

1. ✅ **快速创建新页面** - 按类型选择合适的目录和配置
2. ✅ **正确处理认证** - 理解公开、受保护、iframe页面的区别
3. ✅ **遵循最佳实践** - 使用项目约定的代码规范和架构
4. ✅ **解决常见问题** - 快速定位和修复开发中的问题

**记住**: 在开发新功能前，先了解现有的组件和工具，避免重复造轮子！

---

*🚀 祝您开发愉快！如有问题，请参考本文档或寻求团队帮助。* 