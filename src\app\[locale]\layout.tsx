import { NextIntlClientProvider } from 'next-intl'
import { getMessages } from 'next-intl/server'
import { ReactNode } from 'react'
import { notFound } from 'next/navigation'
import { locales } from '@/lib/i18n/config'

interface LocaleLayoutProps {
  children: ReactNode
  params: Promise<{ locale: string }>
}

// 生成静态参数
export function generateStaticParams() {
  return locales.map((locale) => ({ locale }))
}

export default async function LocaleLayout({
  children,
  params
}: LocaleLayoutProps) {
  // 等待参数解析并获取locale
  const { locale } = await params
  
  // 验证locale是否有效
  if (!locales.includes(locale as any)) {
    notFound()
  }
  
  // 获取当前语言的消息
  const messages = await getMessages()

  return (
    <NextIntlClientProvider locale={locale} messages={messages}>
      {children}
    </NextIntlClientProvider>
  )
} 