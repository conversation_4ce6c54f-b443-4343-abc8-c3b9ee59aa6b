// 认证相关类型
export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  role: UserRole
  permissions: Permission[]
  features: Feature[]
  rightSchemeType: RightSchemeType
  createdAt: string
  updatedAt: string
}

export interface UserRole {
  id: string
  name: string
  code: string
  description?: string
  level: number
}

export interface Permission {
  id: string
  name: string
  code: string
  resource: string
  action: string
  conditions?: Record<string, any>
}

export interface Feature {
  id: string
  name: string
  key: string
  enabled: boolean
  config?: Record<string, any>
}

export enum RightSchemeType {
  BASIC = 'basic',
  ADVANCED = 'advanced',
  ENTERPRISE = 'enterprise',
  CUSTOM = 'custom'
}

export interface LoginCredentials {
  email: string
  password: string
  remember?: boolean
}

export interface RegisterData {
  name: string
  email: string
  password: string
  confirmPassword: string
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  permissions: Permission[]
  features: Feature[]
  rightSchemeType: RightSchemeType | null
  token: string | null
  refreshToken: string | null
  expiresAt: number | null
}
