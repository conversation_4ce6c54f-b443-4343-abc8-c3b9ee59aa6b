#!/bin/bash

set -e

# 解析命令行参数
ENVIRONMENT="dev"  # 默认为开发环境
if [ "$1" == "--pd" ] || [ "$1" == "-pd" ]; then
    ENVIRONMENT="pd"
    echo "使用生产环境配置"
elif [ "$1" == "--dev" ] || [ "$1" == "-dev" ]; then
    ENVIRONMENT="dev"
    echo "使用开发环境配置"
fi

# 配置
DOCKERFILE_PATH="./Dockerfile"
BASE_IMAGE_NAME="nextjs-web-ui"
PRIVATE_REGISTRY="43.153.104.17:5000"
CONTEXT_PATH="."
TAG="latest"

# 完整镜像名
LOCAL_IMAGE_FULL_NAME="${BASE_IMAGE_NAME}:${TAG}"
REMOTE_IMAGE_FULL_NAME="${PRIVATE_REGISTRY}/${BASE_IMAGE_NAME}:${TAG}"

echo "--------------------------------------------------"
echo "构建和推送 ${BASE_IMAGE_NAME} Docker 镜像"
echo "环境: ${ENVIRONMENT}"
echo "使用固定标签: ${TAG}"
echo "--------------------------------------------------"

# 删除旧的本地镜像（如果存在）
if docker images --format "{{.Repository}}:{{.Tag}}" | grep -E "^${BASE_IMAGE_NAME}:${TAG}$" >/dev/null; then
    echo "删除旧的本地镜像: ${LOCAL_IMAGE_FULL_NAME}"
    docker rmi "${LOCAL_IMAGE_FULL_NAME}" || true
fi

# 构建新镜像
echo "构建 Docker 镜像: ${LOCAL_IMAGE_FULL_NAME}"
docker build --no-cache \
    --build-arg ENVIRONMENT="${ENVIRONMENT}" \
    --build-arg NODE_ENV=production \
    --build-arg NEXT_PUBLIC_APP_ENV="${ENVIRONMENT}" \
    --build-arg NEXT_PUBLIC_BASE_URL="https://dev.goglobalsp.com" \
    --build-arg NEXT_PUBLIC_EMBED_ENABLED="true" \
    -f "${DOCKERFILE_PATH}" \
    -t "${LOCAL_IMAGE_FULL_NAME}" \
    "${CONTEXT_PATH}"

# 标记并推送
echo "标记 Docker 镜像: ${REMOTE_IMAGE_FULL_NAME}"
docker tag "${LOCAL_IMAGE_FULL_NAME}" "${REMOTE_IMAGE_FULL_NAME}"

echo "推送 Docker 镜像到私有仓库: ${REMOTE_IMAGE_FULL_NAME}"
docker push "${REMOTE_IMAGE_FULL_NAME}"

# 清理本地镜像
echo "清理本地镜像..."
docker rmi "${LOCAL_IMAGE_FULL_NAME}" || true

echo "推送完成: ${REMOTE_IMAGE_FULL_NAME}"
echo "--------------------------------------------------" 