# iframe 嵌入系统快速部署指南

## 🚀 1. 一键部署

```bash
# 上传项目到服务器并执行
./quick_deploy.sh --pd
```

## ⚙️ 2. 修改 nginx.conf

### 2.1 在现有 nginx.conf 中添加以下配置

```nginx
# 在 http {} 块内添加
map $request_uri $allowed_embed_path {
    ~^/embed/.*$ 1;
    default 0;
}

upstream nextjs_embed {
    server 127.0.0.1:3001;
}

# 在现有 server {} 块内添加
location / {
    # 检查路径权限
    if ($allowed_embed_path = 0) {
        return 403 "Access denied. Only /embed/* paths are allowed.";
    }

    # embed 路径处理
    location ~* ^/embed/ {
        # 验证来源域名
        if ($http_referer !~* "^https://(www\.goglobalsp\.com|dev\.specific-ai\.com)") {
            return 403 "Invalid referer domain";
        }

        # iframe 头部设置
        add_header X-Frame-Options "ALLOW-FROM $http_referer" always;
        add_header Content-Security-Policy "frame-ancestors 'self' https://www.goglobalsp.com https://dev.goglobalsp.com" always;
        add_header Access-Control-Allow-Origin "$http_origin" always;
        add_header Access-Control-Allow-Credentials "true" always;

        # 代理到 Next.js
        proxy_pass http://nextjs_embed;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 健康检查 (只允许内网)
    location = /api/health {
        allow 127.0.0.1;
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;
        
        proxy_pass http://nextjs_embed;
        proxy_set_header Host $http_host;
    }
}

# 拒绝其他路径
location ~* ^/(api|dashboard|ui-gallery|_next|static)/ {
    return 403 "Access denied";
}
```

### 2.2 测试并重新加载 nginx

```bash
sudo nginx -t
sudo systemctl reload nginx
```

## 🔍 3. 部署完成验证

```bash
# 检查服务状态
docker ps | grep nextjs-web-ui


# 测试外部访问 (应该被拒绝)
curl https://dev.goglobalsp.com/dashboard

# 测试 embed 访问 (带正确 Referer 应该成功)
curl -H "Referer: https://www.goglobalsp.com" https://dev.goglobalsp.com/embed/dashboard
```

## 🎯 4. Vue3 项目中接入 iframe

### 4.1 创建 iframe 组件

```vue
<!-- EmbedDashboard.vue -->
<template>
  <div class="embed-container">
    <iframe
      ref="embedFrame"
      :src="embedUrl"
      width="100%"
      height="600"
      frameborder="0"
      @load="onIframeLoad"
    ></iframe>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const props = defineProps({
  token: {
    type: String,
    required: true
  },
  page: {
    type: String,
    default: 'dashboard'
  }
})

const embedFrame = ref(null)

// 构建 embed URL
const embedUrl = computed(() => {
  const baseUrl = 'https://dev.goglobalsp.com'
  const query = new URLSearchParams({
    token: props.token,
    // 其他参数...
  })
  return `${baseUrl}/embed/${props.page}?${query.toString()}`
})

const onIframeLoad = () => {
  console.log('iframe 加载完成')
}

// iframe 消息通信 (可选)
onMounted(() => {
  window.addEventListener('message', (event) => {
    // 验证来源
    if (event.origin !== 'https://dev.goglobalsp.com') return
    
    // 处理消息
    if (event.data.type === 'RESIZE_REQUEST') {
      // 调整 iframe 高度
      if (embedFrame.value) {
        embedFrame.value.style.height = `${event.data.height}px`
      }
    }
  })
})
</script>

<style scoped>
.embed-container {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
```

### 4.2 在页面中使用

```vue
<!-- Dashboard.vue -->
<template>
  <div class="dashboard">
    <h1>我的仪表板</h1>
    
    <!-- 嵌入 Next.js 仪表板 -->
    <EmbedDashboard 
      :token="userToken" 
      page="dashboard"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import EmbedDashboard from '@/components/EmbedDashboard.vue'

const userToken = ref('your-user-token-here')
</script>
```

### 4.3 路由配置 (Vue Router)

```javascript
// router/index.js
const routes = [
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: { 
      requiresAuth: true 
    }
  }
]
```

## 🔒 5. 安全配置

### 5.1 确保域名在白名单中

在 Next.js 项目的 `src/lib/middleware/embed-security.ts` 中确认:

```typescript
const allowedDomains = [
  'https://www.goglobalsp.com',    // 你的 Vue3 项目域名
  'https://dev.goglobalsp.com'
]
```

### 5.2 Token 验证 (推荐)

```javascript
// 在 Vue3 项目中生成 token
const generateEmbedToken = (userId, timestamp) => {
  // 使用你的 token 生成逻辑
  return btoa(`${userId}:${timestamp}:${secret}`)
}
```

## 🎉 部署完成！

现在你的 Vue3 项目可以安全地嵌入 Next.js 应用了！

**访问地址**:
- Vue3 项目: `https://www.goglobalsp.com/dashboard`
- 嵌入的 iframe: `https://dev.goglobalsp.com/embed/dashboard?token=xxx`

**安全保护**:
- ✅ 只有 `/embed/*` 可被外部访问
- ✅ 验证 Referer 头部
- ✅ 其他路径全部拒绝访问 