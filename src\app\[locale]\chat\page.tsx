'use client'

import { useTranslations } from 'next-intl'
import { ProtectedRoute } from '@/components/providers/auth-provider'

export default function ChatPage() {
  const t = useTranslations('chat')
  
  return (
    <ProtectedRoute>
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            {t('welcome', { defaultValue: 'Welcome to SpecificAI!' })}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t('description', { defaultValue: 'Your AI-powered assistant is ready to help.' })}
          </p>
        </div>
      </div>
    </ProtectedRoute>
  )
} 