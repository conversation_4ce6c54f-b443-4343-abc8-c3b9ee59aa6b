{"common": {"loading": "加载中...", "error": "错误", "success": "成功", "cancel": "取消", "confirm": "确认", "save": "保存", "delete": "删除", "edit": "编辑", "back": "返回", "next": "下一步", "previous": "上一步", "submit": "提交", "close": "关闭", "search": "搜索", "filter": "筛选", "refresh": "刷新", "retry": "重试"}, "auth": {"login": {"title": "欢迎回来", "subtitle": "登录到您的 Vibe Coding 账户", "username": "用户名或邮箱", "usernamePlaceholder": "请输入用户名或邮箱", "password": "密码", "passwordPlaceholder": "请输入密码", "forgotPassword": "忘记密码？", "loginButton": "登录", "loggingIn": "登录中...", "orLoginWith": "或者使用第三方登录", "loginWithApple": "使用 Apple 登录", "loginWithGoogle": "使用 Google 登录", "loginWithFacebook": "使用 Facebook 登录", "noAccount": "还没有账号？", "registerNow": "立即注册", "brandTitle": "Vibe Coding Refactory", "brandSubtitle": "企业级AI应用前端架构", "feature1": "✨ Vue3 到 Next.js 完整迁移", "feature2": "🚀 现代化组件系统", "feature3": "🔐 完整的认证与权限管理", "feature4": "🎨 企业级UI设计系统", "termsText": "点击继续即表示您同意我们的", "termsOfService": "服务条款", "and": "和", "privacyPolicy": "隐私政策", "period": "。"}, "validation": {"usernameRequired": "请输入用户名或邮箱", "passwordRequired": "请输入密码"}, "messages": {"loginSuccess": "登录成功", "welcomeBack": "欢迎回来，{username}！", "loginFailed": "登录失败", "loginError": "登录过程中发生错误", "featureInDevelopment": "功能开发中", "forgotPasswordInDevelopment": "忘记密码功能正在开发中", "registerInDevelopment": "注册功能正在开发中"}, "legacy": {"login": "登录", "logout": "退出登录", "pleaseLogin": "请先登录", "invalidCredentials": "用户名或密码错误", "accountLocked": "账户已被锁定，请联系管理员", "userDeleted": "该用户已被删除，请联系管理员"}}, "navigation": {"chat": "Cha<PERSON>", "uiGallery": "UI Gallery", "home": "首页", "dashboard": "仪表板", "settings": "设置"}, "chat": {"title": "Chat Interface", "subtitle": "Coming soon...", "welcome": "欢迎来到 SpecificAI！", "description": "您的AI助手已准备就绪。", "placeholder": "输入消息...", "send": "发送", "clear": "清空对话", "typing": "正在输入..."}, "ui": {"gallery": {"title": "UI 组件库", "description": "测试和调试所有UI组件的样式验证", "buttons": "按钮组件", "inputs": "输入组件", "cards": "卡片组件", "markdown": "Markdown 渲染器演示"}}, "uiGallery": {"title": "UI Component Gallery", "subtitle": "项目中可用的组件展示", "radixComponents": "Radix U<PERSON> 组件", "customComponents": "自定义组件", "componentAvailable": "组件可用"}, "errors": {"unauthorized": "未授权，请重新登录", "noPermission": "权限不足，无法访问", "userNotLogin": "用户未登录，请先登录", "userDeleted": "该用户已被删除，请联系管理员", "networkError": "网络连接失败，请检查网络设置", "serverError": "服务器内部错误", "notFound": "请求的资源不存在", "badRequest": "请求参数错误", "forbidden": "权限不足", "timeout": "请求超时", "unknown": "未知错误"}, "language": {"switch": "切换语言", "chinese": "中文", "english": "English", "japanese": "日本語", "current": "当前语言"}, "user": {"profile": "个人资料", "account": "账户", "preferences": "偏好设置", "avatar": "头像", "name": "姓名", "email": "邮箱", "role": "角色", "lastLogin": "最后登录", "memberSince": "注册时间"}, "embed": {"initializing": "初始化中...", "loading": "正在加载页面", "pipeline": {"status": {"preparing": "准备启动", "completed": "流水线执行完成", "running": "正在为{userName}生成[{title}]的详细背景信息，当前正在执行{currentStep}，请耐心等待或检查操作", "defaultUser": "用户", "defaultTitle": "招投标项目"}}, "report": {"title": "AI 市场分析报告", "subtitle": "AI Insights", "generating": "生成中", "complete": "完成", "analyzing": "正在分析数据并生成洞察...", "viewLatest": "查看最新更新", "outlineNavigation": "大纲导航", "comingSoon": "即将推出", "executiveSummary": "执行摘要", "marketSegmentation": "市场细分", "industryApplications": "行业应用", "futureOutlook": "未来展望", "recommendations": "建议", "content": {"title": "AI 市场分析报告", "subtitle": "生成于 2024年12月20日", "summary": {"title": "执行摘要", "description": "人工智能市场持续经历前所未有的增长，在多个行业都有重大发展。我们的分析揭示了将在2025年塑造行业格局的关键趋势。", "highlights": "关键亮点", "marketSize": "市场规模：预计2024年达到1846亿美元", "growthRate": "增长率：到2030年复合年增长率28.7%", "leadingSectors": "领先行业：医疗保健、金融、汽车"}, "segmentation": {"title": "市场细分", "byTechnology": "按技术分类", "mlDominance": "机器学习占据65%的市场份额，其次是自然语言处理占18%。", "machineLearning": "机器学习：1198亿美元", "nlp": "自然语言处理：332亿美元", "computerVision": "计算机视觉：221亿美元", "robotics": "机器人技术：95亿美元", "geographic": "地理分布", "northAmerica": "北美：42%市场份额", "asiaPacific": "亚太地区：31%市场份额", "europe": "欧洲：21%市场份额", "restOfWorld": "世界其他地区：6%市场份额"}, "applications": {"title": "行业应用", "healthcare": {"title": "医疗保健", "description": "医疗保健中的AI应用正在通过以下方式革命性地改变患者护理：", "medicalImaging": "医学影像：诊断影像准确率94%", "drugDiscovery": "药物发现：开发时间缩短40%", "personalizedTreatment": "个性化治疗：定制化治疗建议"}, "financial": {"title": "金融服务", "warning": "监管合规仍然是AI在金融服务实施中的关键考虑因素。", "fraudDetection": "欺诈检测：实时交易监控", "algoTrading": "算法交易：高频决策制定", "riskAssessment": "风险评估：增强的信用评分模型"}}, "outlook": {"title": "未来展望", "trends": "新兴趋势", "generativeAI": {"title": "生成式AI采用", "description": "73%的企业计划在2025年前实施", "focus": "专注于内容创作和自动化"}, "edgeAI": {"title": "边缘AI计算", "description": "为实时应用减少延迟", "benefits": "增强隐私和安全性"}, "governance": {"title": "AI治理框架", "description": "标准化的伦理指导原则", "automation": "监管合规自动化"}, "investment": {"title": "投资格局", "description": "AI初创公司的风险投资在2024年达到252亿美元，比上一年增长15%。", "topAreas": "主要投资领域：", "autonomous": "自主系统：81亿美元", "healthcareAI": "医疗保健AI：63亿美元", "enterprise": "企业自动化：48亿美元", "cybersecurity": "网络安全AI：32亿美元"}}, "recommendations": {"title": "建议", "description": "基于我们的综合分析，我们建议采取以下战略行动：", "prioritize": "优先考虑AI集成：组织应制定明确的AI采用路线图", "talent": "投资人才：通过培训和招聘建立内部AI能力", "ethics": "关注伦理：从一开始就实施负责任的AI实践", "regulations": "监控法规：了解不断发展的AI治理要求"}, "completion": "报告完成。分析基于来自500多家公司和50多个行业来源的数据。"}}, "test": {"title": "Embed测试页面", "authMode": "认证模式", "vue3Mode": {"title": "Vue3旧项目模式", "description": "绕过Next.js认证系统，直接使用URL中的token"}, "nextjsMode": {"title": "Next.js标准模式", "description": "使用Next.js认证系统验证用户信息"}, "userInfo": "用户信息", "loading": "加载中...", "error": "错误", "user": "用户", "vue3NoUser": "Vue3模式：不获取用户信息，直接使用token", "noUserInfo": "未获取到用户信息", "debugInfo": "调试信息", "testLinks": "测试链接", "nextjsStandard": "Next.js标准模式:", "vue3Legacy": "Vue3旧项目模式:", "tip": "提示:", "tipDescription": "这个测试页面用于验证embed页面的鉴权逻辑。from-og=true时会绕过Next.js认证系统，直接使用URL中的token。"}}}