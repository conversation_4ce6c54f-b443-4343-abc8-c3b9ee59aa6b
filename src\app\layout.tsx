import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { AuthProvider } from '@/components/providers/auth-provider'
import { Toaster } from 'sonner'
import { headers } from 'next/headers'
import { defaultLocale } from '@/lib/i18n/config'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-sans',
})

export const metadata: Metadata = {
  title: 'SpecificAI',
  description: 'SpecificAI is a platform for AI-powered content creation and analysis.',
  keywords: ['AI', 'Content Creation', 'Analysis'],
  authors: [{ name: 'SpecificAI' }],
  icons: {
    icon: [
      { url: '/icon', sizes: '32x32', type: 'image/png' },
      { url: '/logo.svg', sizes: 'any', type: 'image/svg+xml' },
    ],
    apple: { url: '/icon', sizes: '180x180', type: 'image/png' },
    shortcut: '/logo.svg',
  },
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // 从headers中获取当前locale，如果没有则使用默认locale
  const headersList = await headers()
  const pathname = headersList.get('x-pathname') || ''
  const locale = pathname.split('/')[1] || defaultLocale

  return (
    <html lang={locale} suppressHydrationWarning className="w-full h-full">
      <body className={`${inter.variable} font-sans antialiased w-full h-full m-0 p-0 box-border overflow-hidden`}>
        <AuthProvider>
          {children}
        </AuthProvider>
        <Toaster
          position="top-right"
          richColors
          closeButton
          duration={4000}
        />
      </body>
    </html>
  )
}
