/**
 * 测试页面组件
 * 用于验证from-og参数和Vue3旧项目token传递功能
 */

'use client'

import React from 'react'
import { useTranslations } from 'next-intl'

interface TestPageProps {
  user?: any
  userLoading?: boolean
  userError?: any
  fromOg?: boolean
  token?: string
}

export default function TestPage({
  user,
  userLoading,
  userError,
  fromOg = false,
  token
}: TestPageProps) {
  const t = useTranslations('embed.test')
  const [debugInfo, setDebugInfo] = React.useState<any>({})

  React.useEffect(() => {
    // 收集调试信息
    const urlParams = new URLSearchParams(window.location.search)
    setDebugInfo({
      url: window.location.href,
      fromOg,
      hasToken: !!token,
      tokenLength: token ? token.length : 0,
      urlParams: Object.fromEntries(urlParams.entries()),
      user: user ? {
        id: user.id,
        user_name: user.user_name,
        user_type: user.user_type
      } : null,
      userLoading,
      userError: userError ? userError.message : null,
      timestamp: new Date().toISOString()
    })
  }, [user, userLoading, userError, fromOg, token])

  return (
    <div style={{
      padding: '20px',
      fontFamily: 'monospace',
      background: '#f8f9fa',
      border: '1px solid #dee2e6',
      borderRadius: '8px'
    }}>
      <h2 style={{
        margin: '0 0 20px 0',
        color: fromOg ? '#28a745' : '#007bff',
        fontSize: '24px'
      }}>
        🧪 {t('title')}
      </h2>

      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 10px 0', color: '#495057' }}>{t('authMode')}</h3>
        <div style={{
          padding: '10px',
          background: fromOg ? '#d4edda' : '#d1ecf1',
          border: `1px solid ${fromOg ? '#c3e6cb' : '#bee5eb'}`,
          borderRadius: '4px',
          color: fromOg ? '#155724' : '#0c5460'
        }}>
          {fromOg ? (
            <>
              <strong>🔄 {t('vue3Mode.title')}</strong>
              <br />
              {t('vue3Mode.description')}
            </>
          ) : (
            <>
              <strong>🔐 {t('nextjsMode.title')}</strong>
              <br />
              {t('nextjsMode.description')}
            </>
          )}
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 10px 0', color: '#495057' }}>{t('userInfo')}</h3>
        <div style={{
          padding: '10px',
          background: '#fff',
          border: '1px solid #dee2e6',
          borderRadius: '4px'
        }}>
          {userLoading && <div style={{ color: '#6c757d' }}>🔄 {t('loading')}</div>}
          {userError && (
            <div style={{ color: '#dc3545' }}>
              ❌ {t('error')}: {userError.message || userError}
            </div>
          )}
          {user && (
            <div style={{ color: '#28a745' }}>
              ✅ {t('user')}: {user.user_name} (ID: {user.id})
            </div>
          )}
          {!userLoading && !userError && !user && fromOg && (
            <div style={{ color: '#ffc107' }}>
              ⚠️ {t('vue3NoUser')}
            </div>
          )}
          {!userLoading && !userError && !user && !fromOg && (
            <div style={{ color: '#dc3545' }}>
              ❌ {t('noUserInfo')}
            </div>
          )}
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 10px 0', color: '#495057' }}>{t('debugInfo')}</h3>
        <pre style={{
          padding: '10px',
          background: '#fff',
          border: '1px solid #dee2e6',
          borderRadius: '4px',
          fontSize: '12px',
          overflow: 'auto',
          maxHeight: '300px'
        }}>
          {JSON.stringify(debugInfo, null, 2)}
        </pre>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 10px 0', color: '#495057' }}>{t('testLinks')}</h3>
        <div style={{ fontSize: '12px', color: '#6c757d' }}>
          <p><strong>{t('nextjsStandard')}</strong></p>
          <code style={{ background: '#e9ecef', padding: '2px 4px', borderRadius: '2px' }}>
            /zh/embed/test?token=YOUR_TOKEN
          </code>

          <p style={{ marginTop: '10px' }}><strong>{t('vue3Legacy')}</strong></p>
          <code style={{ background: '#e9ecef', padding: '2px 4px', borderRadius: '2px' }}>
            /zh/embed/test?from-og=true&token=YOUR_TOKEN
          </code>
        </div>
      </div>

      <div style={{
        padding: '10px',
        background: '#fff3cd',
        border: '1px solid #ffeaa7',
        borderRadius: '4px',
        fontSize: '12px',
        color: '#856404'
      }}>
        <strong>💡 {t('tip')}</strong>
        {t('tipDescription')}
      </div>
    </div>
  )
}
