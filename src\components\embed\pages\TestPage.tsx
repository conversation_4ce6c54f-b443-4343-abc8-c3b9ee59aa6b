/**
 * 测试页面组件
 * 用于验证from-og参数和Vue3旧项目token传递功能
 */

'use client'

import React from 'react'

interface TestPageProps {
  user?: any
  userLoading?: boolean
  userError?: any
  fromOg?: boolean
  token?: string
}

export default function TestPage({ 
  user, 
  userLoading, 
  userError, 
  fromOg = false,
  token 
}: TestPageProps) {
  const [debugInfo, setDebugInfo] = React.useState<any>({})

  React.useEffect(() => {
    // 收集调试信息
    const urlParams = new URLSearchParams(window.location.search)
    setDebugInfo({
      url: window.location.href,
      fromOg,
      hasToken: !!token,
      tokenLength: token ? token.length : 0,
      urlParams: Object.fromEntries(urlParams.entries()),
      user: user ? {
        id: user.id,
        user_name: user.user_name,
        user_type: user.user_type
      } : null,
      userLoading,
      userError: userError ? userError.message : null,
      timestamp: new Date().toISOString()
    })
  }, [user, userLoading, userError, fromOg, token])

  return (
    <div style={{
      padding: '20px',
      fontFamily: 'monospace',
      background: '#f8f9fa',
      border: '1px solid #dee2e6',
      borderRadius: '8px'
    }}>
      <h2 style={{ 
        margin: '0 0 20px 0', 
        color: fromOg ? '#28a745' : '#007bff',
        fontSize: '24px'
      }}>
        🧪 Embed测试页面
      </h2>
      
      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 10px 0', color: '#495057' }}>认证模式</h3>
        <div style={{
          padding: '10px',
          background: fromOg ? '#d4edda' : '#d1ecf1',
          border: `1px solid ${fromOg ? '#c3e6cb' : '#bee5eb'}`,
          borderRadius: '4px',
          color: fromOg ? '#155724' : '#0c5460'
        }}>
          {fromOg ? (
            <>
              <strong>🔄 Vue3旧项目模式</strong>
              <br />
              绕过Next.js认证系统，直接使用URL中的token
            </>
          ) : (
            <>
              <strong>🔐 Next.js标准模式</strong>
              <br />
              使用Next.js认证系统验证用户信息
            </>
          )}
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 10px 0', color: '#495057' }}>用户信息</h3>
        <div style={{
          padding: '10px',
          background: '#fff',
          border: '1px solid #dee2e6',
          borderRadius: '4px'
        }}>
          {userLoading && <div style={{ color: '#6c757d' }}>🔄 加载中...</div>}
          {userError && (
            <div style={{ color: '#dc3545' }}>
              ❌ 错误: {userError.message || userError}
            </div>
          )}
          {user && (
            <div style={{ color: '#28a745' }}>
              ✅ 用户: {user.user_name} (ID: {user.id})
            </div>
          )}
          {!userLoading && !userError && !user && fromOg && (
            <div style={{ color: '#ffc107' }}>
              ⚠️ Vue3模式：不获取用户信息，直接使用token
            </div>
          )}
          {!userLoading && !userError && !user && !fromOg && (
            <div style={{ color: '#dc3545' }}>
              ❌ 未获取到用户信息
            </div>
          )}
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 10px 0', color: '#495057' }}>调试信息</h3>
        <pre style={{
          padding: '10px',
          background: '#fff',
          border: '1px solid #dee2e6',
          borderRadius: '4px',
          fontSize: '12px',
          overflow: 'auto',
          maxHeight: '300px'
        }}>
          {JSON.stringify(debugInfo, null, 2)}
        </pre>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 10px 0', color: '#495057' }}>测试链接</h3>
        <div style={{ fontSize: '12px', color: '#6c757d' }}>
          <p><strong>Next.js标准模式:</strong></p>
          <code style={{ background: '#e9ecef', padding: '2px 4px', borderRadius: '2px' }}>
            /zh/embed/test?token=YOUR_TOKEN
          </code>
          
          <p style={{ marginTop: '10px' }}><strong>Vue3旧项目模式:</strong></p>
          <code style={{ background: '#e9ecef', padding: '2px 4px', borderRadius: '2px' }}>
            /zh/embed/test?from-og=true&token=YOUR_TOKEN
          </code>
        </div>
      </div>

      <div style={{
        padding: '10px',
        background: '#fff3cd',
        border: '1px solid #ffeaa7',
        borderRadius: '4px',
        fontSize: '12px',
        color: '#856404'
      }}>
        <strong>💡 提示:</strong> 
        这个测试页面用于验证embed页面的鉴权逻辑。
        from-og=true时会绕过Next.js认证系统，直接使用URL中的token。
      </div>
    </div>
  )
}
