# Vue3 到 Next.js 重构任务清单

## 📊 项目总览

- **总预估工作量**: 280小时 (约14周，2人团队)
- **项目开始时间**: 待定
- **预计完成时间**: 待定
- **当前进度**: 0% (规划阶段)

## 🎯 阶段一: 基础架构搭建 (40小时 - 2周)

### 1.1 Monorepo环境搭建 (8小时)
**目标**: 替换原项目的单体架构，建立现代化的Monorepo开发环境

**原项目现状分析**:
```
当前项目结构 (单体应用):
go-global-fd/
├── src/                 # 所有源码在一个目录
├── package.json         # 单一包管理
├── vite.config.ts       # 单一构建配置
└── node_modules/        # 依赖混合管理
```

**迁移任务**:
- [ ] **安装Turborepo** (2小时)
  - 初始化Turborepo项目
  - 配置pnpm workspace
  - 设置基础目录结构
  - **替换**: 单体项目结构 → Monorepo架构

- [ ] **配置包管理** (3小时)
  - 设置pnpm配置
  - 配置依赖版本管理
  - 设置workspace依赖关系
  - **替换**: package.json依赖管理 → workspace依赖管理

- [ ] **设置开发工具** (3小时)
  - 配置ESLint共享配置
  - 配置Prettier
  - 设置Git hooks (husky)
  - **迁移**: 原项目的lint-staged配置和husky hooks

### 1.2 Next.js项目初始化 (12小时)
**目标**: 替换Vue3 + Vite构建系统为Next.js构建系统

**原项目构建配置分析**:
```typescript
// vite.config.ts - 需要迁移的配置
export default defineConfig({
  plugins: [
    VueRouter({ exclude: excludeRouter }),
    vue(), vueJsx(), VueDevTools(),
    Layouts(), PreprocessorDirectives(),
    chunkSplitPlugin(), viteCompression(),
    vitePluginCDN2() // CDN优化配置
  ],
  server: { proxy: { '/api': { target: env.VITE_SERVER_API_URL } } },
  build: { rollupOptions: { manualChunks: { mermaid: ['mermaid'] } } }
})
```

**迁移任务**:
- [ ] **创建主应用** (4小时)
  - 初始化Next.js 15项目
  - 配置App Router (替换Vue Router文件系统路由)
  - 设置TypeScript配置 (迁移tsconfig.json)
  - **替换**: Vue3 SPA → Next.js App Router应用

- [ ] **配置构建工具** (4小时)
  - 迁移Vite配置到Next.js (next.config.js)
  - 配置环境变量系统 (迁移.env配置)
  - 设置构建优化选项 (代码分割、CDN配置)
  - **替换**: Vite构建系统 → Next.js内置构建

- [ ] **设置开发环境** (4小时)
  - 配置开发服务器 (替换Vite dev server)
  - 设置热重载 (Next.js HMR)
  - 配置代理设置 (迁移API代理配置)
  - **替换**: Vite开发服务器 → Next.js开发服务器

### 1.3 TailwindCSS v4配置 (8小时)
**目标**: 保持TailwindCSS v4，迁移样式配置和主题系统

**原项目样式架构分析**:
```typescript
// tailwind.config.js - 现有配置
module.exports = {
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: { 'main-color': '#1f2937' },
      fontFamily: { sans: ['Inter', 'sans-serif'] }
    }
  },
  plugins: [require('@tailwindcss/typography')]
}

// 全局样式文件
src/assets/main.css      # 主样式文件
src/assets/base.css      # 基础样式
src/assets/var.scss      # SCSS变量
src/assets/element.scss  # Element Plus样式覆盖
```

**迁移任务**:
- [ ] **安装配置TailwindCSS** (3小时)
  - 安装TailwindCSS v4 (保持版本一致)
  - 迁移tailwind.config.js配置
  - 设置自定义主题 (保持现有设计系统)
  - **保持**: 现有的Tailwind配置和自定义主题

- [ ] **样式系统迁移** (5小时)
  - 迁移全局样式 (main.css, base.css)
  - 配置CSS变量 (迁移var.scss中的变量)
  - 设置响应式断点 (保持现有断点配置)
  - **替换**: Element Plus样式覆盖 → Radix UI样式定制

### 1.4 基础包结构搭建 (12小时)
- [ ] **创建共享包** (6小时)
  - 创建@specific-ai/ui包
  - 创建@specific-ai/utils包
  - 创建@specific-ai/types包
  - 创建@specific-ai/hooks包
- [ ] **配置包构建** (3小时)
  - 设置tsup构建配置
  - 配置包导出
  - 设置类型声明
- [ ] **包间依赖配置** (3小时)
  - 配置包间引用
  - 设置版本管理
  - 测试包导入导出

## 🧩 阶段二: 核心组件迁移 (60小时 - 3周)

### 2.1 Radix UI + shadcn/ui集成 (16小时)
**目标**: 替换Element Plus组件库为Radix UI + shadcn/ui

**原项目组件库使用分析**:
```typescript
// Element Plus组件使用情况
import { ElMessage, ElMessageBox, ElCheckbox, ElButton, ElInput,
         ElSelect, ElDialog, ElTooltip, ElLoading } from 'element-plus'

// 自定义UI组件 (基于reka-ui)
import { Button } from '@/components/ui/button'
import { Card, CardHeader, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

// 组件使用统计
- ElButton: 50+ 使用
- ElInput: 30+ 使用
- ElSelect: 20+ 使用
- ElDialog: 15+ 使用
- ElMessage: 全局使用
```

**迁移任务**:
- [ ] **安装配置Radix UI** (4小时)
  - 安装核心Radix组件
  - 配置shadcn/ui CLI
  - 设置组件主题系统
  - **替换**: Element Plus → Radix UI基础架构

- [ ] **基础组件迁移** (12小时)
  - Button组件 (2小时) - 替换ElButton，保持现有样式
  - Input组件 (2小时) - 替换ElInput，迁移验证逻辑
  - Select组件 (2小时) - 替换ElSelect，保持多选功能
  - Dialog组件 (2小时) - 替换ElDialog，保持弹窗逻辑
  - Tooltip组件 (2小时) - 替换ElTooltip，保持提示功能
  - Loading组件 (2小时) - 替换ElLoading，保持加载状态

### 2.2 布局组件迁移 (20小时)
- [ ] **主布局组件** (8小时)
  - 迁移default.vue布局 (4小时)
  - 迁移SettingLayout.vue (2小时)
  - 迁移none.vue布局 (2小时)
- [ ] **导航组件** (12小时)
  - 迁移AppSidebar.vue (4小时)
  - 迁移NavMain.vue (3小时)
  - 迁移NavUser.vue (3小时)
  - 迁移TeamSwitcher.vue (2小时)

### 2.3 表单组件迁移 (24小时)
- [ ] **Element Plus表单组件替换** (20小时)
  - ElSelectV2Group → Radix Select (4小时)
  - ElSelectV2Normal → Radix Select (3小时)
  - 表单验证逻辑迁移 (5小时)
  - 日期选择器迁移 (4小时)
  - 文件上传组件迁移 (4小时)
- [ ] **自定义表单组件** (4小时)
  - ContactUsEmailInput迁移 (2小时)
  - PricingPlansForm迁移 (2小时)

## 🔐 阶段三: 认证与路由系统 (48小时 - 2.5周)

### 3.1 认证系统迁移 (20小时)
- [ ] **用户状态管理** (8小时)
  - 迁移user.ts store到Zustand (4小时)
  - 实现用户权限hooks (2小时)
  - 迁移token管理逻辑 (2小时)
- [ ] **认证组件迁移** (12小时)
  - 迁移AuthDialog.vue (4小时)
  - 迁移登录页面组件 (4小时)
  - 实现认证状态管理 (4小时)

### 3.2 路由守卫系统重构 (28小时)
**目标**: 将Vue Router守卫系统迁移到Next.js Middleware

**原项目路由守卫分析**:
```typescript
// 5层路由守卫系统 (按执行顺序)
auth.guard.0.ts      // 认证守卫
access.guard.1.ts    // 访问控制守卫
poll.guard.2.ts      // 业务流程守卫
menu.guard.3.ts      // 菜单守卫
feedback.guard.4.ts  // 反馈守卫

// 核心守卫逻辑
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  if (to.meta.requiresAuth && !await userStore.validateToken()) {
    redirectToPath('/landing', to, from, next)
  }
  if (!userStore.checkAccess(to.meta.access)) {
    redirectToPath('/', to, from, next)
  }
})
```

**迁移任务**:
- [ ] **Next.js Middleware实现** (16小时)
  - 实现认证守卫 (4小时) - 迁移auth.guard.0.ts逻辑
    - **原逻辑**: 检查requiresAuth/requiresGuest/tryAuth
    - **目标**: Next.js middleware中实现token验证
  - 实现访问控制守卫 (6小时) - 迁移access.guard.1.ts逻辑
    - **原逻辑**: 检查features/roles/permissions/rightSchemeType
    - **目标**: 实现细粒度权限控制
  - 实现业务流程守卫 (4小时) - 迁移poll.guard.2.ts逻辑
    - **原逻辑**: 问卷流程控制，防止跳过步骤
    - **目标**: 保持业务流程完整性
  - 实现菜单守卫 (2小时) - 迁移menu.guard.3.ts逻辑
    - **原逻辑**: 菜单权限验证和重定向
    - **目标**: 动态菜单访问控制

- [ ] **路由配置迁移** (8小时)
  - 迁移路由元数据配置 (4小时)
    - **原配置**: Vue Router meta字段
    - **目标**: Next.js路由配置系统
  - 实现动态路由 (2小时) - 迁移[id].vue等动态路由
  - 配置路由重定向 (2小时) - 迁移redirectToPath逻辑

- [ ] **路由测试** (4小时)
  - 编写路由守卫测试 (2小时)
  - 测试权限控制逻辑 (2小时)

## 📄 阶段四: 页面功能迁移 (80小时 - 4周)

### 4.1 核心页面迁移 (32小时)
- [ ] **首页相关** (8小时)
  - 迁移home.vue (3小时)
  - 迁移index.vue (2小时)
  - 迁移landing.vue (3小时)
- [ ] **仪表板页面** (12小时)
  - 迁移dashboard主页面 (6小时)
  - 迁移dashboard子组件 (6小时)
- [ ] **设置页面** (12小时)
  - 迁移settings主页面 (6小时)
  - 迁移设置子页面 (6小时)

### 4.2 业务功能页面 (48小时)
**目标**: 迁移核心业务功能页面，保持完整的业务逻辑

**原项目业务页面分析**:
```typescript
// workspace/index.vue - 工作空间主页面
- 标签页系统: standard_feed, notify_insights, special_analysis
- 权限控制: canAccessNormalFeature, canAccessNotifyFeature, canAccessSpecialAnalysisFeature
- 数据管理: useReports, useWorkspaceAgents, useAgents
- 子组件: BidingRespon, ReportFeed, AgentListSidebar

// agent-market/ - AI代理市场
- 代理列表展示和管理
- 代理创建和配置
- 市场浏览和搜索

// format-report/ - 报告生成
- 报告模板系统
- 图表展示 (ChartDisplay.vue)
- PDF生成功能
```

**迁移任务**:
- [ ] **工作空间功能** (16小时)
  - 迁移workspace主页面 (6小时)
    - **原组件**: src/pages/workspace/index.vue (1200+行)
    - **核心逻辑**: 标签页切换、权限控制、数据过滤
    - **目标**: Next.js页面组件，保持所有业务逻辑
  - 迁移工作空间管理 (5小时)
    - **原组件**: BidingRespon.vue, BidingTableView.vue
    - **核心功能**: 招投标数据展示、批量操作、虚拟滚动
    - **目标**: React组件，保持表格功能和性能
  - 迁移协作功能 (5小时)
    - **原组件**: AgentListSidebar.vue, ReportFeed.vue
    - **核心功能**: 代理列表、报告展示、实时更新
    - **目标**: React组件，保持实时通信功能

- [ ] **AI代理市场** (16小时)
  - 迁移agent-market页面 (8小时)
    - **原页面**: src/pages/agent-market/
    - **核心功能**: 代理浏览、搜索、分类
    - **目标**: Next.js页面，保持市场功能
  - 迁移代理管理功能 (8小时)
    - **核心功能**: 代理创建、编辑、配置
    - **目标**: 保持代理管理的完整功能

- [ ] **报告生成功能** (16小时)
  - 迁移format-report页面 (8小时)
    - **原页面**: src/pages/format-report/
    - **核心组件**: ChartDisplay.vue, UserQueryDialog.vue
    - **目标**: Next.js页面，保持报告生成功能
  - 迁移报告模板系统 (8小时)
    - **核心功能**: 模板管理、图表配置、PDF导出
    - **目标**: 保持完整的报告系统

## 📊 阶段五: 高级功能迁移 (72小时 - 3.5周)

### 5.1 数据可视化迁移 (24小时)
**目标**: 迁移ECharts图表系统，保持数据可视化功能

**原项目图表系统分析**:
```typescript
// EchartsComponent.vue - 核心图表组件
export default defineComponent({
  props: {
    params: Object as PropType<ChartParams>,
    getOption: Function as PropType<(params: ChartParams) => EChartsOptions>,
    autoSize: Boolean,
    notMerge: Boolean,
    renderTimeout: Number
  },
  emits: ['created', 'rendered', 'error', 'dispose']
})

// 图表组件体系
CommonLineChart.vue      // 折线图 - 用于趋势分析
CommonBarChart.vue       // 柱状图 - 用于数据对比
CommonRadarChart.vue     // 雷达图 - 用于多维度分析
CommonWordCloud.vue      // 词云图 - 用于文本分析
CommonSingleLineChart.vue // 单线图 - 用于简单趋势

// 使用场景
- format-report页面: 报告图表展示
- dashboard页面: 数据概览
- workspace页面: 分析结果可视化
```

**迁移任务**:
- [ ] **ECharts集成** (12小时)
  - 迁移EchartsComponent.vue (4小时)
    - **原组件**: 220行Vue组件，支持异步渲染、错误处理
    - **核心功能**: 图表初始化、选项设置、生命周期管理
    - **目标**: React Hook组件，保持所有功能
  - 实现React ECharts hooks (4小时)
    - **原逻辑**: Vue响应式系统 + ECharts实例管理
    - **目标**: useECharts hook，管理图表状态和生命周期
  - 迁移图表配置逻辑 (4小时)
    - **原配置**: getOption函数、参数响应式更新
    - **目标**: 配置管理hooks，保持图表配置的灵活性

- [ ] **图表组件迁移** (12小时)
  - 迁移CommonChartComponents (8小时)
    - **原组件**: 5个图表组件，每个50-100行
    - **核心功能**: 特定图表类型的配置和渲染
    - **目标**: React图表组件，保持图表样式和交互
  - 实现图表数据管理 (4小时)
    - **原逻辑**: props传递 + computed计算
    - **目标**: 数据处理hooks，优化图表性能

### 5.2 PDF功能迁移 (16小时)
- [ ] **PDF组件迁移** (12小时)
  - 迁移PdfComponents (6小时)
  - 迁移PdfActionButtons (3小时)
  - 实现PDF生成hooks (3小时)
- [ ] **PDF功能测试** (4小时)
  - 测试PDF生成功能 (2小时)
  - 测试PDF下载功能 (2小时)

### 5.3 实时通信迁移 (16小时)
- [ ] **WebSocket集成** (8小时)
  - 迁移useWebSocket hook (4小时)
  - 迁移useSafeWebsocket hook (4小时)
- [ ] **SSE功能迁移** (8小时)
  - 迁移useEnhancedEventSource (4小时)
  - 迁移AI流式响应 (4小时)

### 5.4 国际化迁移 (16小时)
- [ ] **next-intl集成** (8小时)
  - 安装配置next-intl (2小时)
  - 迁移语言文件 (4小时)
  - 配置路由国际化 (2小时)
- [ ] **国际化组件迁移** (8小时)
  - 迁移i18n store (4小时)
  - 实现语言切换功能 (4小时)

## 🧪 阶段六: 测试与优化 (40小时 - 2周)

### 6.1 测试体系建设 (24小时)
- [ ] **单元测试** (12小时)
  - 组件测试覆盖 (6小时)
  - hooks测试覆盖 (4小时)
  - 工具函数测试 (2小时)
- [ ] **集成测试** (8小时)
  - 页面功能测试 (4小时)
  - 路由测试 (2小时)
  - API集成测试 (2小时)
- [ ] **E2E测试** (4小时)
  - 关键用户流程测试 (4小时)

### 6.2 性能优化 (16小时)
- [ ] **构建优化** (8小时)
  - 代码分割优化 (4小时)
  - 包体积分析优化 (2小时)
  - 缓存策略优化 (2小时)
- [ ] **运行时优化** (8小时)
  - 组件懒加载 (3小时)
  - 图片优化 (2小时)
  - 首屏性能优化 (3小时)

## 📚 文档与部署 (20小时 - 1周)

### 7.1 文档编写 (12小时)
- [ ] **技术文档** (8小时)
  - API文档更新 (3小时)
  - 组件使用文档 (3小时)
  - 架构设计文档 (2小时)
- [ ] **用户文档** (4小时)
  - 功能使用指南 (2小时)
  - 迁移对比说明 (2小时)

### 7.2 部署配置 (8小时)
- [ ] **生产环境配置** (4小时)
  - Next.js生产配置 (2小时)
  - 环境变量配置 (2小时)
- [ ] **CI/CD配置** (4小时)
  - 构建流程配置 (2小时)
  - 部署流程配置 (2小时)

## 📈 进度跟踪

### 完成情况统计
- **阶段一**: 0/40小时 (0%)
- **阶段二**: 0/60小时 (0%)
- **阶段三**: 0/48小时 (0%)
- **阶段四**: 0/80小时 (0%)
- **阶段五**: 0/72小时 (0%)
- **阶段六**: 0/40小时 (0%)
- **文档部署**: 0/20小时 (0%)

### 里程碑节点
- [ ] **M1**: 基础架构完成 (第2周)
- [ ] **M2**: 核心组件迁移完成 (第5周)
- [ ] **M3**: 认证路由系统完成 (第7.5周)
- [ ] **M4**: 主要页面功能完成 (第11.5周)
- [ ] **M5**: 高级功能迁移完成 (第15周)
- [ ] **M6**: 测试优化完成 (第17周)
- [ ] **M7**: 项目交付 (第18周)

## ⚠️ 风险提醒

### 高优先级风险
1. **Element Plus组件替换** - 可能需要额外时间适配样式
2. **复杂路由守卫** - 业务逻辑复杂，需要仔细测试
3. **状态管理迁移** - 可能影响多个页面功能

### 应对策略
- 每周进行风险评估
- 提前准备备选方案
- 保持与原项目的功能对比测试

---

*此任务清单将根据实际进展动态调整*
