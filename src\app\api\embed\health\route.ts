/**
 * 嵌入系统健康检查API
 */

import { NextResponse } from 'next/server'
import { domainValidator } from '@/lib/embed/domain-validator'

// 配置路由段 - 强制动态渲染以获取实时健康状态
export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      embed: {
        allowedDomains: domainValidator.getAllowedDomains(),
        securityEnabled: true,
        communicationEnabled: true
      },
      services: {
        tokenValidation: 'operational',
        domainValidation: 'operational',
        middleware: 'operational'
      }
    }

    return NextResponse.json(healthData)
  } catch (error) {
    console.error('Health check error:', error)
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed'
      },
      { status: 500 }
    )
  }
}
