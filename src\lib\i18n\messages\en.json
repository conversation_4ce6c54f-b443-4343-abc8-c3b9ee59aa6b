{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "close": "Close", "search": "Search", "filter": "Filter", "refresh": "Refresh", "retry": "Retry"}, "auth": {"login": {"title": "Welcome Back", "subtitle": "Sign in to your Vibe Coding account", "username": "Username or Email", "usernamePlaceholder": "Enter username or email", "password": "Password", "passwordPlaceholder": "Enter password", "forgotPassword": "Forgot password?", "loginButton": "<PERSON><PERSON>", "loggingIn": "Logging in...", "orLoginWith": "Or login with", "loginWithApple": "Login with Apple", "loginWithGoogle": "Login with Google", "loginWithFacebook": "Login with Facebook", "noAccount": "Don't have an account?", "registerNow": "Register now", "brandTitle": "Vibe Coding Refactory", "brandSubtitle": "Enterprise AI Application Frontend Architecture", "feature1": "✨ Complete Vue3 to Next.js Migration", "feature2": "🚀 Modern Component System", "feature3": "🔐 Complete Authentication & Permission Management", "feature4": "🎨 Enterprise UI Design System", "termsText": "By continuing, you agree to our", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy", "period": "."}, "validation": {"usernameRequired": "Please enter username or email", "passwordRequired": "Please enter password"}, "messages": {"loginSuccess": "Login successful", "welcomeBack": "Welcome back, {username}!", "loginFailed": "<PERSON><PERSON> failed", "loginError": "An error occurred during login", "featureInDevelopment": "Feature in Development", "forgotPasswordInDevelopment": "Forgot password feature is under development", "registerInDevelopment": "Registration feature is under development"}, "legacy": {"login": "<PERSON><PERSON>", "logout": "Logout", "pleaseLogin": "Please login first", "invalidCredentials": "Invalid username or password", "accountLocked": "Account has been locked, please contact administrator", "userDeleted": "This user has been deleted, please contact administrator"}}, "navigation": {"chat": "Cha<PERSON>", "uiGallery": "UI Gallery", "home": "Home", "dashboard": "Dashboard", "settings": "Settings"}, "chat": {"title": "Chat Interface", "subtitle": "Coming soon...", "welcome": "Welcome to SpecificAI!", "description": "Your AI-powered assistant is ready to help.", "placeholder": "Type a message...", "send": "Send", "clear": "Clear chat", "typing": "Typing..."}, "ui": {"gallery": {"title": "UI Component Gallery", "description": "Test and debug all UI components for style verification", "buttons": "Button Components", "inputs": "Input Components", "cards": "Card Components", "markdown": "Markdown Renderer Demo"}}, "uiGallery": {"title": "UI Component Gallery", "subtitle": "Showcase of available components in the project", "radixComponents": "Radix UI Components", "customComponents": "Custom Components", "componentAvailable": "Component Available"}, "errors": {"unauthorized": "Unauthorized, please login again", "noPermission": "Insufficient permissions to access", "userNotLogin": "User not logged in, please login first", "userDeleted": "This user has been deleted, please contact administrator", "networkError": "Network connection failed, please check network settings", "serverError": "Internal server error", "notFound": "Requested resource not found", "badRequest": "Bad request parameters", "forbidden": "Insufficient permissions", "timeout": "Request timeout", "unknown": "Unknown error"}, "language": {"switch": "Switch Language", "chinese": "中文", "english": "English", "japanese": "日本語", "current": "Current Language"}, "user": {"profile": "Profile", "account": "Account", "preferences": "Preferences", "avatar": "Avatar", "name": "Name", "email": "Email", "role": "Role", "lastLogin": "Last Login", "memberSince": "Member Since"}, "embed": {"initializing": "Initializing...", "loading": "Loading page"}}