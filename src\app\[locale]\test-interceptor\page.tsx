/**
 * HTTP请求系统测试页面
 * 用于验证新的useHttp系统的功能
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useHttp, useGet, usePost, usePut, useDelete, useHttpPresets } from '@/hooks/useHttp'
import { useAuthHttp } from '@/hooks/useAuthHttp'

interface RequestLog {
  id: number
  url: string
  method: string
  headers: Record<string, string>
  timestamp: string
  status: 'pending' | 'success' | 'error'
  response?: any
  error?: string
}

export default function TestHttpPage() {
  const t = useTranslations('test')
  const [logs, setLogs] = useState<RequestLog[]>([])

  // 初始化各种HTTP hooks
  const http = useHttp()
  const getCurrentUser = useAuthHttp.useGetCurrentUser()
  const loginHook = useAuthHttp.useLogin()

  // 测试用的各种请求hooks
  const testGet = useHttpPresets.silent.get('/api/user/get_current')
  const testPost = useHttpPresets.save.post('/api/test')
  const testAuthPost = useHttpPresets.auth.post('/api/user/login')

  // 添加请求日志
  const addLog = (log: Omit<RequestLog, 'id' | 'timestamp'>) => {
    const newLog: RequestLog = {
      ...log,
      id: Date.now(),
      timestamp: new Date().toLocaleTimeString()
    }
    setLogs(prev => [newLog, ...prev.slice(0, 9)]) // 保留最新10条
    return newLog.id
  }

  // 更新请求日志
  const updateLog = (id: number, updates: Partial<RequestLog>) => {
    setLogs(prev => prev.map(log => 
      log.id === id ? { ...log, ...updates } : log
    ))
  }

  // 测试API请求
  const testApiRequest = async (url: string, options: RequestInit = {}) => {
    const logId = addLog({
      url,
      method: options.method || 'GET',
      headers: options.headers as Record<string, string> || {},
      status: 'pending'
    })

    try {
      const response = await fetch(url, options)
      const data = await response.json()
      
      updateLog(logId, {
        status: 'success',
        response: data
      })
    } catch (error) {
      updateLog(logId, {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  // 测试用例
  const testCases = [
    {
      name: '普通API请求',
      description: '应该自动添加language和authorization头',
      action: () => testApiRequest('/api/user/get_current')
    },
    {
      name: '跳过认证的请求',
      description: '应该只添加language头，不添加authorization头',
      action: () => testApiRequest('/api/user/login', {
        method: 'POST',
        headers: { 'x-skip-auth': 'true' },
        body: JSON.stringify({ username: 'test', password: 'test' })
      })
    },
    {
      name: '自定义请求头',
      description: '应该保留自定义头，同时添加拦截器头',
      action: () => testApiRequest('/api/test', {
        headers: {
          'Custom-Header': 'custom-value',
          'X-Test': 'test-value'
        }
      })
    },
    {
      name: '非API请求',
      description: '应该不被拦截，直接通过',
      action: () => testApiRequest('/static/test.json')
    }
  ]

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">🔧 Fetch拦截器测试</h1>
        <p className="text-gray-600">验证全局fetch拦截器的功能</p>
      </div>

      {/* 拦截器状态 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            📊 拦截器状态
            <Badge variant={interceptorStatus?.installed ? 'default' : 'destructive'}>
              {interceptorStatus?.installed ? '已安装' : '未安装'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {interceptorStatus && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <div className="font-medium">安装状态</div>
                <div className={interceptorStatus.installed ? 'text-green-600' : 'text-red-600'}>
                  {interceptorStatus.installed ? '✅ 已安装' : '❌ 未安装'}
                </div>
              </div>
              <div>
                <div className="font-medium">当前语言</div>
                <div className="text-blue-600">{interceptorStatus.currentLanguage}</div>
              </div>
              <div>
                <div className="font-medium">Token状态</div>
                <div className={interceptorStatus.hasToken ? 'text-green-600' : 'text-orange-600'}>
                  {interceptorStatus.hasToken ? '✅ 有Token' : '⚠️ 无Token'}
                </div>
              </div>
              <div>
                <div className="font-medium">原始Fetch</div>
                <div className={interceptorStatus.originalFetchExists ? 'text-green-600' : 'text-red-600'}>
                  {interceptorStatus.originalFetchExists ? '✅ 存在' : '❌ 不存在'}
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 测试用例 */}
      <Card>
        <CardHeader>
          <CardTitle>🧪 测试用例</CardTitle>
          <CardDescription>点击按钮执行不同的测试场景</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {testCases.map((testCase, index) => (
              <div key={index} className="border rounded-lg p-4">
                <h3 className="font-medium mb-2">{testCase.name}</h3>
                <p className="text-sm text-gray-600 mb-3">{testCase.description}</p>
                <Button 
                  onClick={testCase.action}
                  variant="outline"
                  size="sm"
                  className="w-full"
                >
                  执行测试
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 请求日志 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            📝 请求日志
            <Button 
              onClick={() => setLogs([])}
              variant="outline"
              size="sm"
            >
              清空日志
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {logs.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              暂无请求日志，点击上方测试按钮开始测试
            </div>
          ) : (
            <div className="space-y-3">
              {logs.map((log) => (
                <div key={log.id} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{log.method}</Badge>
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                        {log.url}
                      </code>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge 
                        variant={
                          log.status === 'success' ? 'default' : 
                          log.status === 'error' ? 'destructive' : 
                          'secondary'
                        }
                      >
                        {log.status}
                      </Badge>
                      <span className="text-xs text-gray-500">{log.timestamp}</span>
                    </div>
                  </div>
                  
                  {/* 请求头 */}
                  <div className="text-sm">
                    <div className="font-medium mb-1">请求头:</div>
                    <div className="bg-gray-50 p-2 rounded text-xs font-mono">
                      {Object.keys(log.headers).length > 0 ? (
                        Object.entries(log.headers).map(([key, value]) => (
                          <div key={key}>
                            <span className="text-blue-600">{key}</span>: {value}
                          </div>
                        ))
                      ) : (
                        <span className="text-gray-500">无自定义请求头</span>
                      )}
                    </div>
                  </div>

                  {/* 响应或错误 */}
                  {log.response && (
                    <div className="text-sm mt-2">
                      <div className="font-medium mb-1">响应:</div>
                      <div className="bg-green-50 p-2 rounded text-xs font-mono">
                        {JSON.stringify(log.response, null, 2)}
                      </div>
                    </div>
                  )}
                  
                  {log.error && (
                    <div className="text-sm mt-2">
                      <div className="font-medium mb-1">错误:</div>
                      <div className="bg-red-50 p-2 rounded text-xs font-mono text-red-600">
                        {log.error}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
