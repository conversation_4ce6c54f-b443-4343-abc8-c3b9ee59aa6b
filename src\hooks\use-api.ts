/**
 * 统一的API请求Hook
 * 基于react-use的useAsyncFn，集成项目的响应拦截器和错误处理
 */

import { useAsyncFn, useAsync, useAsyncRetry } from 'react-use'
import { useCallback, useMemo, useState } from 'react'
// import { tokenManager } from '@/lib/token' // 不再需要，全局拦截器会处理
import { responseInterceptor, type ApiResponse } from '@/lib/api-interceptor'
import type { AsyncState } from 'react-use/lib/useAsyncFn'

// API基础配置
const API_BASE_URL = ''

// 请求配置类型
export interface ApiRequestConfig extends RequestInit {
  skipAuth?: boolean // 是否跳过认证
  showError?: boolean // 是否显示错误提示
  showSuccess?: boolean // 是否显示成功提示
  successMessage?: string // 自定义成功消息
  errorMessage?: string // 自定义错误消息
}

// 统一的请求函数
async function apiRequest<T>(
  endpoint: string,
  config: ApiRequestConfig = {}
): Promise<ApiResponse<T>> {
  const {
    skipAuth = false,
    showError,
    showSuccess,
    successMessage,
    errorMessage,
    ...requestInit
  } = config

  const url = `${API_BASE_URL}${endpoint}`

  // 🔧 简化请求头处理 - 全局fetch拦截器会自动添加language和authorization头
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...requestInit.headers as Record<string, string>,
  }

  // 🔧 只在明确跳过认证时移除authorization头
  // 全局拦截器会自动添加，这里只需要在skipAuth时移除
  if (skipAuth) {
    // 标记跳过认证，拦截器会检查这个标记
    headers['x-skip-auth'] = 'true'
  }

  const finalConfig: RequestInit = {
    ...requestInit,
    headers,
  }

  try {
    const response = await fetch(url, finalConfig)
    const data = await response.json()

    // 如果HTTP状态码不是2xx，但API返回了结构化错误信息，仍然返回数据
    if (!response.ok && !data.hasOwnProperty('success')) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return data
  } catch (error: any) {
    console.error('API request failed:', error)
    throw error
  }
}

/**
 * 基础API Hook - 使用useAsyncFn
 * 适用于需要手动触发的API调用
 */
export function useApi<T>(
  endpoint: string,
  config: ApiRequestConfig = {}
): [AsyncState<T>, (newConfig?: Partial<ApiRequestConfig>) => Promise<T>] {
  const {
    showError = true,
    showSuccess = false,
    successMessage,
    errorMessage,
  } = config

  const [state, execute] = useAsyncFn(async (overrideConfig: Partial<ApiRequestConfig> = {}) => {
    const finalConfig = { ...config, ...overrideConfig }
    
    const response = await apiRequest<T>(endpoint, finalConfig)
    
    return responseInterceptor(
      Promise.resolve(response),
      {
        showError: finalConfig.showError ?? showError,
        showSuccess: finalConfig.showSuccess ?? showSuccess,
        ...(finalConfig.successMessage ?? successMessage ? { successMessage: finalConfig.successMessage ?? successMessage } : {}),
        ...(finalConfig.errorMessage ?? errorMessage ? { errorMessage: finalConfig.errorMessage ?? errorMessage } : {}),
      }
    )
  }, [endpoint])

  return [state, execute]
}

/**
 * 自动执行的API Hook - 使用useAsync
 * 适用于组件加载时自动执行的API调用
 */
export function useApiAuto<T>(
  endpoint: string,
  config: ApiRequestConfig = {},
  deps: React.DependencyList = []
): AsyncState<T> {
  const {
    showError = true,
    showSuccess = false,
    successMessage,
    errorMessage,
  } = config

  return useAsync(async () => {
    const response = await apiRequest<T>(endpoint, config)
    
    return responseInterceptor(
      Promise.resolve(response),
      {
        showError,
        showSuccess,
        ...(successMessage ? { successMessage } : {}),
        ...(errorMessage ? { errorMessage } : {}),
      }
    )
  }, [endpoint, ...deps])
}

/**
 * 带重试功能的API Hook - 使用useAsyncRetry
 * 适用于需要重试机制的API调用
 */
export function useApiRetry<T>(
  endpoint: string,
  config: ApiRequestConfig = {},
  deps: React.DependencyList = []
) {
  const {
    showError = true,
    showSuccess = false,
    successMessage,
    errorMessage,
  } = config

  return useAsyncRetry(async () => {
    const response = await apiRequest<T>(endpoint, config)

    return responseInterceptor(
      Promise.resolve(response),
      {
        showError,
        showSuccess,
        ...(successMessage ? { successMessage } : {}),
        ...(errorMessage ? { errorMessage } : {}),
      }
    )
  }, [endpoint, ...deps])
}

/**
 * RESTful API Hook集合
 * 提供标准的CRUD操作
 */
export function useRestApi<T>(baseEndpoint: string) {
  // GET 列表
  const [listState, fetchList] = useApi<T[]>(baseEndpoint, {
    showError: true,
    showSuccess: false,
  })

  // POST 创建
  const [createState, createInternal] = useApi<T>(baseEndpoint, {
    method: 'POST',
    showError: true,
    showSuccess: true,
    successMessage: '创建成功',
  })

  // 其他操作使用直接的API调用
  const fetchOne = useCallback(async (id: string) => {
    const response = await apiRequest<T>(`${baseEndpoint}/${id}`, {
      showError: true,
      showSuccess: false,
    })
    return responseInterceptor(Promise.resolve(response), {
      showError: true,
      showSuccess: false,
    })
  }, [baseEndpoint])

  const update = useCallback(async (id: string, data: Partial<T>) => {
    const response = await apiRequest<T>(`${baseEndpoint}/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
      showError: true,
      showSuccess: true,
    })
    return responseInterceptor(Promise.resolve(response), {
      showError: true,
      showSuccess: true,
      successMessage: '更新成功',
    })
  }, [baseEndpoint])

  const remove = useCallback(async (id: string) => {
    const response = await apiRequest<void>(`${baseEndpoint}/${id}`, {
      method: 'DELETE',
      showError: true,
      showSuccess: true,
    })
    return responseInterceptor(Promise.resolve(response), {
      showError: true,
      showSuccess: true,
      successMessage: '删除成功',
    })
  }, [baseEndpoint])

  return {
    // 状态
    listState,
    createState,

    // 操作函数
    fetchList: useCallback(() => fetchList(), [fetchList]),
    fetchOne,
    create: useCallback((data: Partial<T>) =>
      createInternal({ body: JSON.stringify(data) }), [createInternal]
    ),
    update,
    remove,

    // 便捷状态
    isLoading: listState.loading || createState.loading,
    hasError: !!(listState.error || createState.error),
  }
}

/**
 * 预配置的API Hooks
 * 使用项目中常用的拦截器配置
 */

// 静默请求（不显示任何提示）
export function useApiSilent<T>(endpoint: string, config: Omit<ApiRequestConfig, 'showError' | 'showSuccess'> = {}) {
  return useApi<T>(endpoint, { ...config, showError: false, showSuccess: false })
}

// 只显示错误
export function useApiErrorOnly<T>(endpoint: string, config: Omit<ApiRequestConfig, 'showError' | 'showSuccess'> = {}) {
  return useApi<T>(endpoint, { ...config, showError: true, showSuccess: false })
}

// 显示成功和错误
export function useApiFull<T>(endpoint: string, config: Omit<ApiRequestConfig, 'showError' | 'showSuccess'> = {}) {
  return useApi<T>(endpoint, { ...config, showError: true, showSuccess: true })
}

// 认证相关
export function useApiAuth<T>(endpoint: string, config: Omit<ApiRequestConfig, 'showError' | 'showSuccess' | 'successMessage' | 'errorMessage'> = {}) {
  return useApi<T>(endpoint, {
    ...config,
    showError: true,
    showSuccess: true,
    successMessage: '操作成功',
    errorMessage: '操作失败，请重试'
  })
}

// 数据保存
export function useApiSave<T>(endpoint: string, config: Omit<ApiRequestConfig, 'showError' | 'showSuccess' | 'successMessage' | 'errorMessage'> = {}) {
  return useApi<T>(endpoint, {
    ...config,
    showError: true,
    showSuccess: true,
    successMessage: '保存成功',
    errorMessage: '保存失败，请重试'
  })
}

// 数据删除
export function useApiDelete<T>(endpoint: string, config: Omit<ApiRequestConfig, 'showError' | 'showSuccess' | 'successMessage' | 'errorMessage'> = {}) {
  return useApi<T>(endpoint, {
    ...config,
    showError: true,
    showSuccess: true,
    successMessage: '删除成功',
    errorMessage: '删除失败，请重试'
  })
}

// 为了向后兼容，保留useApiPresets对象
export const useApiPresets = {
  silent: useApiSilent,
  errorOnly: useApiErrorOnly,
  full: useApiFull,
  auth: useApiAuth,
  save: useApiSave,
  delete: useApiDelete,
}

/**
 * 分页API Hook
 */
export interface PaginationParams {
  page?: number
  pageSize?: number
  search?: string
  [key: string]: any
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export function usePaginatedApi<T>(
  baseEndpoint: string,
  initialParams: PaginationParams = {}
) {
  const [params, setParams] = useState<PaginationParams>({
    page: 1,
    pageSize: 10,
    ...initialParams
  })

  const endpoint = useMemo(() => {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value))
      }
    })
    return `${baseEndpoint}?${searchParams.toString()}`
  }, [baseEndpoint, params])

  const state = useApiAuto<PaginatedResponse<T>>(endpoint, {
    showError: true,
    showSuccess: false,
  }, [endpoint])

  const updateParams = useCallback((newParams: Partial<PaginationParams>) => {
    setParams(prev => ({ ...prev, ...newParams }))
  }, [])

  const nextPage = useCallback(() => {
    if (state.value && params.page! < state.value.totalPages) {
      updateParams({ page: params.page! + 1 })
    }
  }, [state.value, params.page, updateParams])

  const prevPage = useCallback(() => {
    if (params.page! > 1) {
      updateParams({ page: params.page! - 1 })
    }
  }, [params.page, updateParams])

  const goToPage = useCallback((page: number) => {
    updateParams({ page })
  }, [updateParams])

  return {
    ...state,
    params,
    updateParams,
    nextPage,
    prevPage,
    goToPage,
    hasNextPage: state.value ? params.page! < state.value.totalPages : false,
    hasPrevPage: params.page! > 1,
  }
}
