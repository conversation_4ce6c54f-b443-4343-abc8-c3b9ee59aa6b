# 用户认证系统重构详细设计

## 📋 模块概述

### 重构目标
将Vue3 + Pinia的用户认证系统完整迁移到Next.js + Zustand，保持所有认证功能、权限控制和用户体验。

### 工作量估算
- **总工时**: 16小时
- **复杂度**: 高
- **风险等级**: 中等

## 🔍 原系统深度分析

### 原系统架构
```typescript
// src/stores/user.ts (400+行)
export const useUserStore = defineStore('user', () => {
  // 状态定义
  const userInfo = ref<User>()
  const menuConfig = ref<MenuConfigItem['menu']>([])
  const featureConfig = ref<MenuConfigItem['feature']>([])
  const tourConfig = ref<TourConfigItem[]>([])
  
  // 计算属性
  const avatarName = computed(() => userInfo.value?.user_name.slice(0, 2))
  const userId = computed(() => userInfo.value?.id)
  const userRole = computed(() => USER_TYPE_MAP[userInfo.value?.user_type])
  const isTokensValid = computed(() => {
    if (!companyConfig.value) return false
    return companyConfig.value?.tokens === 'unlimited' || companyConfig.value?.tokens > 0
  })
  
  // 核心方法
  const validateToken = async () => { /* 复杂的token验证逻辑 */ }
  const login = async (type: LoginType, params: LoginParams) => { /* 登录逻辑 */ }
  const logout = () => { /* 登出逻辑 */ }
  const checkAccess = (accessParams: AccessParams) => { /* 权限检查 */ }
  const hasPermission = (permission: string) => { /* 权限验证 */ }
  const hasFeatureAccess = (feature: string) => { /* 功能权限 */ }
})
```

### 核心功能分析

#### 1. 用户状态管理
```typescript
// 用户信息结构
interface User {
  id: number
  user_name: string
  user_type: UserType
  company: {
    id: number
    config: {
      tokens: number | 'unlimited'
      // 其他配置
    }
  }
  step: number  // 问卷步骤
  language: 'chinese' | 'english' | 'japanese'
  tour_config: TourConfigItem[]
}

// 权限配置
interface MenuConfigItem {
  menu: { id: string; status: boolean }[]
  feature: { id: string; status: boolean }[]
}
```

#### 2. 认证流程
```typescript
// 登录流程伪代码
async function loginFlow(credentials: LoginParams) {
  1. 调用 /user/login API
  2. 获取 LoginResult { token, user }
  3. 存储 token 到 localStorage
  4. 设置用户信息到 store
  5. 加载用户权限配置
  6. 根据用户状态决定跳转路径
     - 如果 step !== 4: 跳转到问卷页面
     - 否则: 跳转到主页面
}

// Token验证流程
async function validateTokenFlow() {
  1. 从 localStorage 获取 token
  2. 如果没有 token: 返回 false
  3. 调用 /user/account API 验证 token
  4. 如果验证成功: 更新用户信息，返回 true
  5. 如果验证失败: 清除 token，返回 false
}
```

#### 3. 权限检查系统
```typescript
// 权限检查伪代码
function checkAccessFlow(accessParams: AccessParams) {
  const { features, roles, permissions, rightSchemeType, fallback } = accessParams
  
  // 检查功能权限
  if (features?.length) {
    const hasFeatureAccess = features.some(feature => 
      featureConfig.value.find(f => f.id === feature && f.status)
    )
    if (!hasFeatureAccess) return false
  }
  
  // 检查角色权限
  if (roles?.length) {
    const userRoles = userRoleStore.data?.map(r => r.roleCode) || []
    const hasRoleAccess = roles.some(role => userRoles.includes(role))
    if (!hasRoleAccess) return false
  }
  
  // 检查具体权限
  if (permissions?.length) {
    const userPermissions = userPermissionStore.data?.map(p => p.code) || []
    const hasPermissionAccess = permissions.some(permission => 
      userPermissions.includes(permission)
    )
    if (!hasPermissionAccess) return false
  }
  
  return true
}
```

## 🎯 目标架构设计

### Zustand状态结构
```typescript
// packages/stores/src/auth.ts
interface AuthState {
  // 基础状态
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  
  // 权限配置
  menuConfig: MenuConfig[]
  featureConfig: FeatureConfig[]
  userRoles: UserRole[]
  userPermissions: UserPermission[]
  
  // 计算属性 (通过 selector 实现)
  // avatarName, userId, userRole, isTokensValid 等
  
  // 异步方法
  validateToken: () => Promise<boolean>
  login: (type: LoginType, params: LoginParams) => Promise<void>
  logout: () => void
  loadAccount: () => Promise<void>
  checkAccess: (accessParams: AccessParams) => boolean
  
  // 权限检查方法
  hasPermission: (permission: string) => boolean
  hasFeatureAccess: (feature: string) => boolean
  hasRoleAccess: (role: string) => boolean
}

// 创建 store
export const useAuthStore = create<AuthState>((set, get) => ({
  // 初始状态
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  menuConfig: [],
  featureConfig: [],
  userRoles: [],
  userPermissions: [],
  
  // 实现方法...
}))
```

### 权限 Hooks 设计
```typescript
// packages/hooks/src/usePermissions.ts
export function usePermissions() {
  const { user, menuConfig, featureConfig, userRoles, userPermissions } = useAuthStore()
  
  return useMemo(() => ({
    // 基础权限检查
    hasPermission: (permission: string) => 
      userPermissions.some(p => p.code === permission),
    
    hasFeatureAccess: (feature: string) =>
      featureConfig.some(f => f.id === feature && f.status),
    
    hasRoleAccess: (role: string) =>
      userRoles.some(r => r.roleCode === role),
    
    // 业务权限 (保持与原系统一致)
    canAccessNotifyFeature: featureConfig.some(f => f.id === 'notify' && f.status),
    canAccessNormalFeature: featureConfig.some(f => f.id === 'normal' && f.status),
    canAccessBindingFeature: featureConfig.some(f => 
      (f.id === 'binding' || f.id === 'strategic') && f.status
    ),
    canAccessBusinessOpportunityFeature: featureConfig.some(f => 
      f.id === 'binding' && f.status
    ),
    canAccessStrategicFeature: featureConfig.some(f => 
      f.id === 'strategic' && f.status
    ),
    canAccessSpecialAnalysisFeature: featureConfig.some(f => 
      (f.id === 'binding' || f.id === 'strategic') && f.status
    ),
  }), [user, menuConfig, featureConfig, userRoles, userPermissions])
}
```

## 🔧 分步实施计划

### 步骤1: Zustand Store基础结构 (4小时)

#### 1.1 创建基础Store (2小时)
```typescript
// packages/stores/src/auth.ts
import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

interface AuthState {
  // ... 状态定义
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      immer((set, get) => ({
        // 初始状态
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        menuConfig: [],
        featureConfig: [],
        userRoles: [],
        userPermissions: [],
        
        // 基础方法实现
        setUser: (user: User) => set(state => {
          state.user = user
          state.isAuthenticated = true
        }),
        
        clearAuth: () => set(state => {
          state.user = null
          state.isAuthenticated = false
          state.menuConfig = []
          state.featureConfig = []
          state.userRoles = []
          state.userPermissions = []
        }),
        
        setLoading: (loading: boolean) => set(state => {
          state.isLoading = loading
        }),
        
        setError: (error: string | null) => set(state => {
          state.error = error
        }),
      })),
      {
        name: 'auth-storage',
        partialize: (state) => ({ 
          user: state.user,
          isAuthenticated: state.isAuthenticated 
        }),
      }
    )
  )
)
```

#### 1.2 实现计算属性选择器 (2小时)
```typescript
// packages/stores/src/auth-selectors.ts
export const authSelectors = {
  avatarName: (state: AuthState) => state.user?.user_name.slice(0, 2) || '',
  userId: (state: AuthState) => state.user?.id,
  userRole: (state: AuthState) => state.user?.user_type ? USER_TYPE_MAP[state.user.user_type] : null,
  isTokensValid: (state: AuthState) => {
    const config = state.user?.company?.config
    if (!config) return false
    return config.tokens === 'unlimited' || config.tokens > 0
  },
  companyId: (state: AuthState) => state.user?.company?.id,
}

// 使用示例
export function useAuthSelectors() {
  const avatarName = useAuthStore(authSelectors.avatarName)
  const userId = useAuthStore(authSelectors.userId)
  const userRole = useAuthStore(authSelectors.userRole)
  const isTokensValid = useAuthStore(authSelectors.isTokensValid)
  
  return { avatarName, userId, userRole, isTokensValid }
}
```

### 步骤2: 认证逻辑迁移 (6小时)

#### 2.1 Token管理系统 (2小时)
```typescript
// packages/utils/src/token.ts
const TOKEN_KEY = 'auth-token'

export const tokenManager = {
  getToken: (): string | null => {
    if (typeof window === 'undefined') return null
    return localStorage.getItem(TOKEN_KEY)
  },
  
  setToken: (token: string): void => {
    if (typeof window === 'undefined') return
    localStorage.setItem(TOKEN_KEY, token)
  },
  
  removeToken: (): void => {
    if (typeof window === 'undefined') return
    localStorage.removeItem(TOKEN_KEY)
  },
  
  isTokenExpired: (token: string): boolean => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return Date.now() >= payload.exp * 1000
    } catch {
      return true
    }
  }
}
```

#### 2.2 登录逻辑实现 (2小时)
```typescript
// 在 useAuthStore 中添加登录方法
login: async (type: LoginType, params: LoginParams) => {
  const { setLoading, setError, setUser } = get()
  
  setLoading(true)
  setError(null)
  
  try {
    // 调用登录API
    const response = await authAPI.login(type, params)
    
    if (response.success) {
      // 存储token
      tokenManager.setToken(response.token)
      
      // 设置用户信息
      setUser(response.user)
      
      // 加载用户权限配置
      await get().loadUserPermissions()
      
      // 根据用户状态决定跳转
      const isToStep = response.user.step !== 4
      return { success: true, isToStep }
    } else {
      throw new Error(response.message || 'Login failed')
    }
  } catch (error) {
    setError(error.message)
    throw error
  } finally {
    setLoading(false)
  }
},
```

#### 2.3 Token验证逻辑 (2小时)
```typescript
// Token验证方法
validateToken: async () => {
  const { setLoading, setUser, clearAuth } = get()
  
  const token = tokenManager.getToken()
  if (!token) {
    clearAuth()
    return false
  }
  
  // 检查token是否过期
  if (tokenManager.isTokenExpired(token)) {
    clearAuth()
    return false
  }
  
  setLoading(true)
  
  try {
    // 调用验证API
    const response = await authAPI.validateToken()
    
    if (response.success) {
      setUser(response.user)
      await get().loadUserPermissions()
      return true
    } else {
      clearAuth()
      return false
    }
  } catch (error) {
    clearAuth()
    return false
  } finally {
    setLoading(false)
  }
},
```

### 步骤3: 权限系统实现 (4小时)

#### 3.1 权限数据加载 (2小时)
```typescript
// 加载用户权限配置
loadUserPermissions: async () => {
  const { user } = get()
  if (!user) return
  
  try {
    const [menuConfig, featureConfig, userRoles, userPermissions] = await Promise.all([
      authAPI.getMenuConfig(),
      authAPI.getFeatureConfig(), 
      authAPI.getUserRoles(),
      authAPI.getUserPermissions()
    ])
    
    set(state => {
      state.menuConfig = menuConfig
      state.featureConfig = featureConfig
      state.userRoles = userRoles
      state.userPermissions = userPermissions
    })
  } catch (error) {
    console.error('Failed to load user permissions:', error)
  }
},
```

#### 3.2 权限检查方法 (2小时)
```typescript
// 权限检查核心方法
checkAccess: (accessParams: AccessParams) => {
  const { featureConfig, userRoles, userPermissions } = get()
  const { features, roles, permissions, rightSchemeType, fallback } = accessParams
  
  // 检查功能权限
  if (features?.length) {
    const hasFeatureAccess = features.some(feature => 
      featureConfig.find(f => f.id === feature && f.status)
    )
    if (!hasFeatureAccess) {
      // 检查是否有fallback选项
      if (fallback?.length) {
        return fallback.some(fb => 
          featureConfig.find(f => f.id === fb && f.status)
        )
      }
      return false
    }
  }
  
  // 检查角色权限
  if (roles?.length) {
    const userRoleCodes = userRoles.map(r => r.roleCode)
    const hasRoleAccess = roles.some(role => userRoleCodes.includes(role))
    if (!hasRoleAccess) return false
  }
  
  // 检查具体权限
  if (permissions?.length) {
    const userPermissionCodes = userPermissions.map(p => p.code)
    const hasPermissionAccess = permissions.some(permission => 
      userPermissionCodes.includes(permission)
    )
    if (!hasPermissionAccess) return false
  }
  
  // 检查权限方案类型
  if (rightSchemeType?.length) {
    // 实现权限方案类型检查逻辑
    // 这部分需要根据具体业务逻辑实现
  }
  
  return true
},

// 具体权限检查方法
hasPermission: (permission: string) => {
  const { userPermissions } = get()
  return userPermissions.some(p => p.code === permission)
},

hasFeatureAccess: (feature: string) => {
  const { featureConfig } = get()
  return featureConfig.some(f => f.id === feature && f.status)
},

hasRoleAccess: (role: string) => {
  const { userRoles } = get()
  return userRoles.some(r => r.roleCode === role)
},
```

### 步骤4: 认证组件迁移 (2小时)

#### 4.1 登录表单组件 (1小时)
```typescript
// components/auth/LoginForm.tsx
export function LoginForm() {
  const [form, setForm] = useState({ username: '', password: '' })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const { login, isLoading, error } = useAuthStore()
  const { t } = useTranslations()
  
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()
    
    // 表单验证
    const newErrors: Record<string, string> = {}
    if (!form.username.trim()) {
      newErrors.username = t('login.usernameRequired')
    }
    if (!form.password.trim()) {
      newErrors.password = t('login.passwordRequired')
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }
    
    try {
      const result = await login('password', form)
      if (result.success) {
        // 根据用户状态跳转
        if (result.isToStep) {
          router.push('/poll')
        } else {
          router.push('/workspace')
        }
      }
    } catch (error) {
      // 错误已经在store中处理
    }
  }
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <Input
        label={t('login.username')}
        value={form.username}
        onChange={(e) => setForm({...form, username: e.target.value})}
        error={errors.username}
        required
      />
      <Input
        label={t('login.password')}
        type="password"
        value={form.password}
        onChange={(e) => setForm({...form, password: e.target.value})}
        error={errors.password}
        required
      />
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      <Button type="submit" disabled={isLoading} className="w-full">
        {isLoading ? <Spinner className="mr-2" /> : null}
        {t('login.submit')}
      </Button>
    </form>
  )
}
```

#### 4.2 认证状态组件 (1小时)
```typescript
// components/auth/AuthProvider.tsx
export function AuthProvider({ children }: { children: ReactNode }) {
  const { validateToken, isAuthenticated } = useAuthStore()
  const [isInitialized, setIsInitialized] = useState(false)
  
  useEffect(() => {
    const initAuth = async () => {
      await validateToken()
      setIsInitialized(true)
    }
    
    initAuth()
  }, [validateToken])
  
  if (!isInitialized) {
    return (
      <div className="h-screen flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    )
  }
  
  return <>{children}</>
}

// 受保护的路由组件
export function ProtectedRoute({ children }: { children: ReactNode }) {
  const { isAuthenticated } = useAuthStore()
  const router = useRouter()
  
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, router])
  
  if (!isAuthenticated) {
    return null
  }
  
  return <>{children}</>
}
```

## ✅ 验收标准

### 功能验收
- [ ] 用户登录功能正常
- [ ] Token验证和自动登录
- [ ] 权限检查功能完整
- [ ] 登出功能正常
- [ ] 用户状态持久化

### 性能验收
- [ ] 登录响应时间 < 2秒
- [ ] 权限检查响应时间 < 100ms
- [ ] 状态更新无明显延迟

### 兼容性验收
- [ ] 与原系统权限逻辑100%兼容
- [ ] 支持所有原有的权限配置
- [ ] API接口调用方式保持一致

## 🧪 测试计划

### 单元测试
- [ ] Zustand store状态管理测试
- [ ] 权限检查逻辑测试
- [ ] Token管理功能测试

### 集成测试
- [ ] 登录流程端到端测试
- [ ] 权限控制集成测试
- [ ] 路由守卫集成测试

### 用户体验测试
- [ ] 登录表单交互测试
- [ ] 错误处理用户体验测试
- [ ] 加载状态展示测试
