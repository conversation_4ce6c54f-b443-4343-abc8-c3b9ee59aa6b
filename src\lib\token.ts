/**
 * Token管理工具
 * 处理JWT token的存储、获取、验证和清除
 * 支持cookies存储和环境隔离
 */

// Cookie工具函数
const cookieUtils = {
  /**
   * 设置Cookie
   */
  setCookie(name: string, value: string, days: number = 7): void {
    if (typeof document === 'undefined') return

    const expires = new Date()
    expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000)

    // 根据环境设置不同的cookie属性
    const isProduction = process.env.NODE_ENV === 'production'
    const domain = isProduction ? '.goglobalsp.com' : 'localhost'
    const secure = isProduction ? '; Secure' : ''
    const sameSite = isProduction ? '; SameSite=Strict' : '; SameSite=Lax'

    document.cookie = `${name}=${value}; expires=${expires.toUTCString()}; path=/; domain=${domain}${secure}${sameSite}`
  },

  /**
   * 获取Cookie
   */
  getCookie(name: string): string | null {
    if (typeof document === 'undefined') return null

    const nameEQ = name + '='
    const ca = document.cookie.split(';')

    for (let i = 0; i < ca.length; i++) {
      let c = ca[i]
      while (c.charAt(0) === ' ') c = c.substring(1, c.length)
      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length)
    }
    return null
  },

  /**
   * 删除Cookie
   */
  deleteCookie(name: string): void {
    if (typeof document === 'undefined') return

    const isProduction = process.env.NODE_ENV === 'production'
    const domain = isProduction ? '.goglobalsp.com' : 'localhost'

    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${domain}`
  }
}

// 根据环境设置不同的key前缀，实现环境隔离
const ENV_PREFIX = process.env.NODE_ENV === 'production' ? 'prod_' : 'dev_'
const TOKEN_KEY = `${ENV_PREFIX}auth_token`
const REFRESH_TOKEN_KEY = `${ENV_PREFIX}refresh_token`

export const tokenManager = {
  /**
   * 获取访问token（优先从cookies读取，fallback到localStorage）
   */
  getToken: (): string | null => {
    
    if (typeof window === 'undefined') return null

    // 优先从cookies获取
    let token = cookieUtils.getCookie(TOKEN_KEY)
    

    // 如果cookies中没有，尝试从localStorage获取
    if (!token) {
      try {
        token = localStorage.getItem(TOKEN_KEY)
      } catch (error) {
        console.warn('Failed to read token from localStorage:', error)
      }
    }

    return token
  },

  /**
   * 设置访问token（同时存储到cookies和localStorage）
   */
  setToken: (token: string): void => {
    if (typeof window === 'undefined') return

    // 存储到cookies
    cookieUtils.setCookie(TOKEN_KEY, token, 7)

    // 同时存储到localStorage作为备份
    try {
      localStorage.setItem(TOKEN_KEY, token)
    } catch (error) {
      console.warn('Failed to store token in localStorage:', error)
    }
  },

  /**
   * 获取刷新token
   */
  getRefreshToken: (): string | null => {
    if (typeof window === 'undefined') return null

    let refreshToken = cookieUtils.getCookie(REFRESH_TOKEN_KEY)

    if (!refreshToken) {
      try {
        refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY)
      } catch (error) {
        console.warn('Failed to read refresh token from localStorage:', error)
      }
    }

    return refreshToken
  },

  /**
   * 设置刷新token
   */
  setRefreshToken: (token: string): void => {
    if (typeof window === 'undefined') return

    // 存储到cookies（30天过期）
    cookieUtils.setCookie(REFRESH_TOKEN_KEY, token, 30)

    // 同时存储到localStorage作为备份
    try {
      localStorage.setItem(REFRESH_TOKEN_KEY, token)
    } catch (error) {
      console.warn('Failed to store refresh token in localStorage:', error)
    }
  },

  /**
   * 移除所有token
   */
  removeTokens: (): void => {
    if (typeof window === 'undefined') return

    // 清除cookies
    cookieUtils.deleteCookie(TOKEN_KEY)
    cookieUtils.deleteCookie(REFRESH_TOKEN_KEY)

    // 清除localStorage
    try {
      localStorage.removeItem(TOKEN_KEY)
      localStorage.removeItem(REFRESH_TOKEN_KEY)
    } catch (error) {
      console.warn('Failed to clear tokens from localStorage:', error)
    }
  },

  /**
   * 检查token是否过期
   */
  isTokenExpired: (token: string): boolean => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return Date.now() >= payload.exp * 1000
    } catch {
      return true
    }
  },

  /**
   * 获取token的payload
   */
  getTokenPayload: (token: string): any => {
    try {
      return JSON.parse(atob(token.split('.')[1]))
    } catch {
      return null
    }
  },

  /**
   * 检查token是否有效（存在且未过期）
   */
  isTokenValid: (): boolean => {
    const token = tokenManager.getToken()
    if (!token) return false
    return !tokenManager.isTokenExpired(token)
  }
}
