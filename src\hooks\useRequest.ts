/**
 * 底层HTTP请求Hook
 * 提供统一的请求处理逻辑，包括认证、语言头、错误处理等
 */

import React, { useState, useCallback, useRef } from 'react'
import { useLocale } from 'next-intl'
import { tokenManager } from '@/lib/token'
import { getBackendLanguage, type Locale } from '@/lib/i18n/config'
import { responseInterceptor, type ApiResponse } from '@/lib/api-interceptor'

// 请求配置接口
export interface RequestConfig extends Omit<RequestInit, 'body'> {
  skipAuth?: boolean // 是否跳过认证
  showError?: boolean // 是否显示错误提示
  showSuccess?: boolean // 是否显示成功提示
  successMessage?: string // 自定义成功消息
  errorMessage?: string // 自定义错误消息
  timeout?: number // 请求超时时间(毫秒)
  retries?: number // 重试次数
  body?: any // 请求体，支持任意类型
}

// 请求状态接口
export interface RequestState<T> {
  data: T | null
  loading: boolean
  error: Error | null
  executed: boolean
}

// 请求执行函数类型
export type RequestExecutor<T> = (config?: Partial<RequestConfig>) => Promise<T>

/**
 * 获取当前用户语言设置
 */
function getCurrentLanguage(locale?: string): string {
  if (typeof window === 'undefined') {
    return 'chinese' // 服务端默认中文
  }

  try {
    // 1. 优先使用传入的locale
    if (locale && ['zh', 'en', 'ja'].includes(locale)) {
      return getBackendLanguage(locale as Locale)
    }

    // 2. 从URL路径获取语言
    const pathname = window.location.pathname
    const pathSegments = pathname.split('/').filter(Boolean)
    
    if (pathSegments.length > 0) {
      const firstSegment = pathSegments[0]
      if (['zh', 'en', 'ja'].includes(firstSegment)) {
        return getBackendLanguage(firstSegment as Locale)
      }
    }

    // 3. 从localStorage获取用户语言偏好
    const storedLanguage = localStorage.getItem('user-language')
    if (storedLanguage) {
      return storedLanguage
    }

    // 4. 默认中文
    return 'chinese'
  } catch (error) {
    console.warn('获取当前语言失败，使用默认中文:', error)
    return 'chinese'
  }
}

/**
 * 底层请求Hook
 * 提供统一的HTTP请求处理逻辑
 */
export function useRequest<T = any>(
  url: string,
  defaultConfig: RequestConfig = {}
): [RequestState<T>, RequestExecutor<T>] {
  const locale = useLocale()
  const [state, setState] = useState<RequestState<T>>({
    data: null,
    loading: false,
    error: null,
    executed: false
  })
  
  const abortControllerRef = useRef<AbortController | null>(null)

  const execute = useCallback(async (overrideConfig: Partial<RequestConfig> = {}): Promise<T> => {
    // 合并配置
    const config: RequestConfig = {
      method: 'GET',
      skipAuth: false,
      showError: true,
      showSuccess: false,
      timeout: 30000,
      retries: 0,
      ...defaultConfig,
      ...overrideConfig
    }

    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // 创建新的AbortController
    abortControllerRef.current = new AbortController()

    // 设置加载状态
    setState(prev => ({
      ...prev,
      loading: true,
      error: null
    }))

    try {
      // 准备请求头
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        ...config.headers as Record<string, string>
      }

      // 添加语言头
      headers['language'] = getCurrentLanguage(locale)

      // 添加认证头
      if (!config.skipAuth) {
        const token = tokenManager.getToken()
        if (token) {
          headers['authorization'] = token
        }
      }

      // 准备请求体
      let body: string | undefined
      if (config.body !== undefined && config.method !== 'GET' && config.method !== 'DELETE') {
        body = typeof config.body === 'string' ? config.body : JSON.stringify(config.body)
      }

      // 准备最终请求配置
      const finalConfig: RequestInit = {
        ...config,
        headers,
        body,
        signal: abortControllerRef.current.signal
      }

      // 设置超时
      const timeoutId = setTimeout(() => {
        if (abortControllerRef.current) {
          abortControllerRef.current.abort()
        }
      }, config.timeout)

      try {
        // 执行请求
        const response = await fetch(url, finalConfig)
        clearTimeout(timeoutId)

        const data = await response.json()

        // 检查HTTP状态
        if (!response.ok && !data.hasOwnProperty('success')) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // 使用响应拦截器处理结果
        const result = await responseInterceptor(
          Promise.resolve(data as ApiResponse<T>),
          {
            showError: config.showError,
            showSuccess: config.showSuccess,
            successMessage: config.successMessage,
            errorMessage: config.errorMessage
          }
        )

        // 更新状态
        setState({
          data: result,
          loading: false,
          error: null,
          executed: true
        })

        return result
      } catch (error) {
        clearTimeout(timeoutId)
        
        // 如果是取消请求，不处理错误
        if (error instanceof Error && error.name === 'AbortError') {
          throw error
        }

        // 重试逻辑
        if (config.retries && config.retries > 0) {
          console.log(`请求失败，${config.retries}秒后重试...`)
          await new Promise(resolve => setTimeout(resolve, 1000))
          return execute({ ...config, retries: config.retries - 1 })
        }

        throw error
      }
    } catch (error) {
      const finalError = error instanceof Error ? error : new Error('Unknown error')
      
      setState({
        data: null,
        loading: false,
        error: finalError,
        executed: true
      })

      throw finalError
    }
  }, [url, defaultConfig, locale])

  return [state, execute]
}

/**
 * 自动执行的请求Hook
 * 组件挂载时自动执行请求
 */
export function useAutoRequest<T = any>(
  url: string,
  config: RequestConfig = {},
  deps: React.DependencyList = []
): RequestState<T> {
  const [state, execute] = useRequest<T>(url, config)

  // 自动执行请求
  React.useEffect(() => {
    execute().catch(error => {
      console.error('Auto request failed:', error)
    })
  }, [execute, ...deps])

  return state
}
