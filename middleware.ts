/**
 * Next.js 中间件
 * 实现国际化 + 5层路由守卫系统
 */

import { NextRequest, NextResponse } from 'next/server'
import createIntlMiddleware from 'next-intl/middleware'
import { createMiddlewareChain } from './src/lib/middleware/chain'
import { embedGuard } from './src/lib/middleware/embed-security'
import { authGuard } from './src/lib/middleware/auth-guard'
// import { accessGuard } from './src/lib/middleware/access-guard' // 已删除 - 无路由使用access权限控制
// import { pollGuard } from './src/lib/middleware/poll-guard' // 已删除
import { menuGuard } from './src/lib/middleware/menu-guard'
import { feedbackGuard } from './src/lib/middleware/feedback-guard'
import { locales, defaultLocale } from './src/lib/i18n/config'

// 创建 next-intl 中间件
const intlMiddleware = createIntlMiddleware({
  locales,
  defaultLocale,
  localePrefix: 'always'
})

/**
 * 创建中间件链，按顺序执行4层守卫
 * 0. 嵌入守卫 - 处理iframe嵌入页面的特殊认证和安全验证
 * 1. 认证守卫 - 处理登录状态验证
 * 2. 菜单守卫 - 处理菜单权限
 * 3. 反馈守卫 - 处理页面重定向
 */
const middlewareChain = createMiddlewareChain([
  embedGuard,     // 第0层：嵌入守卫（优先级最高）
  authGuard,      // 第1层：认证守卫
  // accessGuard, // 已删除 - 无路由使用access权限控制
  // pollGuard,   // 已删除 - 无业务流程需求
  menuGuard,      // 第2层：菜单守卫
  feedbackGuard,  // 第3层：反馈守卫
])

/**
 * 主中间件函数
 * @param request Next.js请求对象
 * @returns Next.js响应对象
 */
export async function middleware(request: NextRequest): Promise<NextResponse> {
  const { pathname } = request.nextUrl

  // 跳过静态资源和API路由
  if (shouldSkipMiddleware(pathname)) {
    return NextResponse.next()
  }

  // 开发环境日志
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Middleware] Processing: ${pathname}`)
  }

  try {
    // 首先处理国际化路由
    // 如果路径不是以语言代码开头且不是特殊路径，则应用国际化中间件
    const isApiRoute = pathname.startsWith('/api')
    const isEmbedRoute = pathname.startsWith('/embed')
    const hasLocalePrefix = locales.some(locale => pathname.startsWith(`/${locale}`))

    // 对于没有语言前缀的路径（如 /login），应用国际化中间件进行重定向
    if (!isApiRoute && !isEmbedRoute && !hasLocalePrefix) {
      console.log(`[Middleware] Applying i18n redirect for: ${pathname}`)
      const intlResponse = intlMiddleware(request)
      if (intlResponse) {
        console.log(`[Middleware] i18n redirecting ${pathname} to ${intlResponse.headers.get('location')}`)
        return intlResponse
      }
    }

    // 执行业务中间件链
    const response = await middlewareChain(request)

    // 为非嵌入页面添加标准安全头
    if (!pathname.startsWith('/embed')) {
      addSecurityHeaders(response)
    }

    return response
  } catch (error) {
    console.error('[Middleware] Error:', error)
    
    // 发生错误时，允许请求继续
    return NextResponse.next()
  }
}

/**
 * 检查是否应该跳过中间件处理
 * @param pathname 路径名
 * @returns 是否跳过
 */
function shouldSkipMiddleware(pathname: string): boolean {
  const skipPatterns = [
    // 静态资源
    '/_next/static',
    '/_next/image',
    '/favicon.ico',
    '/robots.txt',
    '/sitemap.xml',
    
    // 图片和媒体文件
    /\.(jpg|jpeg|png|gif|svg|ico|webp|avif)$/,
    /\.(mp4|webm|ogg|mp3|wav|flac|aac)$/,
    /\.(woff|woff2|eot|ttf|otf)$/,
    
    // API路由（如果需要特殊处理）
    '/api/public',
    '/api/health',
    '/api/status',
    
    // 开发工具
    '/__nextjs',
    '/_vercel',
    
    // 特殊路径
    '/manifest.json',
    '/sw.js',
    '/workbox-',
  ]

  return skipPatterns.some(pattern => {
    if (typeof pattern === 'string') {
      return pathname.startsWith(pattern)
    }
    if (pattern instanceof RegExp) {
      return pattern.test(pathname)
    }
    return false
  })
}

/**
 * 添加安全头
 * @param response Next.js响应对象
 */
function addSecurityHeaders(response: NextResponse): void {
  // 防止点击劫持
  response.headers.set('X-Frame-Options', 'DENY')
  
  // 防止MIME类型嗅探
  response.headers.set('X-Content-Type-Options', 'nosniff')
  
  // XSS保护
  response.headers.set('X-XSS-Protection', '1; mode=block')
  
  // 引用者策略
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  
  // 权限策略
  response.headers.set(
    'Permissions-Policy',
    'camera=(), microphone=(), geolocation=(), interest-cohort=()'
  )
}

/**
 * 中间件配置
 * 定义哪些路径需要经过中间件处理
 * 使用Next.js官方推荐的matcher模式
 */
export const config = {
  matcher: [
    /*
     * 匹配所有路径，除了：
     * - api路由（/api开头）
     * - _next（Next.js内部路由）
     * - 静态资源文件（包含.的文件）
     */
    '/((?!api|_next|.*\\..*).*)'
  ],
}

/**
 * 中间件工具函数
 */
export const middlewareUtils = {
  /**
   * 检查请求是否来自移动设备
   */
  isMobileDevice(request: NextRequest): boolean {
    const userAgent = request.headers.get('user-agent') || ''
    return /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)
  },

  /**
   * 获取客户端IP地址
   */
  getClientIP(request: NextRequest): string {
    const forwarded = request.headers.get('x-forwarded-for')
    const realIP = request.headers.get('x-real-ip')
    const cfConnectingIP = request.headers.get('cf-connecting-ip')
    
    if (forwarded) {
      return forwarded.split(',')[0].trim()
    }
    
    if (realIP) {
      return realIP
    }
    
    if (cfConnectingIP) {
      return cfConnectingIP
    }
    
    return 'unknown'
  },

  /**
   * 检查请求是否来自搜索引擎爬虫
   */
  isBot(request: NextRequest): boolean {
    const userAgent = request.headers.get('user-agent') || ''
    return /bot|crawler|spider|crawling/i.test(userAgent)
  },

  /**
   * 获取请求的地理位置信息（如果可用）
   */
  getGeoLocation(request: NextRequest): {
    country?: string
    region?: string
    city?: string
  } {
    const country = request.headers.get('cf-ipcountry')
    const region = request.headers.get('cf-region')
    const city = request.headers.get('cf-city')

    const result: { country?: string; region?: string; city?: string } = {}
    if (country) result.country = country
    if (region) result.region = region
    if (city) result.city = city

    return result
  },

  /**
   * 创建带有追踪信息的响应
   */
  createTrackedResponse(
    response: NextResponse,
    trackingData: Record<string, string>
  ): NextResponse {
    Object.entries(trackingData).forEach(([key, value]) => {
      response.headers.set(`x-track-${key}`, value)
    })
    return response
  }
}
