'use client'

import * as React from 'react'
import { motion, AnimatePresence, useReducedMotion } from 'framer-motion'
import {
  Play,
  CheckCircle,
  XCircle,
  Clock,
  ChevronDown,
  ChevronRight,
  ExternalLink,
  RotateCcw,
  Copy,
  Download,
  Eye,
  Maximize2,
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { cn } from '@/lib/utils'

// --- 类型定义 ---
export type ToolResult = {
  id: string
  name: string
  provider: 'openai' | 'azure' | 'gemini'
  status: 'pending' | 'running' | 'completed' | 'error'
  outputType?: 'urls' | 'text'
  outputSnippet?: string
  outputPayload?: string[] | string
  startedAt: string
  finishedAt?: string
  raw?: any
}

export type Step = {
  id: string
  title: string
  description?: string
  status: 'pending' | 'running' | 'completed' | 'error'
  tools: ToolResult[]
}

type BoardProps = {
  steps: Step[]
  onRetry: (toolId: string) => void
}

// --- 响应式 hooks ---
const useIsMobile = () => {
  const [isMobile, setIsMobile] = React.useState(false)
  React.useEffect(() => {
    const update = () => setIsMobile(window.innerWidth < 768)
    update()
    window.addEventListener('resize', update)
    return () => window.removeEventListener('resize', update)
  }, [])
  return isMobile
}

const useIsTablet = () => {
  const [isTablet, setIsTablet] = React.useState(false)
  React.useEffect(() => {
    const update = () => {
      const width = window.innerWidth
      setIsTablet(width >= 768 && width < 1024)
    }
    update()
    window.addEventListener('resize', update)
    return () => window.removeEventListener('resize', update)
  }, [])
  return isTablet
}

// --- 小组件 ---
const ProviderLogo = ({ provider }: { provider: ToolResult['provider'] }) => {
  const logos = {
    openai: '🔮',
    azure: '☁️',
    gemini: '♊',
  }
  return (
    <span className="text-xs" role="img" aria-label={`${provider} provider`}>
      {logos[provider]}
    </span>
  )
}

const StatusDot = ({ status }: { status: ToolResult['status'] }) => {
  const statusMap = {
    pending: 'bg-yellow-500',
    running: 'bg-blue-500 animate-pulse',
    completed: 'bg-green-500',
    error: 'bg-red-500',
  }
  return (
    <div
      className={cn('w-2 h-2 rounded-full', statusMap[status])}
      role="status"
      aria-label={`Status: ${status}`}
    />
  )
}

const ToolPreviewContent = ({ tool }: { tool: ToolResult }) => (
  <div className="space-y-3 p-1">
    <div className="flex items-center gap-2">
      <ProviderLogo provider={tool.provider} />
      <h4 className="font-medium text-sm">{tool.name}</h4>
      <Badge variant="secondary" className="text-xs">
        {tool.status}
      </Badge>
    </div>
    <div className="space-y-2">
      <div className="text-xs text-gray-600">
        Started: {new Date(tool.startedAt).toLocaleTimeString()}
      </div>
      {tool.finishedAt && (
        <div className="text-xs text-gray-600">
          Finished: {new Date(tool.finishedAt).toLocaleTimeString()}
        </div>
      )}
    </div>
    {tool.outputSnippet && (
      <div className="p-2 bg-gray-50 rounded text-xs">
        <div className="font-medium text-gray-700 mb-1">Preview:</div>
        <div className="text-gray-600 line-clamp-2">{tool.outputSnippet}</div>
      </div>
    )}
    <div className="flex gap-1">
      <Button size="sm" variant="outline" className="h-6 text-xs">
        <Eye className="w-3 h-3 mr-1" />
        View Full
      </Button>
      {tool.status === 'error' && (
        <Button size="sm" variant="outline" className="h-6 text-xs">
          <RotateCcw className="w-3 h-3 mr-1" />
          Retry
        </Button>
      )}
    </div>
  </div>
)

const ToolDetailContent = ({
  tool,
  onRetry,
}: {
  tool: ToolResult
  onRetry: (id: string) => void
}) => (
  <div className="space-y-4">
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        <ProviderLogo provider={tool.provider} />
        <h3 className="text-lg font-semibold">{tool.name}</h3>
        <Badge>{tool.status}</Badge>
      </div>
      <div className="flex gap-2">
        {tool.outputPayload && (
          <>
            <Button size="sm" variant="outline">
              <Copy className="w-4 h-4 mr-2" />
              Copy
            </Button>
            <Button size="sm" variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </>
        )}
      </div>
    </div>
    <div className="grid grid-cols-2 gap-4 text-sm">
      <div>
        <span className="font-medium text-gray-700">Provider:</span>
        <span className="ml-2 capitalize">{tool.provider}</span>
      </div>
      <div>
        <span className="font-medium text-gray-700">Status:</span>
        <span className="ml-2 capitalize">{tool.status}</span>
      </div>
      <div>
        <span className="font-medium text-gray-700">Started:</span>
        <span className="ml-2">{new Date(tool.startedAt).toLocaleString()}</span>
      </div>
      {tool.finishedAt && (
        <div>
          <span className="font-medium text-gray-700">Finished:</span>
          <span className="ml-2">{new Date(tool.finishedAt).toLocaleString()}</span>
        </div>
      )}
    </div>
    {tool.outputPayload && (
      <div>
        <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
          Output
          {tool.outputType && (
            <Badge variant="outline" className="text-xs">
              {tool.outputType}
            </Badge>
          )}
        </h4>
        {tool.outputType === 'urls' ? (
          <ScrollArea className="max-h-[300px]">
            <div className="space-y-2">
              {(tool.outputPayload as string[]).map((url, i) => (
                <div key={i} className="flex items-center gap-2 p-2 border rounded">
                  <ExternalLink className="w-4 h-4 text-gray-400" />
                  <a
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:underline truncate flex-1"
                  >
                    {url}
                  </a>
                  <Button size="sm" variant="ghost">
                    <Copy className="w-3 h-3" />
                  </Button>
                </div>
              ))}
            </div>
          </ScrollArea>
        ) : (
          <ScrollArea className="max-h-[300px]">
            <pre className="text-sm whitespace-pre-wrap p-3 bg-gray-50 rounded border">
              {tool.outputPayload as string}
            </pre>
          </ScrollArea>
        )}
      </div>
    )}
    {tool.status === 'error' && (
      <div className="flex gap-2">
        <Button onClick={() => onRetry(tool.id)} className="flex-1">
          <RotateCcw className="w-4 h-4 mr-2" />
          Retry Tool
        </Button>
      </div>
    )}
  </div>
)

// ---- ToolItem (混合模式：桌面Popover+Modal，移动Drawer) ----
const ToolItem = ({
  tool,
  onRetry,
  onOpenDetail,
}: {
  tool: ToolResult
  onRetry: (id: string) => void
  onOpenDetail: () => void
}) => {
  const isMobile = useIsMobile()
  const [popoverOpen, setPopoverOpen] = React.useState(false)

  if (isMobile) {
    return (
      <button
        className="flex items-center gap-3 p-3 w-full hover:bg-gray-50 text-left transition-colors rounded"
        onClick={onOpenDetail}
        aria-label={`View details for ${tool.name}`}
      >
        <ProviderLogo provider={tool.provider} />
        <div className="flex-1 min-w-0">
          <div className="font-medium text-sm truncate">{tool.name}</div>
          {tool.outputSnippet && (
            <div className="text-xs text-gray-500 truncate mt-0.5">{tool.outputSnippet}</div>
          )}
        </div>
        <StatusDot status={tool.status} />
        <ChevronRight className="w-4 h-4 text-gray-400" />
      </button>
    )
  }

  return (
    <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
      <PopoverTrigger asChild>
        <button
          className="flex items-center gap-2 p-2 w-full hover:bg-gray-50 text-left transition-colors rounded group"
          aria-haspopup="dialog"
          aria-expanded={popoverOpen}
          aria-label={`Preview ${tool.name}`}
          onClick={() => setPopoverOpen(prev => !prev)}
        >
          <ProviderLogo provider={tool.provider} />
          <span className="flex-1 truncate text-sm">{tool.name}</span>
          <StatusDot status={tool.status} />
          {tool.outputSnippet && (
            <span className="text-xs text-gray-400 max-w-[100px] truncate hidden sm:block">
              {tool.outputSnippet}
            </span>
          )}
          <Maximize2 className="w-3 h-3 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
        </button>
      </PopoverTrigger>
      <AnimatePresence>
        {popoverOpen && (
          <PopoverContent className="w-80" align="start" asChild>
            <motion.div
              initial={{ opacity: 0, y: 8 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 8 }}
              transition={{ type: 'spring', stiffness: 320, damping: 24 }}
            >
              <ToolPreviewContent tool={tool} />
              <div className="flex gap-2 mt-3 pt-3 border-t">
                <Button
                  size="sm"
                  onClick={() => {
                    setPopoverOpen(false)
                    onOpenDetail()
                  }}
                  className="flex-1"
                  aria-label={`View full detail for ${tool.name}`}
                >
                  <Maximize2 className="w-3 h-3 mr-1" />
                  View Full
                </Button>
                {tool.status === 'error' && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={e => {
                      e.stopPropagation()
                      setPopoverOpen(false)
                      onRetry(tool.id)
                    }}
                  >
                    <RotateCcw className="w-3 h-3 mr-1" />
                    Retry
                  </Button>
                )}
              </div>
            </motion.div>
          </PopoverContent>
        )}
      </AnimatePresence>
    </Popover>
  )
}

// ---- Tool Detail（Drawer / Dialog，含动画） ----
const ToolDetailModal = ({
  tool,
  open,
  onOpenChange,
  onRetry,
}: {
  tool: ToolResult
  open: boolean
  onOpenChange: (v: boolean) => void
  onRetry: (id: string) => void
}) => {
  const isMobile = useIsMobile()
  const content = <ToolDetailContent tool={tool} onRetry={onRetry} />

  return isMobile ? (
    <Drawer open={open} onOpenChange={onOpenChange} modal>
      <DrawerContent className="px-4 pb-4 max-h-[90vh]">
        <motion.div
          initial={{ opacity: 0, y: 60 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 60 }}
          transition={{ type: 'spring', stiffness: 260, damping: 20 }}
        >
          <DrawerHeader>
            <DrawerTitle>{tool.name}</DrawerTitle>
            <DrawerDescription>Tool execution details</DrawerDescription>
          </DrawerHeader>
          {content}
        </motion.div>
      </DrawerContent>
    </Drawer>
  ) : (
    <Dialog open={open} onOpenChange={onOpenChange} modal>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <motion.div
          initial={{ opacity: 0, scale: 0.98 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.98 }}
          transition={{ type: 'spring', stiffness: 320, damping: 24 }}
        >
          <DialogHeader>
            <DialogTitle>{tool.name}</DialogTitle>
            <DialogDescription>Tool execution details</DialogDescription>
          </DialogHeader>
          <div className="flex-1 overflow-auto">{content}</div>
        </motion.div>
      </DialogContent>
    </Dialog>
  )
}

// ---- StepCard ----
const StepCard = ({
  step,
  onRetry,
  isExpanded,
  onToggle,
}: {
  step: Step
  onRetry: (id: string) => void
  isExpanded: boolean
  onToggle: () => void
}) => {
  const [selectedTool, setSelectedTool] = React.useState<ToolResult | null>(null)
  const completed = step.tools.filter(t => t.status === 'completed').length
  const shouldReduceMotion = useReducedMotion()

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: shouldReduceMotion
        ? { duration: 0 }
        : { type: 'spring', stiffness: 300, damping: 30 },
    },
  }

  const toolListVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: shouldReduceMotion
        ? { duration: 0 }
        : { staggerChildren: 0.05, delayChildren: 0.1 },
    },
  }

  const toolItemVariants = {
    hidden: { opacity: 0, x: -10 },
    visible: {
      opacity: 1,
      x: 0,
      transition: shouldReduceMotion ? { duration: 0 } : { duration: 0.2 },
    },
  }

  return (
    <>
      <motion.div variants={cardVariants}>
        <Card className="w-full transition-all hover:shadow-md">
          <Collapsible open={isExpanded} onOpenChange={onToggle}>
            <CollapsibleTrigger asChild>
              <CardHeader className="cursor-pointer hover:bg-gray-50/50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="text-left flex-1">
                    <CardTitle className="text-base">{step.title}</CardTitle>
                    {step.description && (
                      <p className="text-sm text-gray-500 mt-1">{step.description}</p>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">{step.status}</Badge>
                    <ChevronDown
                      className={cn('w-4 h-4 transition-transform', isExpanded && 'rotate-180')}
                    />
                  </div>
                </div>
                <div className="space-y-2 mt-3">
                  <div className="flex justify-between text-xs text-gray-600">
                    <span>Progress</span>
                    <span>
                      {completed}/{step.tools.length} completed
                    </span>
                  </div>
                  <Progress value={(completed / step.tools.length) * 100} className="h-2" />
                </div>
              </CardHeader>
            </CollapsibleTrigger>

            <CollapsibleContent>
              <CardContent className="pt-0">
                <motion.div
                  variants={toolListVariants}
                  initial="hidden"
                  animate="visible"
                  className="space-y-1"
                >
                  {step.tools.map(tool => (
                    <motion.div key={tool.id} variants={toolItemVariants}>
                      <ToolItem
                        tool={tool}
                        onRetry={onRetry}
                        onOpenDetail={() => setSelectedTool(tool)}
                      />
                    </motion.div>
                  ))}
                </motion.div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>
      </motion.div>

      {selectedTool && (
        <ToolDetailModal
          tool={selectedTool}
          open={!!selectedTool}
          onOpenChange={open => !open && setSelectedTool(null)}
          onRetry={onRetry}
        />
      )}
    </>
  )
}

// ---- StatusColumn ----
const StatusColumn = ({
  title,
  status,
  steps,
  onRetry,
  accentColor,
  icon: Icon,
}: {
  title: string
  status: Step['status']
  steps: Step[]
  onRetry: (id: string) => void
  accentColor: string
  icon: React.ElementType
}) => {
  const [expandedSteps, setExpandedSteps] = React.useState<Set<string>>(new Set(steps.map(s => s.id)))
  const shouldReduceMotion = useReducedMotion()

  const filtered = steps.filter(s => s.status === status)

  const toggleStep = (stepId: string) => {
    setExpandedSteps(prev => {
      const newSet = new Set(prev)
      if (newSet.has(stepId)) {
        newSet.delete(stepId)
      } else {
        newSet.add(stepId)
      }
      return newSet
    })
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: shouldReduceMotion
        ? { duration: 0 }
        : { staggerChildren: 0.1, delayChildren: 0.2 },
    },
  }

  return (
    <div className="flex-1 min-w-0 flex flex-col bg-white rounded-lg shadow-sm border">
      <div className={cn('p-4 border-b rounded-t-lg', accentColor)}>
        <div className="flex items-center gap-2">
          <Icon className="w-5 h-5" />
          <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
          <Badge className="ml-auto bg-white text-gray-700 hover:bg-gray-50">
            {filtered.length}
          </Badge>
        </div>
      </div>
      <ScrollArea className="flex-1 p-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-3"
        >
          <AnimatePresence mode="popLayout">
            {filtered.map(step => (
              <StepCard
                key={step.id}
                step={step}
                onRetry={onRetry}
                isExpanded={expandedSteps.has(step.id)}
                onToggle={() => toggleStep(step.id)}
              />
            ))}
          </AnimatePresence>
        </motion.div>
      </ScrollArea>
    </div>
  )
}

// ---- 三端布局适配 ----
const DesktopLayout = ({ steps, onRetry }: BoardProps) => (
  <div className="h-screen bg-gray-50">
    <div className="flex gap-6 p-6 h-full">
      <StatusColumn
        title="Pending"
        status="pending"
        steps={steps}
        onRetry={onRetry}
        accentColor="bg-yellow-50 border-yellow-200"
        icon={Clock}
      />
      <StatusColumn
        title="Running"
        status="running"
        steps={steps}
        onRetry={onRetry}
        accentColor="bg-blue-50 border-blue-200"
        icon={Play}
      />
      <StatusColumn
        title="Completed"
        status="completed"
        steps={steps}
        onRetry={onRetry}
        accentColor="bg-green-50 border-green-200"
        icon={CheckCircle}
      />
      <StatusColumn
        title="Error"
        status="error"
        steps={steps}
        onRetry={onRetry}
        accentColor="bg-red-50 border-red-200"
        icon={XCircle}
      />
    </div>
  </div>
)

const TabletLayout = ({ steps, onRetry }: BoardProps) => {
  const getStepsByStatus = (status: Step['status']) => steps.filter(s => s.status === status)

  return (
    <div className="h-screen bg-gray-50">
      <Tabs defaultValue="running" className="h-full flex flex-col">
        <div className="border-b bg-white px-4 py-2">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="pending" className="text-xs">
              Pending ({getStepsByStatus('pending').length})
            </TabsTrigger>
            <TabsTrigger value="running" className="text-xs">
              Running ({getStepsByStatus('running').length})
            </TabsTrigger>
            <TabsTrigger value="completed" className="text-xs">
              Completed ({getStepsByStatus('completed').length})
            </TabsTrigger>
            <TabsTrigger value="error" className="text-xs">
              Error ({getStepsByStatus('error').length})
            </TabsTrigger>
          </TabsList>
        </div>
        <div className="flex-1 overflow-hidden">
          {(['pending', 'running', 'completed', 'error'] as const).map(status => (
            <TabsContent key={status} value={status} className="h-full mt-0">
              <ScrollArea className="h-full p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {getStepsByStatus(status).map(step => (
                    <StepCard
                      key={step.id}
                      step={step}
                      onRetry={onRetry}
                      isExpanded={true}
                      onToggle={() => {}}
                    />
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          ))}
        </div>
      </Tabs>
    </div>
  )
}

const MobileLayout = ({ steps, onRetry }: BoardProps) => {
  const [selectedStatus, setSelectedStatus] = React.useState<Step['status']>('running')

  const getCountByStatus = (status: Step['status']) => steps.filter(s => s.status === status).length

  const filteredSteps = steps.filter(s => s.status === selectedStatus)

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      <div className="sticky top-0 bg-white border-b p-4 z-10">
        <Select
          value={selectedStatus}
          onValueChange={value => setSelectedStatus(value as Step['status'])}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="pending">Pending ({getCountByStatus('pending')})</SelectItem>
            <SelectItem value="running">Running ({getCountByStatus('running')})</SelectItem>
            <SelectItem value="completed">Completed ({getCountByStatus('completed')})</SelectItem>
            <SelectItem value="error">Error ({getCountByStatus('error')})</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {filteredSteps.map(step => (
            <StepCard
              key={step.id}
              step={step}
              onRetry={onRetry}
              isExpanded={true}
              onToggle={() => {}}
            />
          ))}
        </div>
      </ScrollArea>
    </div>
  )
}

// ---- 主 Kanban 入口 ----
export const KanbanBoard = ({ steps, onRetry }: BoardProps) => {
  const isMobile = useIsMobile()
  const isTablet = useIsTablet()

  if (isMobile) {
    return <MobileLayout steps={steps} onRetry={onRetry} />
  }
  if (isTablet) {
    return <TabletLayout steps={steps} onRetry={onRetry} />
  }
  return <DesktopLayout steps={steps} onRetry={onRetry} />
}

// ---- Demo 用例 ----
export default function KanbanDemoPage() {
  const [steps, setSteps] = React.useState<Step[]>([
    {
      id: 'step-1',
      title: 'Data Processing',
      description: 'Process incoming data streams',
      status: 'pending',
      tools: [
        {
          id: 'tool-1',
          name: 'Text Analysis',
          provider: 'openai',
          status: 'pending',
          startedAt: new Date().toISOString(),
        },
        {
          id: 'tool-2',
          name: 'Sentiment Detection',
          provider: 'azure',
          status: 'pending',
          startedAt: new Date().toISOString(),
        },
      ],
    },
    {
      id: 'step-2',
      title: 'Content Generation',
      description: 'Generate content based on analysis',
      status: 'running',
      tools: [
        {
          id: 'tool-3',
          name: 'Content Writer',
          provider: 'openai',
          status: 'running',
          outputType: 'text',
          outputSnippet: 'Generating comprehensive article...',
          outputPayload:
            '# AI in 2024\n\nAI continues to evolve rapidly with new breakthroughs in natural language processing, computer vision, and autonomous systems.',
          startedAt: new Date().toISOString(),
        },
        {
          id: 'tool-4',
          name: 'Image Generator',
          provider: 'gemini',
          status: 'completed',
          outputType: 'urls',
          outputSnippet: '2 images generated',
          outputPayload: [
            'https://example.com/generated-image-1.jpg',
            'https://example.com/generated-image-2.jpg',
          ],
          startedAt: new Date().toISOString(),
          finishedAt: new Date().toISOString(),
        },
      ],
    },
    {
      id: 'step-3',
      title: 'Quality Review',
      description: 'Review and validate outputs',
      status: 'completed',
      tools: [
        {
          id: 'tool-5',
          name: 'Grammar Check',
          provider: 'azure',
          status: 'completed',
          outputType: 'text',
          outputSnippet: 'Grammar check passed',
          outputPayload:
            'No grammar issues found in the generated content. All sentences are properly structured.',
          startedAt: new Date().toISOString(),
          finishedAt: new Date().toISOString(),
        },
      ],
    },
    {
      id: 'step-4',
      title: 'Failed Processing',
      description: 'This step encountered errors',
      status: 'error',
      tools: [
        {
          id: 'tool-6',
          name: 'Data Validator',
          provider: 'openai',
          status: 'error',
          outputType: 'text',
          outputSnippet: 'Connection timeout after 30s',
          outputPayload:
            'Error: Connection timeout\n\nThe request failed due to network connectivity issues. Please check your connection settings and try again.',
          startedAt: new Date().toISOString(),
          finishedAt: new Date().toISOString(),
        },
      ],
    },
  ])

  const handleRetry = (toolId: string) => {
    setSteps(prev =>
      prev.map(step => ({
        ...step,
        tools: step.tools.map(tool => (tool.id === toolId ? { ...tool, status: 'pending' } : tool)),
      }))
    )
  }

  return (
    <div className="w-full">
      {/* Skip Link for Accessibility */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4
                 bg-white px-4 py-2 rounded shadow-lg z-50 focus:outline-2 focus:outline-blue-600"
        tabIndex={0}
      >
        Skip to main content
      </a>
      <main id="main-content">
        <KanbanBoard steps={steps} onRetry={handleRetry} />
      </main>
    </div>
  )
}
