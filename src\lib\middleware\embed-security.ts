import { NextRequest, NextResponse } from 'next/server'
import type { MiddlewareFunction } from './types'
import { DevDebugger } from '@/lib/config/dev-debug'

/**
 * 统一的iframe嵌入安全中间件
 * 整合了安全验证、域名控制、认证检查和访问日志功能
 */

// 配置常量
const ALLOWED_DOMAINS = [
  'https://www.goglobalsp.com',
  'https://dev.goglobalsp.com'
]

const INTERNAL_IPS = [
  '127.0.0.1',
  '**************',
  '**************'
]

// 开发环境支持
const DEV_DOMAINS = [
  'http://localhost:3000',
  'http://127.0.0.1:3000',
  'http://0.0.0.0:3000',
  'http://localhost:10001',
  'http://127.0.0.1:10001',
  'http://**************',
  // 🔧 新增：支持特定的iframe-demo页面
  'http://localhost:10001/iframe-demo',
  'http://127.0.0.1:10001/iframe-demo'
]

// 开发调试模式 - 可以通过环境变量控制
const DEV_DEBUG_MODE = process.env.NEXT_PUBLIC_DEBUG === 'true' || process.env.NODE_ENV === 'development'


/**
 * 嵌入页面访问日志记录器
 */
class EmbedAccessLogger {
  private static logs: Array<{
    timestamp: Date
    pathname: string
    clientIP: string
    origin?: string | null
    referer?: string | null
    success: boolean
    error?: string
  }> = []

  static log(request: NextRequest, success: boolean, error?: string) {
    const referer = request.headers.get('referer')
    const origin = request.headers.get('origin')
    const clientIP = this.getClientIP(request)

    this.logs.push({
      timestamp: new Date(),
      pathname: request.nextUrl.pathname,
      clientIP,
      ...(origin && { origin }),
      ...(referer && { referer }),
      success,
      ...(error && { error })
    })

    // 保持日志数量在合理范围内
    if (this.logs.length > 1000) {
      this.logs = this.logs.slice(-500)
    }

    // 开发环境输出日志
    if (process.env.NODE_ENV === 'development') {
      console.log(`[EmbedAccess] ${success ? 'SUCCESS' : 'FAILED'} - ${request.nextUrl.pathname}`, {
        clientIP,
        origin,
        referer,
        error
      })
    }
  }

  static getClientIP(request: NextRequest): string {
    const forwardedFor = request.headers.get('x-forwarded-for')
    const realIP = request.headers.get('x-real-ip')
    return forwardedFor?.split(',')[0] || realIP || '127.0.0.1'
  }

  static getLogs(limit = 100) {
    return this.logs.slice(-limit)
  }

  static getFailedLogs(limit = 50) {
    return this.logs.filter(log => !log.success).slice(-limit)
  }

  static clearLogs() {
    this.logs = []
  }
}

/**
 * 域名验证工具
 */
class DomainValidator {
  static isAllowed(origin: string | null): boolean {
    if (!origin) {
      console.log(`[DomainValidator] origin为空`)
      return false
    }

    console.log(`[DomainValidator] 检查origin: ${origin}`)
    console.log(`[DomainValidator] NODE_ENV: ${process.env.NODE_ENV}`)

    // 开发环境支持
    if (process.env.NODE_ENV === 'development') {
      console.log(`[DomainValidator] 开发环境，检查DEV_DOMAINS:`, DEV_DOMAINS)
      if (DEV_DOMAINS.some(domain => origin.startsWith(domain))) {
        console.log(`[DomainValidator] ✅ 开发环境域名匹配`)
        return true
      }
    }

    const isInAllowedDomains = ALLOWED_DOMAINS.includes(origin)
    console.log(`[DomainValidator] 检查ALLOWED_DOMAINS包含 ${origin}: ${isInAllowedDomains}`)

    return isInAllowedDomains
  }

  static isRefererAllowed(referer: string | null): boolean {
    if (!referer) return false

    try {
      const url = new URL(referer)
      const origin = `${url.protocol}//${url.hostname}`
      const fullReferer = `${url.protocol}//${url.hostname}${url.port ? ':' + url.port : ''}`

      console.log(`[DomainValidator] 检查referer: ${referer}`)
      console.log(`[DomainValidator] 解析的origin: ${origin}`)
      console.log(`[DomainValidator] 完整的referer源: ${fullReferer}`)
      console.log(`[DomainValidator] 允许的域名列表:`, ALLOWED_DOMAINS)

      // 🔧 开发环境强化支持：特殊处理localhost:10001
      if (DEV_DEBUG_MODE) {
        // 检查是否是localhost系列域名
        if (url.hostname === 'localhost' || url.hostname === '127.0.0.1' || url.hostname === '0.0.0.0') {
          console.log(`[DomainValidator] 🔓 开发调试模式 - 允许localhost访问: ${fullReferer}`)
          return true
        }
        
        // 特殊支持localhost:10001的iframe-demo页面
        if (url.hostname === 'localhost' && url.port === '10001' && url.pathname.includes('iframe-demo')) {
          console.log(`[DomainValidator] 🎯 iframe-demo调试模式 - 特殊支持: ${referer}`)
          return true
        }
      }

      // 检查DEV_DOMAINS列表
      if (process.env.NODE_ENV === 'development') {
        const isInDevDomains = DEV_DOMAINS.some(domain => {
          return fullReferer.startsWith(domain) || origin.startsWith(domain)
        })
        if (isInDevDomains) {
          console.log(`[DomainValidator] ✅ 开发环境域名列表匹配: ${fullReferer}`)
          return true
        }
      }

      const isAllowed = this.isAllowed(origin)
      console.log(`[DomainValidator] 域名 ${origin} 是否被允许: ${isAllowed}`)

      return isAllowed
    } catch (error) {
      console.error(`[DomainValidator] 解析referer失败:`, error)
      return false
    }
  }

  static getAllowedDomains(): string[] {
    const domains = [...ALLOWED_DOMAINS]
    if (process.env.NODE_ENV === 'development') {
      domains.push(...DEV_DOMAINS)
    }
    return domains
  }
}

/**
 * 安全头部设置工具
 */
class SecurityHeaders {
  static setIframeHeaders(response: NextResponse, allowedOrigin: string): void {
    // 🔧 开发环境特殊处理：完全允许localhost嵌入
    if (process.env.NODE_ENV === 'development') {
      // 开发环境使用更宽松的策略
      response.headers.set('X-Frame-Options', 'SAMEORIGIN')
      
      // 🎯 开发环境CSP策略：明确列出所有支持的localhost端口
      const devLocalhostPorts = [
        'http://localhost:3000',
        'http://localhost:3001', 
        'http://localhost:8000',
        'http://localhost:8080',
        'http://localhost:9000',
        'http://localhost:10001',  // 🎯 用户的端口
        'http://127.0.0.1:3000',
        'http://127.0.0.1:3001',
        'http://127.0.0.1:8000', 
        'http://127.0.0.1:8080',
        'http://127.0.0.1:9000',
        'http://127.0.0.1:10001'   // 🎯 用户的端口
      ]
      const devCSP = `frame-ancestors 'self' ${devLocalhostPorts.join(' ')} ${allowedOrigin}`
      response.headers.set('Content-Security-Policy', devCSP)
      
      console.log(`[SecurityHeaders] 🔓 开发环境CSP策略: ${devCSP}`)
    } else {
      // 生产环境严格策略
      response.headers.set('X-Frame-Options', `ALLOW-FROM ${allowedOrigin}`)
      
      const allowedDomains = DomainValidator.getAllowedDomains()
      response.headers.set(
        'Content-Security-Policy', 
        `frame-ancestors 'self' ${allowedDomains.join(' ')}`
      )
    }
    
    // CORS 设置
    response.headers.set('Access-Control-Allow-Origin', allowedOrigin)
    response.headers.set('Access-Control-Allow-Credentials', 'true')
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
    response.headers.set('Access-Control-Allow-Headers', 'Authorization, Content-Type, X-Requested-With')
    
    // 其他安全头部
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  }

  static setCORSHeaders(response: NextResponse, origin: string | null): void {
    response.headers.set('Access-Control-Allow-Origin', origin || '*')
    response.headers.set('Access-Control-Allow-Credentials', 'true')
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
    response.headers.set('Access-Control-Allow-Headers', 'Authorization, Content-Type, X-Requested-With')
  }

  /**
   * 🔧 开发环境专用头部设置 - 完全允许iframe嵌入
   */
  static setDevModeHeaders(response: NextResponse, allowedOrigin: string): void {
    console.log(`[SecurityHeaders] 🔓 开发模式：设置完全开放的安全头部，允许源: ${allowedOrigin}`)
    
    // 🚨 开发环境：完全禁用Frame限制
    response.headers.delete('X-Frame-Options')
    
    // 🚨 开发环境：超级宽松的CSP策略
    response.headers.set('Content-Security-Policy', "frame-ancestors 'self' * data: blob:")
    
    // 🚨 开发环境：完全开放的CORS
    response.headers.set('Access-Control-Allow-Origin', '*')
    response.headers.set('Access-Control-Allow-Credentials', 'true')
    response.headers.set('Access-Control-Allow-Methods', '*')
    response.headers.set('Access-Control-Allow-Headers', '*')
    
    // 保持基本安全头部
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('Referrer-Policy', 'no-referrer-when-downgrade')
    
    console.log(`[SecurityHeaders] 🔓 开发模式安全头部设置完成`)
  }
}

/**
 * IP访问控制
 */
class IPAccessControl {
  static isInternalAccess(request: NextRequest): boolean {
    const clientIP = EmbedAccessLogger.getClientIP(request)
    return INTERNAL_IPS.some(ip => clientIP.startsWith(ip))
  }

  static isLocalhost(request: NextRequest): boolean {
    const clientIP = EmbedAccessLogger.getClientIP(request)
    return clientIP === '127.0.0.1' || clientIP === '::1' || clientIP.startsWith('192.168.') || clientIP.startsWith('10.')
  }
}

/**
 * Token验证工具
 */
class TokenValidator {
  static isValidTokenFormat(token: string): boolean {
    if (!token || typeof token !== 'string') {
      return false
    }

    // JWT 格式验证
    const parts = token.split('.')
    if (parts.length !== 3) {
      return false
    }

    try {
      // 尝试解析 header 和 payload
      JSON.parse(atob(parts[0]))
      JSON.parse(atob(parts[1]))
      return true
    } catch {
      return false
    }
  }

  static extractToken(request: NextRequest): string | null {
    // 优先从 URL 参数获取 token
    const urlToken = request.nextUrl.searchParams.get('token')
    if (urlToken && this.isValidTokenFormat(urlToken)) {
      return urlToken
    }

    // 备选：从 Authorization 头获取 token
    const authHeader = request.headers.get('authorization')
    if (authHeader?.startsWith('Bearer ')) {
      const headerToken = authHeader.slice(7)
      if (this.isValidTokenFormat(headerToken)) {
        return headerToken
      }
    }

    // 备选：从 cookie 获取 token
    const cookieToken = request.cookies.get('auth-token')?.value
    if (cookieToken && this.isValidTokenFormat(cookieToken)) {
      return cookieToken
    }

    return null
  }
}

/**
 * 主要的嵌入安全中间件函数
 */
export function embedSecurityMiddleware(request: NextRequest): NextResponse {
  const { pathname } = request.nextUrl
  const origin = request.headers.get('origin')
  const referer = request.headers.get('referer')
  const clientIP = EmbedAccessLogger.getClientIP(request)

  console.log(`[EmbedSecurity] ${pathname} - IP: ${clientIP}, Origin: ${origin}, Referer: ${referer}`)

  // 处理 /[locale]/embed/* 路径（国际化embed路径）
  if (pathname.match(/^\/[a-z]{2}\/embed\//)) {
    return handleEmbedPath(request, origin, referer)
  }

  // 兼容旧的 /embed/* 路径（重定向到国际化路径）
  if (pathname.startsWith('/embed/')) {
    const url = request.nextUrl.clone()
    url.pathname = `/zh${pathname}` // 重定向到默认语言
    return NextResponse.redirect(url)
  }

  // 处理 /ui-gallery 路径 - 允许公开访问
  if (pathname.startsWith('/ui-gallery')) {
    console.log(`[EmbedSecurity] 允许公开访问 ui-gallery: ${pathname}`)
    return NextResponse.next()
  }

  // 处理 /api/health 健康检查端点
  if (pathname === '/api/health') {
    return handleHealthCheck(request)
  }

  // 处理所有其他路径
  return handleOtherPaths(request)
}

/**
 * 处理嵌入路径访问
 */
function handleEmbedPath(request: NextRequest, origin: string | null, referer: string | null): NextResponse {
  const { pathname } = request.nextUrl

  try {
    // 🎯 强化的开发调试模式 - 使用新的DevDebugger
    if (DevDebugger.isDebugMode()) {
      const debugResult = DevDebugger.shouldAllowIframeEmbed(referer, origin)
      
      if (debugResult.allowed) {
        console.log(`[EmbedSecurity] 🔓 调试模式允许嵌入: ${debugResult.reason}`)
        console.log(`[EmbedSecurity] 调试信息:`, debugResult.debugInfo)
        
        const response = NextResponse.next()
        // 🔧 开发环境特殊CSP设置：完全允许嵌入
        SecurityHeaders.setDevModeHeaders(response, origin || referer || 'http://localhost:10001')
        EmbedAccessLogger.log(request, true, `Debug mode: ${debugResult.reason}`)
        return response
      } else {
        console.log(`[EmbedSecurity] 🔒 调试模式拒绝嵌入: ${debugResult.reason}`)
        console.log(`[EmbedSecurity] 调试信息:`, debugResult.debugInfo)
      }
    }

    // 🔓 原始开发调试模式超级后门：完全跳过安全检查
    if (DEV_DEBUG_MODE && IPAccessControl.isLocalhost(request)) {
      console.log(`[EmbedSecurity] 🔓 开发调试模式 - 跳过所有安全检查: ${pathname}`)
      const response = NextResponse.next()
      SecurityHeaders.setDevModeHeaders(response, origin || referer || 'http://localhost:10001')
      EmbedAccessLogger.log(request, true, 'Development debug mode bypass')
      return response
    }

    // 🎯 终极开发环境后门：检查是否从localhost:10001访问
    if (process.env.NODE_ENV === 'development' && referer) {
      try {
        const refererUrl = new URL(referer)
        if (refererUrl.hostname === 'localhost' && (refererUrl.port === '10001' || refererUrl.port === '3000')) {
          console.log(`[EmbedSecurity] 🎯 终极后门 - 允许localhost:${refererUrl.port}嵌入: ${pathname}`)
          const response = NextResponse.next()
          SecurityHeaders.setDevModeHeaders(response, referer)
          EmbedAccessLogger.log(request, true, `Ultimate localhost bypass: ${refererUrl.port}`)
          return response
        }
      } catch (error) {
        console.log(`[EmbedSecurity] 无法解析referer: ${referer}`)
      }
    }

    // 验证 referer 域名
    if (referer) {
      console.log(`[EmbedSecurity] 开始验证referer: ${referer}`)
      console.log(`[EmbedSecurity] 当前ALLOWED_DOMAINS:`, ALLOWED_DOMAINS)

      if (!DomainValidator.isRefererAllowed(referer)) {
        const refererOrigin = new URL(referer).origin
        console.log(`[EmbedSecurity] ❌ 拒绝访问 /embed/* - 无效的 referer: ${refererOrigin}`)
        console.log(`[EmbedSecurity] 详细信息 - referer: ${referer}, origin: ${refererOrigin}`)
        console.log(`[EmbedSecurity] ALLOWED_DOMAINS包含检查:`, ALLOWED_DOMAINS.map(domain => `${domain} === ${refererOrigin} ? ${domain === refererOrigin}`))
        EmbedAccessLogger.log(request, false, `Invalid referer domain: ${refererOrigin}`)
        return new NextResponse('Forbidden: Invalid referer domain', { status: 403 })
      } else {
        console.log(`[EmbedSecurity] ✅ referer验证通过: ${referer}`)
      }
    } else {
      // 没有 referer 的情况
      const isInternalAccess = IPAccessControl.isInternalAccess(request)
      const isLocalhost = IPAccessControl.isLocalhost(request)
      
      // 开发环境或内网访问允许无 referer
      if (process.env.NODE_ENV === 'development' || isInternalAccess || isLocalhost) {
        console.log(`[EmbedSecurity] 允许无 referer 访问 - 开发环境或内网访问`)
      } else {
        console.log(`[EmbedSecurity] 拒绝访问 /embed/* - 缺少 referer 且非内网访问`)
        EmbedAccessLogger.log(request, false, 'Missing referer header')
        return new NextResponse('Forbidden: Missing referer header', { status: 403 })
      }
    }

    // 验证 token（可选，取决于业务需求）
    const token = TokenValidator.extractToken(request)
    if (!token && process.env.NODE_ENV === 'production') {
      console.log(`[EmbedSecurity] 生产环境缺少认证 token`)
      EmbedAccessLogger.log(request, false, 'Missing authentication token')
      return new NextResponse('Unauthorized: Missing token', { status: 401 })
    }

    // 创建响应并设置安全头部
    const response = NextResponse.next()
    
    if (referer) {
      const refererOrigin = new URL(referer).origin
      SecurityHeaders.setIframeHeaders(response, refererOrigin)
    } else {
      // 对于内网访问，设置基本的CORS头部
      SecurityHeaders.setCORSHeaders(response, origin)
    }

    // 记录成功访问
    EmbedAccessLogger.log(request, true)
    console.log(`[EmbedSecurity] 允许访问 /embed/* - ${pathname}`)
    
    return response

  } catch (error) {
    console.error(`[EmbedSecurity] 处理嵌入路径时发生错误:`, error)
    EmbedAccessLogger.log(request, false, error instanceof Error ? error.message : 'Unknown error')
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}

/**
 * 处理健康检查端点
 */
function handleHealthCheck(request: NextRequest): NextResponse {
  const isInternalAccess = IPAccessControl.isInternalAccess(request)
  const isLocalhost = IPAccessControl.isLocalhost(request)
  
  if (!isInternalAccess && !isLocalhost) {
    const clientIP = EmbedAccessLogger.getClientIP(request)
    console.log(`[EmbedSecurity] 拒绝访问 /api/health - 非内网IP: ${clientIP}`)
    EmbedAccessLogger.log(request, false, 'Health check access denied')
    return new NextResponse('Forbidden: Health check only available from internal network', { status: 403 })
  }
  
  return NextResponse.next()
}

/**
 * 处理其他路径访问
 */
function handleOtherPaths(request: NextRequest): NextResponse {
  const { pathname } = request.nextUrl
  const isInternalAccess = IPAccessControl.isInternalAccess(request)
  const isLocalhost = IPAccessControl.isLocalhost(request)
  
  // 开发环境允许所有访问
  if (process.env.NODE_ENV === 'development') {
    return NextResponse.next()
  }
  
  if (!isInternalAccess && !isLocalhost) {
    const clientIP = EmbedAccessLogger.getClientIP(request)
    console.log(`[EmbedSecurity] 拒绝外部访问: ${pathname} - IP: ${clientIP}`)
    EmbedAccessLogger.log(request, false, `External access denied: ${pathname}`)
    return new NextResponse('Forbidden: This path is not accessible from external sources', { status: 403 })
  }

  // 内网访问允许通过
  return NextResponse.next()
}

/**
 * 导出工具类供其他模块使用
 */
export {
  EmbedAccessLogger,
  DomainValidator,
  SecurityHeaders,
  IPAccessControl,
  TokenValidator
}

/**
 * 适配器函数：将embedSecurityMiddleware转换为MiddlewareFunction类型
 * 用于在中间件链中使用
 */
export const embedGuard: MiddlewareFunction = async (
  request: NextRequest,
  next: () => Promise<NextResponse>
): Promise<NextResponse> => {
  const { pathname } = request.nextUrl

  // 处理国际化embed路径和需要特殊安全检查的路径
  const isEmbedPath = pathname.match(/^\/[a-z]{2}\/embed\//) || pathname.startsWith('/embed/')

  if (isEmbedPath) {
    // 使用embedSecurityMiddleware处理
    const result = embedSecurityMiddleware(request)

    // 如果返回的不是默认的next()响应，说明有特殊处理
    if (result.status !== 200 || result.headers.get('x-middleware-processed')) {
      return result
    }
  }

  // 对于其他路径，在生产环境下进行IP访问控制
  if (process.env.NODE_ENV === 'production' && !isEmbedPath) {
    const isInternalAccess = IPAccessControl.isInternalAccess(request)
    const isLocalhost = IPAccessControl.isLocalhost(request)
    
    if (!isInternalAccess && !isLocalhost) {
      const clientIP = EmbedAccessLogger.getClientIP(request)
      console.log(`[EmbedGuard] 拒绝外部访问: ${pathname} - IP: ${clientIP}`)
      EmbedAccessLogger.log(request, false, `External access denied: ${pathname}`)
      return new NextResponse('Forbidden: This path is not accessible from external sources', { status: 403 })
    }
  }

  // 继续执行中间件链
  return await next()
} 