/**
 * 域名验证器
 * 验证请求来源是否在允许的域名列表中
 */

import { getAllowedEmbedDomains, isDebugEnabled, isDevelopment } from '../config/environment'

export class DomainValidator {
  private allowedDomains: string[]

  constructor(domains?: string[]) {
    this.allowedDomains = domains || getAllowedEmbedDomains()

    if (isDebugEnabled()) {
      console.log('[DomainValidator] Initialized with domains:', this.allowedDomains)
    }
  }

  /**
   * 验证域名是否被允许
   */
  isAllowed(origin: string | null): boolean {
    if (!origin) return false

    try {
      const url = new URL(origin)
      const fullOrigin = `${url.protocol}//${url.hostname}`
      return this.allowedDomains.includes(fullOrigin)
    } catch {
      return false
    }
  }

  /**
   * 验证 Referer 头
   */
  isRefererAllowed(referer: string | null): boolean {
    if (!referer) return false

    try {
      const url = new URL(referer)
      const origin = `${url.protocol}//${url.hostname}`
      return this.allowedDomains.includes(origin)
    } catch {
      return false
    }
  }

  /**
   * 获取允许的域名列表
   */
  getAllowedDomains(): string[] {
    return [...this.allowedDomains]
  }

  /**
   * 添加允许的域名
   */
  addAllowedDomain(domain: string): void {
    if (!this.allowedDomains.includes(domain)) {
      this.allowedDomains.push(domain)
    }
  }

  /**
   * 移除允许的域名
   */
  removeAllowedDomain(domain: string): void {
    this.allowedDomains = this.allowedDomains.filter(d => d !== domain)
  }

  /**
   * 检查域名是否为本地开发环境
   */
  isLocalDevelopment(origin: string | null): boolean {
    if (!origin) return false
    
    const localPatterns = [
      'http://localhost',
      'http://127.0.0.1',
      'http://0.0.0.0'
    ]
    
    return localPatterns.some(pattern => origin.startsWith(pattern))
  }

  /**
   * 验证域名（包含开发环境支持）
   */
  isAllowedWithDev(origin: string | null): boolean {
    if (isDevelopment() && this.isLocalDevelopment(origin)) {
      if (isDebugEnabled()) {
        console.log('[DomainValidator] Allowing local development domain:', origin)
      }
      return true
    }

    const isAllowed = this.isAllowed(origin)
    if (isDebugEnabled()) {
      console.log(`[DomainValidator] Domain ${origin} allowed: ${isAllowed}`)
    }

    return isAllowed
  }
}

// 单例实例
export const domainValidator = new DomainValidator()
