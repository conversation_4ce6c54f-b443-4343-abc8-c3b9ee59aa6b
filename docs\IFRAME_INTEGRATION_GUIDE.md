# SpecificAI Iframe 集成指南

## 概述

SpecificAI 提供了一套完整的 iframe 嵌入解决方案，支持多语言国际化、实时流水线展示和安全认证。本文档详细说明了如何在外部系统中集成和使用这些 iframe 组件。

## 🌍 支持的语言

- **中文 (zh)**: 默认语言
- **英文 (en)**: 完整支持
- **日文 (ja)**: 完整支持

## 📋 可用页面

### 1. Dashboard 页面 (推荐)

实时流水线展示页面，支持自动循环执行和国际化显示。

### 2. Report 页面

报告展示页面。

### 3. Test 页面

测试和调试页面。

## 🔗 URL 格式

### 基础格式

```
https://your-domain.com/{locale}/embed/{page}?token={JWT_TOKEN}&{additional_params}
```

### 参数说明

#### 必需参数

- `{locale}`: 语言代码 (`zh` | `en` | `ja`)
- `{page}`: 页面类型 (`dashboard` | `report` | `test`)
- `token`: JWT认证令牌

#### 可选参数

- `title`: 项目标题，用于显示在流水线状态中
- `from-og`: 是否来自Vue3旧项目 (`true` | `false`)

### 示例URL

#### 英文Dashboard页面

```
https://dev.goglobalsp.com/en/embed/dashboard?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&title=Government%20Procurement%20Project
```

#### 中文Dashboard页面

```
https://dev.goglobalsp.com/zh/embed/dashboard?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&title=政府采购项目
```

#### 日文Dashboard页面

```
https://dev.goglobalsp.com/ja/embed/dashboard?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&title=政府調達プロジェクト
```

## 🔐 认证机制

### JWT Token 格式

当前用户的token

## 📨 PostMessage 通信协议

iframe 会通过 `window.postMessage` 与父窗口进行通信，发送以下类型的消息：

### 1. 页面加载完成

```javascript
{
  type: 'PAGE_LOADED',
  data: {
    timestamp: 1703123456789,
    url: 'https://dev.goglobalsp.com/en/embed/dashboard?token=...',
    fromOg: false,
    authMode: 'nextjs-standard'
  }
}
```

### 2. iframe 就绪

```javascript
{
  type: 'IFRAME_READY',
  data: {
    timestamp: '2025-06-23T09:51:06.700Z',
    url: 'https://dev.goglobalsp.com/en/embed/dashboard?token=...',
    fromOg: false,
    authMode: 'nextjs-standard',
    message: 'Next.js标准iframe就绪，用户认证完成'
  }
}
```

### 3. 高度调整

```javascript
{
  type: 'RESIZE',
  data: {
    height: 972  // 新的高度值（像素）
  }
}
```

### 4. API 调用状态

```javascript
// API 调用开始
{
  type: 'API_CALL',
  data: {
    method: 'GET',
    url: '/api/user/get_current',
    timestamp: '2025-06-23T09:51:05.642Z',
    token: '已设置'
  }
}

// API 调用成功
{
  type: 'API_SUCCESS',
  data: {
    url: '/api/user/get_current',
    message: '用户信息获取成功',
    user: 'ricky'
  }
}
```

### 5. 错误处理

```javascript
// 认证错误
{
  type: 'AUTH_ERROR',
  data: {
    message: '未提供有效的认证token',
    tokenStatus: '未设置',
    fromOg: false
  }
}

// API 错误
{
  type: 'API_ERROR',
  data: {
    url: '/api/user/get_current',
    message: '获取用户信息失败',
    tokenStatus: '已设置'
  }
}

// 一般错误
{
  type: 'ERROR',
  data: {
    message: 'Script error.',
    filename: 'https://dev.goglobalsp.com/_next/static/chunks/app/layout.js',
    lineno: 1,
    colno: 2345
  }
}
```

## 🎯 Dashboard 页面特性

### 流水线展示

Dashboard 页面会自动展示一个完整的AI流水线执行过程，包括：

1. **数据收集阶段**
   - 招投标数据采集器
   - 数据验证和清洗

2. **AI分析阶段**
   - AI摘要生成引擎
   - 智能内容分析

3. **报告生成阶段**
   - 报告编译器
   - 格式化输出

### 国际化支持

所有文本内容都会根据URL中的语言参数自动切换：

#### 中文 (zh)

- "获取招投标数据"
- "生成招标摘要"
- "招投标数据采集器"

#### 英文 (en)

- "Collect Bidding Data"
- "Generate Bidding Summary"
- "Bidding Data Collector"

#### 日文 (ja)

- "入札データの収集"
- "入札要約の生成"
- "入札データコレクター"

## 🔧 技术规格

### 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### 性能要求

- 最小宽度: 320px
- 推荐宽度: 800px+
- 初始高度: 600px (会自动调整)
- 内存占用: ~50MB

### 网络要求

- 稳定的互联网连接
- 支持 WebSocket (用于实时更新)
- 允许跨域请求到 `dev.goglobalsp.com`

## 🚨 错误处理

### 常见错误及解决方案

#### 1. 认证失败

**错误信息**: `AUTH_ERROR: 未提供有效的认证token`
**解决方案**:

- 检查 JWT Token 是否有效
- 确认 Token 未过期
- 联系技术支持获取新的 Token

#### 2. 网络连接问题

**错误信息**: `API_ERROR: 获取用户信息失败`
**解决方案**:

- 检查网络连接
- 确认防火墙设置允许访问 `dev.goglobalsp.com`
- 检查代理服务器配置

#### 3. 跨域问题

**错误信息**: `CORS policy: No 'Access-Control-Allow-Origin' header`
**解决方案**:

- 确保从允许的域名访问
- 联系技术支持添加您的域名到白名单

## 📞 技术支持

### 联系方式

- **邮箱**: <<EMAIL>>
- **技术文档**: <https://docs.specificai.com>
- **状态页面**: <https://status.specificai.com>

### 支持时间

- **工作日**: 9:00 - 18:00 (UTC+8)
- **紧急支持**: 24/7 (仅限生产环境问题)

### 版本信息

- **当前版本**: v2.1.0
- **最后更新**: 2025-06-23
- **兼容性**: Next.js 14+, React 18+

---

*本文档会持续更新，请定期检查最新版本。*
