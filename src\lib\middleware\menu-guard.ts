/**
 * 菜单守卫
 * 处理菜单权限控制和重定向
 */

import { NextRequest, NextResponse } from 'next/server'
import { buildGuardContext } from './context-builder'
import { isRouteInMenu, getFirstAccessibleRoute, routeToMenuMap } from '../route-config'
import type { MiddlewareFunction } from './types'

/**
 * 菜单守卫中间件
 * 检查用户菜单权限并处理相应的重定向
 */
export const menuGuard: MiddlewareFunction = async (
  request: NextRequest,
  next: () => Promise<NextResponse>
): Promise<NextResponse> => {
  const context = await buildGuardContext(request)
  const { user, menuConfig, pathname, routeConfig } = context

  // 跳过非菜单路由
  if (!isRouteInMenu(pathname)) {
    return await next()
  }

  // 跳过认证相关路由
  if (routeConfig?.requiresGuest) {
    return await next()
  }

  // 如果用户未登录，跳过菜单检查（由认证守卫处理）
  if (!user) {
    return await next()
  }

  // 检查是否有启用的菜单
  const hasEnabledMenu = menuConfig.some(menu => menu.status)
  if (!hasEnabledMenu) {
    console.warn(`[MenuGuard] No enabled menus for user, redirect to landing`)
    return NextResponse.redirect(new URL('/landing', request.url))
  }

  // 查找当前路由对应的菜单ID
  let menuId = routeToMenuMap[pathname]

  // 处理嵌套路由
  if (!menuId) {
    for (const [route, id] of Object.entries(routeToMenuMap)) {
      if (pathname.startsWith(`${route}/`)) {
        menuId = id
        break
      }
    }
  }

  // 如果找不到对应的菜单ID，允许访问（可能是动态路由）
  if (!menuId) {
    return await next()
  }

  // 检查菜单是否被禁用
  const menuItem = menuConfig.find(item => item.id === menuId)
  if (!menuItem?.status) {
    const firstAccessibleRoute = getFirstAccessibleRoute(menuConfig)
    if (firstAccessibleRoute) {
      console.warn(`[MenuGuard] Menu ${menuId} disabled, redirect to ${firstAccessibleRoute}`)
      return NextResponse.redirect(new URL(firstAccessibleRoute, request.url))
    } else {
      console.warn(`[MenuGuard] No accessible routes, redirect to landing`)
      return NextResponse.redirect(new URL('/landing', request.url))
    }
  }

  return await next()
}

/**
 * 创建自定义菜单守卫
 * @param options 菜单守卫选项
 * @returns 自定义菜单守卫
 */
export function createMenuGuard(options: {
  enableLogging?: boolean
  customMenuChecker?: (menuId: string, menuConfig: any[]) => boolean
  defaultRedirect?: string
  skipPaths?: string[]
}): MiddlewareFunction {
  const {
    enableLogging = false,
    customMenuChecker,
    defaultRedirect = '/landing',
    skipPaths = []
  } = options

  return async (request: NextRequest, next: () => Promise<NextResponse>) => {
    const context = await buildGuardContext(request)
    const { user, menuConfig, pathname, routeConfig } = context

    // 检查是否跳过此路径
    if (skipPaths.some(path => pathname.startsWith(path))) {
      return await next()
    }

    if (!isRouteInMenu(pathname) || routeConfig?.requiresGuest || !user) {
      return await next()
    }

    const hasEnabledMenu = menuConfig.some(menu => menu.status)
    if (!hasEnabledMenu) {
      if (enableLogging) {
        console.log(`[CustomMenuGuard] No enabled menus: ${pathname} -> ${defaultRedirect}`)
      }
      return NextResponse.redirect(new URL(defaultRedirect, request.url))
    }

    let menuId = routeToMenuMap[pathname]
    if (!menuId) {
      for (const [route, id] of Object.entries(routeToMenuMap)) {
        if (pathname.startsWith(`${route}/`)) {
          menuId = id
          break
        }
      }
    }

    if (!menuId) {
      return await next()
    }

    // 使用自定义菜单检查器或默认检查器
    const hasMenuAccess = customMenuChecker
      ? customMenuChecker(menuId, menuConfig)
      : menuConfig.some(item => item.id === menuId && item.status)

    if (!hasMenuAccess) {
      const firstAccessibleRoute = getFirstAccessibleRoute(menuConfig)
      const redirectPath = firstAccessibleRoute || defaultRedirect
      
      if (enableLogging) {
        console.log(`[CustomMenuGuard] Menu access denied: ${pathname} -> ${redirectPath}`)
      }
      return NextResponse.redirect(new URL(redirectPath, request.url))
    }

    return await next()
  }
}

/**
 * 菜单权限检查器
 * 提供更细粒度的菜单权限控制
 */
export class MenuPermissionChecker {
  private menuConfig: Array<{ id: string; status: boolean }>

  constructor(menuConfig: Array<{ id: string; status: boolean }>) {
    this.menuConfig = menuConfig
  }

  /**
   * 检查菜单是否可访问
   */
  isMenuAccessible(menuId: string): boolean {
    return this.menuConfig.some(item => item.id === menuId && item.status)
  }

  /**
   * 获取所有可访问的菜单
   */
  getAccessibleMenus(): string[] {
    return this.menuConfig
      .filter(item => item.status)
      .map(item => item.id)
  }

  /**
   * 检查是否有任何可访问的菜单
   */
  hasAnyAccessibleMenu(): boolean {
    return this.menuConfig.some(item => item.status)
  }

  /**
   * 获取菜单访问统计
   */
  getMenuStats(): {
    total: number
    accessible: number
    disabled: number
  } {
    const total = this.menuConfig.length
    const accessible = this.menuConfig.filter(item => item.status).length
    const disabled = total - accessible

    return { total, accessible, disabled }
  }
}

/**
 * 菜单守卫工具函数
 */
export const menuGuardUtils = {
  /**
   * 检查路径是否需要菜单权限
   */
  requiresMenuPermission(pathname: string): boolean {
    return isRouteInMenu(pathname)
  },

  /**
   * 获取路径对应的菜单ID
   */
  getMenuIdForPath(pathname: string): string | null {
    let menuId = routeToMenuMap[pathname]
    
    if (!menuId) {
      for (const [route, id] of Object.entries(routeToMenuMap)) {
        if (pathname.startsWith(`${route}/`)) {
          menuId = id
          break
        }
      }
    }

    return menuId || null
  },

  /**
   * 检查菜单配置是否有效
   */
  isValidMenuConfig(menuConfig: any[]): boolean {
    return Array.isArray(menuConfig) && 
           menuConfig.length > 0 && 
           menuConfig.every(item => 
             typeof item === 'object' && 
             typeof item.id === 'string' && 
             typeof item.status === 'boolean'
           )
  },

  /**
   * 过滤可访问的菜单项
   */
  filterAccessibleMenus(menuConfig: Array<{ id: string; status: boolean }>): Array<{ id: string; status: boolean }> {
    return menuConfig.filter(item => item.status)
  },

  /**
   * 创建菜单权限映射
   */
  createMenuPermissionMap(menuConfig: Array<{ id: string; status: boolean }>): Record<string, boolean> {
    const map: Record<string, boolean> = {}
    menuConfig.forEach(item => {
      map[item.id] = item.status
    })
    return map
  },

  /**
   * 验证菜单访问权限
   */
  validateMenuAccess(
    pathname: string, 
    menuConfig: Array<{ id: string; status: boolean }>
  ): {
    hasAccess: boolean
    menuId: string | null
    reason?: string
  } {
    const menuId = menuGuardUtils.getMenuIdForPath(pathname)
    
    if (!menuId) {
      return { hasAccess: true, menuId: null, reason: 'No menu required' }
    }

    const menuItem = menuConfig.find(item => item.id === menuId)
    if (!menuItem) {
      return { hasAccess: false, menuId, reason: 'Menu not found' }
    }

    if (!menuItem.status) {
      return { hasAccess: false, menuId, reason: 'Menu disabled' }
    }

    return { hasAccess: true, menuId }
  }
}
