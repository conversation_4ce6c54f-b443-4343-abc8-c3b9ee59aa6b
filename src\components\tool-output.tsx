"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON>er<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DrawerTrigger } from "@/components/ui/drawer"
import { ScrollArea } from "@/components/ui/scroll-area"
import type { ToolOutput as ToolOutputType } from "./pipeline-dashboard"
import { Clock, CheckCircle, AlertCircle, Loader2, Bot, Cloud, ChevronRight } from "lucide-react"
import { useMediaQuery } from "@/hooks/use-media-query"

interface ToolOutputProps {
  tool: ToolOutputType
}

const statusConfig = {
  pending: {
    color: "bg-slate-100 text-slate-600 border-slate-200",
    icon: Clock,
  },
  running: {
    color: "bg-blue-100 text-blue-600 border-blue-200",
    icon: Loader2,
  },
  completed: {
    color: "bg-emerald-100 text-emerald-600 border-emerald-200",
    icon: CheckCircle,
  },
  error: {
    color: "bg-red-100 text-red-600 border-red-200",
    icon: AlertCircle,
  },
}

const providerIcons = {
  openai: Bot,
  azure: Cloud,
  default: Bot,
}

export function ToolOutput({ tool }: ToolOutputProps) {
  const [open, setOpen] = useState(false)
  const isDesktop = useMediaQuery("(min-width: 768px)")

  const config = statusConfig[tool.status]
  const StatusIcon = config.icon
  const ProviderIcon = providerIcons[(tool.provider || 'default') as keyof typeof providerIcons] || providerIcons.default

  const formatTime = (timestamp?: string) => {
    if (!timestamp) return "N/A"
    return new Date(timestamp).toLocaleTimeString("en-US", {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    })
  }

  const getDuration = () => {
    if (!tool.finishedAt || !tool.startedAt) return null
    const start = new Date(tool.startedAt)
    const end = new Date(tool.finishedAt)
    const duration = Math.round((end.getTime() - start.getTime()) / 1000)
    return `${duration}s`
  }

  const ToolDetails = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3 pb-4 border-b border-slate-200">
        <div className="p-2 rounded-lg bg-slate-100">
          <ProviderIcon className="w-5 h-5 text-slate-600" />
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-slate-900">{tool.name}</h3>
          <p className="text-sm text-slate-500 capitalize">{tool.provider || 'unknown'} provider</p>
        </div>
        <Badge variant="outline" className={config.color}>
          <StatusIcon className={`w-3 h-3 mr-1 ${tool.status === "running" ? "animate-spin" : ""}`} />
          {tool.status}
        </Badge>
      </div>

      {/* Metadata Grid */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-1">
          <span className="text-xs font-medium text-slate-500 uppercase tracking-wide">Output Type</span>
          <p className="text-sm font-mono bg-slate-50 px-2 py-1 rounded">{tool.outputType || "N/A"}</p>
        </div>
        <div className="space-y-1">
          <span className="text-xs font-medium text-slate-500 uppercase tracking-wide">Duration</span>
          <p className="text-sm font-mono bg-slate-50 px-2 py-1 rounded">{getDuration() || "Running..."}</p>
        </div>
        <div className="space-y-1">
          <span className="text-xs font-medium text-slate-500 uppercase tracking-wide">Started</span>
          <p className="text-sm font-mono bg-slate-50 px-2 py-1 rounded">{formatTime(tool.startedAt)}</p>
        </div>
        <div className="space-y-1">
          <span className="text-xs font-medium text-slate-500 uppercase tracking-wide">Finished</span>
          <p className="text-sm font-mono bg-slate-50 px-2 py-1 rounded">
            {tool.finishedAt ? formatTime(tool.finishedAt) : "In progress"}
          </p>
        </div>
      </div>

      {/* Output Preview */}
      {tool.outputSnippet && (
        <div className="space-y-2">
          <span className="text-xs font-medium text-slate-500 uppercase tracking-wide">Output Preview</span>
          <div className="p-4 bg-slate-50 rounded-lg border">
            <p className="text-sm text-slate-700 font-mono leading-relaxed">{tool.outputSnippet}</p>
          </div>
        </div>
      )}

      {/* Full Output */}
      {tool.outputPayload && (
        <div className="space-y-2">
          <span className="text-xs font-medium text-slate-500 uppercase tracking-wide">Full Output</span>
          <ScrollArea className="h-64 w-full rounded-lg border bg-slate-50">
            <div className="p-4">
              {Array.isArray(tool.outputPayload) ? (
                <ul className="space-y-2">
                  {tool.outputPayload.map((item: any, index: number) => (
                    <motion.li
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className="flex items-start gap-3 text-sm"
                    >
                      <span className="text-slate-400 mt-1">•</span>
                      <span className="text-slate-700 leading-relaxed">{String(item)}</span>
                    </motion.li>
                  ))}
                </ul>
              ) : (
                <pre className="whitespace-pre-wrap text-xs text-slate-700 leading-relaxed font-mono">
                  {String(tool.outputPayload)}
                </pre>
              )}
            </div>
          </ScrollArea>
        </div>
      )}
    </div>
  )

  const TriggerButton = () => (
    <motion.div whileHover={{ scale: 1.01 }} whileTap={{ scale: 0.99 }} className="w-full">
      <Button
        variant="ghost"
        size="sm"
        className="w-full justify-between h-auto p-3 text-left hover:bg-slate-50 transition-colors duration-200"
        onClick={() => setOpen(true)}
      >
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <ProviderIcon className="w-4 h-4 text-slate-500 flex-shrink-0" />
          <div className="min-w-0 flex-1">
            <p className="font-medium text-sm truncate text-slate-900">{tool.name}</p>
            <p className="text-xs text-slate-500 truncate">{tool.outputSnippet || 'No preview available'}</p>
          </div>
        </div>
        <div className="flex items-center gap-2 flex-shrink-0">
          <Badge variant="outline" className={`${config.color} text-xs border-0`}>
            <StatusIcon className={`w-3 h-3 mr-1 ${tool.status === "running" ? "animate-spin" : ""}`} />
            {tool.status}
          </Badge>
          <ChevronRight className="w-3 h-3 text-slate-400" />
        </div>
      </Button>
    </motion.div>
  )

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <TriggerButton />
        </DialogTrigger>
        <DialogContent className="max-w-3xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>Tool Execution Details</DialogTitle>
          </DialogHeader>
          <ScrollArea className="max-h-[60vh]">
            <ToolDetails />
          </ScrollArea>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <TriggerButton />
      </DrawerTrigger>
      <DrawerContent className="max-h-[85vh]">
        <DrawerHeader>
          <DrawerTitle>Tool Execution Details</DrawerTitle>
        </DrawerHeader>
        <ScrollArea className="px-4 pb-4 max-h-[70vh]">
          <ToolDetails />
        </ScrollArea>
      </DrawerContent>
    </Drawer>
  )
}
