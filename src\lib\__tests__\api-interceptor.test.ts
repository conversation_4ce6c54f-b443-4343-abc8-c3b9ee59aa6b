/**
 * API拦截器测试
 */

import { requestInterceptor } from '../api-interceptor'

// Mock window.location
const mockLocation = {
  pathname: '/en/dashboard',
  href: 'http://localhost:3000/en/dashboard'
}

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
}

// Mock tokenManager
const mockTokenManager = {
  getToken: jest.fn(() => 'mock-jwt-token'),
  setToken: jest.fn(),
  removeTokens: jest.fn()
}

// Setup mocks
Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true
})

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
  writable: true
})

// Mock the token module
jest.mock('@/lib/token', () => ({
  tokenManager: mockTokenManager
}))

describe('API拦截器测试', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue(null)
  })

  describe('requestInterceptor', () => {
    it('应该从URL路径获取语言设置', () => {
      // 设置URL路径为英文
      mockLocation.pathname = '/en/dashboard'
      
      const { options } = requestInterceptor('/api/test')
      
      expect(options.headers).toEqual(
        expect.objectContaining({
          'Content-Type': 'application/json',
          'language': 'english',
          'authorization': 'mock-jwt-token'
        })
      )
    })

    it('应该从URL路径获取中文语言设置', () => {
      // 设置URL路径为中文
      mockLocation.pathname = '/zh/dashboard'
      
      const { options } = requestInterceptor('/api/test')
      
      expect(options.headers).toEqual(
        expect.objectContaining({
          'language': 'chinese'
        })
      )
    })

    it('应该从URL路径获取日文语言设置', () => {
      // 设置URL路径为日文
      mockLocation.pathname = '/ja/dashboard'
      
      const { options } = requestInterceptor('/api/test')
      
      expect(options.headers).toEqual(
        expect.objectContaining({
          'language': 'japanese'
        })
      )
    })

    it('应该从localStorage获取语言设置（当URL无语言前缀时）', () => {
      // 设置URL路径无语言前缀
      mockLocation.pathname = '/dashboard'
      // 设置localStorage语言偏好
      mockLocalStorage.getItem.mockReturnValue('english')
      
      const { options } = requestInterceptor('/api/test')
      
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('user-language')
      expect(options.headers).toEqual(
        expect.objectContaining({
          'language': 'english'
        })
      )
    })

    it('应该使用默认中文（当URL和localStorage都无语言设置时）', () => {
      // 设置URL路径无语言前缀
      mockLocation.pathname = '/dashboard'
      // localStorage返回null
      mockLocalStorage.getItem.mockReturnValue(null)
      
      const { options } = requestInterceptor('/api/test')
      
      expect(options.headers).toEqual(
        expect.objectContaining({
          'language': 'chinese'
        })
      )
    })

    it('应该添加认证头', () => {
      const { options } = requestInterceptor('/api/test')
      
      expect(mockTokenManager.getToken).toHaveBeenCalled()
      expect(options.headers).toEqual(
        expect.objectContaining({
          'authorization': 'mock-jwt-token'
        })
      )
    })

    it('应该保留现有的请求头', () => {
      const existingHeaders = {
        'Custom-Header': 'custom-value',
        'Another-Header': 'another-value'
      }
      
      const { options } = requestInterceptor('/api/test', {
        headers: existingHeaders
      })
      
      expect(options.headers).toEqual(
        expect.objectContaining({
          'Content-Type': 'application/json',
          'language': 'english',
          'authorization': 'mock-jwt-token',
          'Custom-Header': 'custom-value',
          'Another-Header': 'another-value'
        })
      )
    })

    it('应该处理token获取失败的情况', () => {
      // Mock token获取失败
      mockTokenManager.getToken.mockImplementation(() => {
        throw new Error('Token获取失败')
      })
      
      const { options } = requestInterceptor('/api/test')
      
      // 应该仍然包含其他头，但不包含authorization
      expect(options.headers).toEqual(
        expect.objectContaining({
          'Content-Type': 'application/json',
          'language': 'english'
        })
      )
      expect(options.headers).not.toHaveProperty('authorization')
    })

    it('应该处理无效URL路径的情况', () => {
      // 设置无效的URL路径
      mockLocation.pathname = '/invalid/path/with/unknown/language'
      mockLocalStorage.getItem.mockReturnValue(null)
      
      const { options } = requestInterceptor('/api/test')
      
      // 应该使用默认中文
      expect(options.headers).toEqual(
        expect.objectContaining({
          'language': 'chinese'
        })
      )
    })
  })
})
