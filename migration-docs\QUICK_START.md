# 🚀 快速开始 - 通用Iframe嵌入框架 (完整认证集成)

## 📋 测试步骤

### 1️⃣ 启动开发服务器

```bash
npm install
npm run dev
```

### 2️⃣ 打开测试工具

在浏览器中访问：**http://localhost:3000/iframe-test.html**

### 3️⃣ 使用测试界面

1. 确认环境选择为 "Development"
2. **使用真实JWT Token** - 从您的项目认证系统获取
3. 点击 "加载 Iframe" 按钮
4. 观察嵌入的 dashboard 页面加载
5. 查看底部的实时消息日志

## 🎯 核心功能

- ✅ **完整认证集成** - 使用项目现有的`tokenManager`和请求拦截器
- ✅ **通用Iframe容器** - 任何页面组件都可以直接嵌入
- ✅ **现有API集成** - 使用项目现有的认证hooks (`useAuthApi.useCurrentUserAuto`)
- ✅ **智能Token处理** - 从URL参数获取token并设置到`tokenManager`，避免重复请求
- ✅ **请求拦截器兼容** - 完全遵循项目的请求/响应拦截器逻辑
- ✅ **通信机制** - postMessage 消息日志（包含API调用状态）
- ✅ **智能高度调整** - 防止无限循环的自动高度调整
- ✅ **错误处理** - 认证失败等错误显示
- ✅ **模块化设计** - 页面组件与iframe逻辑完全分离

## 🔧 最新修复 (v2.0)

### 🚫 解决重复API请求问题
- ✅ **状态控制** - 添加`tokenInitialized`和`hasValidToken`状态
- ✅ **条件执行** - 只有在token有效时才执行API调用
- ✅ **依赖优化** - 精确控制useEffect的依赖项
- ✅ **单次执行** - 确保token设置只执行一次

### 🚫 解决iframe高度无限增长问题
- ✅ **阈值控制** - 只有高度变化超过20px才调整
- ✅ **防抖处理** - 200ms延时避免频繁调整
- ✅ **高度记录** - 跟踪上次高度避免重复通知
- ✅ **延时初始化** - 500ms延时确保DOM完全渲染
- ✅ **调试日志** - 显示详细的高度调整过程

### 📊 优化后的行为
```
Token设置: 只执行一次 ✅
API请求: 只在有效token时执行 ✅
高度调整: 智能防抖，避免无限循环 ✅
```

## 🏗️ 认证架构设计

### Token处理流程
```
URL参数: ?token=JWT_TOKEN
    ↓
IframeContainer提取token (一次性)
    ↓
tokenManager.setToken(token) + hasValidToken=true
    ↓
现有API hooks (仅当hasValidToken=true时)
    ↓
useApi → api-interceptor → 自动添加Authorization header
    ↓
GET /api/user/get_current (带正确认证头，仅一次)
```

### 高度调整优化
```
ResizeObserver触发
    ↓
防抖延时200ms
    ↓
检查高度变化 > 20px ?
    ↓ (是)
记录新高度 + 通知父窗口
    ↓ (否)
忽略微小变化，防止无限循环
```

## 🔧 如何添加新页面

### 1. 创建页面组件
```typescript
// src/components/embed/pages/NewPage.tsx
import type { User } from '@/stores/auth-store'

interface NewPageProps {
  user?: User
  userLoading?: boolean
  userError?: any
}

export default function NewPage({ user }: NewPageProps) {
  return (
    <div>
      <h1>新页面</h1>
      <p>用户: {user?.user_name}</p>
      {/* 你的页面内容 */}
    </div>
  )
}
```

### 2. 在路由中添加
```typescript
// src/app/embed/[page]/page.tsx
import NewPage from '@/components/embed/pages/NewPage'

const renderPageComponent = () => {
  if (page === 'dashboard') return <DashboardPage />
  if (page === 'new-page') return <NewPage />  // 添加这行
  
  return <div>页面不支持</div>
}
```

### 3. 完成！
新页面自动获得：
- ✅ 完整的项目认证系统
- ✅ 优化的API调用（无重复请求）
- ✅ 智能iframe通信
- ✅ 智能高度调整（无无限循环）
- ✅ 错误处理

## 🔧 核心组件

### IframeContainer (通用容器)
- **职责**: 认证、通信、错误处理
- **Token处理**: 从URL参数提取并设置到`tokenManager`（一次性）
- **API集成**: 使用现有的`useAuthApi.useCurrentUserAuto`（条件执行）
- **高度管理**: 智能防抖和阈值控制
- **拦截器兼容**: 完全遵循项目的请求/响应拦截器
- **输入**: 任何React组件
- **输出**: 增强的iframe组件，自动注入用户信息

### tokenManager 集成
- **URL Token提取**: `?token=JWT_TOKEN` → `tokenManager.setToken()`（一次性）
- **状态管理**: `hasValidToken`状态控制API调用
- **自动认证**: 现有API hooks自动使用设置的token
- **请求头注入**: `Authorization: Bearer {token}` 自动添加
- **环境隔离**: 支持dev/prod环境不同的token存储

### 页面组件 (纯展示)
- **职责**: 纯粹的展示逻辑
- **输入**: 用户信息作为props
- **特点**: 无需关心认证、通信等逻辑

## 📝 集成到你的项目

### Vue 3 项目

```vue
<template>
  <iframe :src="iframeUrl" style="width: 100%; border: none;" />
</template>

<script setup>
// 使用真实的JWT Token
const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
const iframeUrl = `http://localhost:3000/embed/dashboard?token=${token}`

// 监听认证状态和高度调整
window.addEventListener('message', (event) => {
  if (event.data.type === 'API_SUCCESS') {
    console.log('用户认证成功:', event.data.data.user)
  }
  if (event.data.type === 'RESIZE') {
    console.log('高度调整:', event.data.data.height)
  }
  if (event.data.type === 'AUTH_ERROR') {
    console.error('认证失败:', event.data.data.message)
  }
})
</script>
```

### React 项目

```jsx
function App() {
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' // 真实JWT Token
  
  useEffect(() => {
    const handleMessage = (event) => {
      if (event.data.type === 'API_SUCCESS') {
        console.log('用户认证成功:', event.data.data.user)
      }
      if (event.data.type === 'RESIZE') {
        console.log('高度调整:', event.data.data.height)
      }
      if (event.data.type === 'AUTH_ERROR') {
        console.error('认证失败:', event.data.data.message)
      }
    }
    
    window.addEventListener('message', handleMessage)
    return () => window.removeEventListener('message', handleMessage)
  }, [])

  return (
    <iframe 
      src={`http://localhost:3000/embed/dashboard?token=${token}`}
      style={{ width: '100%', border: 'none' }}
    />
  )
}
```

## 🌐 URL 格式

```
http://localhost:3000/embed/{page-name}?token={JWT_TOKEN}
```

**重要**: token必须是有效的JWT token，与您的项目认证系统兼容。

支持的页面：
- `dashboard` - 仪表板页面

## 💡 设计优势

- **完全集成** - 使用项目现有的所有认证基础设施
- **性能优化** - 避免重复API请求和无限高度调整
- **Token管理** - 智能处理URL token到项目token管理器的桥接
- **拦截器兼容** - 所有请求都经过项目的请求/响应拦截器
- **分离关注点** - 页面组件只关心展示，容器负责基础设施
- **复用性强** - 一个容器支持无限页面
- **维护性好** - 修改认证逻辑只需改一个地方
- **扩展性强** - 添加新页面极其简单
- **类型安全** - 完整的TypeScript支持
- **现有集成** - 100%使用项目现有的API hooks和token管理

## 🔧 故障排除

1. **Token格式错误**: 确保使用有效的JWT token，与项目认证系统兼容
2. **认证失败**: 检查token是否过期或无效
3. **重复API请求**: 已修复 - 使用状态控制确保单次执行
4. **高度无限增长**: 已修复 - 使用防抖和阈值控制
5. **API调用失败**: 查看控制台中的API请求日志和token状态
6. **请求头缺失**: 确认tokenManager已正确设置token
7. **iframe不显示**: 检查路由配置和页面组件
8. **通信失败**: 查看测试工具的消息日志

### 调试Token状态
测试工具会显示：
- ✅ **Token状态**: "已设置" / "未设置"
- ✅ **API调用计数**: 现在只会调用一次
- ✅ **高度调整日志**: 显示详细的高度变化过程
- ✅ **认证错误**: 会显示token状态

### 性能监控
- **API请求**: 每个token只调用一次 `/api/user/get_current`
- **高度调整**: 只有>20px变化才触发，防抖200ms
- **内存泄漏**: 正确清理所有监听器和定时器

---

**现在你有了一个完全优化的iframe嵌入框架！** 🎉

**修复了重复请求和高度无限增长问题，确保最佳性能！**
