/**
 * 用户认证状态管理
 * 基于 Zustand 的全局状态管理
 */

import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
// 使用静态导入替代动态导入，避免触发 Fast Refresh 循环
import { authAPI } from '@/lib/auth-api'
import { tokenManager } from '@/lib/token'

// API 响应的用户接口（来自后端）
export interface ApiUser {
  id: string
  user_name: string
  nickname?: string
  email: string
  avatar?: string
  user_type: number
  projects: string[]
  nation: string[]
  industry: string[]
  default_project: {
    id: string
  }
  company: {
    id: string
    name: string
    nations: string[]
    industries: string[]
    invite_code: string
    language: string
    contact: string
    employees_limit: number
    token_limit: number
    is_pay: boolean
    is_delete: boolean
    config: {
      members: number
      tokens: number | 'unlimited'
      information_data: string
      pdf_report: string
      permissions: {
        pdf_setting: boolean
        pdf_review: boolean
        pdf_download: boolean
      }
      plan: string
      period: string
    }
    menu: {
      chat_bot: boolean
      daily_report: boolean
      dashboard: boolean
      format_report: boolean
      information: boolean
      procurement: boolean
    }
    feature: {
      risk_monitoring_intelligence_officer: boolean
      overseas_market_intelligence_officer: boolean
      business_opportunity_intelligence_officer: boolean
      strategic: boolean
    }
    step: number
    user_profile: Record<string, any>
    others: Record<string, any>
    created_at: string
    updated_at: string
  }
  step: number
  language: 'chinese' | 'english' | 'japanese'
  user_profile: {
    industry?: string[]
    industry_value?: string
    area?: string[]
    area_value?: string
    style?: string[]
    style_value?: string
    overseas?: string[]
    overseas_value?: string
    project_recommendation?: string[]
    industry_recommendation?: string[]
    target_market_recommendation?: string[]
    user_profile_summary?: string
    topic_of_interest?: {
      topic_of_interest: string[]
    }
    industry_first_recommendation?: string[]
    industry_second_recommendation?: string[]
  }
  tour_config: Array<{ id: string; tour_completed: boolean }>
  others: Record<string, any>
  created_at: string
  updated_at: string
  is_delete: boolean
}

export type UserType = 'admin' | 'user' | 'guest'

export interface MenuConfig {
  id: string
  status: boolean
}

export interface FeatureConfig {
  id: string
  status: boolean
}

export interface UserRole {
  id: string
  roleCode: string
  roleName: string
}

export interface UserPermission {
  id: string
  code: string
  name: string
  resource: string
  action: string
}

export interface AccessParams {
  features?: string[]
  roles?: string[]
  permissions?: string[]
  rightSchemeType?: string[]
  fallback?: string[]
}

export type LoginType = 'password' | 'sms' | 'email'

// 基础登录参数接口
export interface BaseLoginParams {
  remember?: boolean
}

// 密码登录参数（用户名和密码必填）
export interface PasswordLoginParams extends BaseLoginParams {
  username: string
  password: string
}

// 短信登录参数
export interface SmsLoginParams extends BaseLoginParams {
  phone: string
  code: string
}

// 邮箱登录参数
export interface EmailLoginParams extends BaseLoginParams {
  email: string
  code: string
}

// 联合类型，根据登录类型确定参数
export type LoginParams = PasswordLoginParams | SmsLoginParams | EmailLoginParams

// 向后兼容的通用登录参数（仅用于API层）
export interface LegacyLoginParams {
  username?: string
  email?: string
  password?: string
  phone?: string
  code?: string
  remember?: boolean
}

export interface AuthState {
  // 基础状态
  user: ApiUser | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null

  // 权限配置
  menuConfig: MenuConfig[]
  featureConfig: FeatureConfig[]
  userRoles: UserRole[]
  userPermissions: UserPermission[]

  // 基础方法
  setUser: (user: ApiUser) => void
  clearAuth: () => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void

  // 权限数据设置方法
  setMenuConfig: (config: MenuConfig[]) => void
  setFeatureConfig: (config: FeatureConfig[]) => void
  setUserRoles: (roles: UserRole[]) => void
  setUserPermissions: (permissions: UserPermission[]) => void

  // 异步方法
  login: (type: LoginType, params: LoginParams) => Promise<{ success: boolean; isToStep?: boolean }>
  logout: () => Promise<void>
  validateToken: () => Promise<boolean>
  loadUserPermissions: () => Promise<void>

  // 权限检查方法
  checkAccess: (accessParams: AccessParams) => boolean
  hasPermission: (permission: string) => boolean
  hasFeatureAccess: (feature: string) => boolean
  hasRoleAccess: (role: string) => boolean
}

export const useAuthStore = create<AuthState>()(
  devtools(
    immer((set, get) => ({
        // 初始状态
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,

        // 权限配置初始状态
        menuConfig: [],
        featureConfig: [],
        userRoles: [],
        userPermissions: [],

        // 同步方法
        setUser: (user: ApiUser) => set(state => {
          state.user = user
          state.isAuthenticated = true
          state.error = null
        }),

        clearAuth: () => set(state => {
          state.user = null
          state.isAuthenticated = false
          state.error = null
          state.menuConfig = []
          state.featureConfig = []
          state.userRoles = []
          state.userPermissions = []
        }),

        setLoading: (loading: boolean) => set(state => {
          state.isLoading = loading
        }),

        setError: (error: string | null) => set(state => {
          state.error = error
        }),

        // 权限数据设置方法
        setMenuConfig: (config: MenuConfig[]) => set(state => {
          state.menuConfig = config
        }),

        setFeatureConfig: (config: FeatureConfig[]) => set(state => {
          state.featureConfig = config
        }),

        setUserRoles: (roles: UserRole[]) => set(state => {
          state.userRoles = roles
        }),

        setUserPermissions: (permissions: UserPermission[]) => set(state => {
          state.userPermissions = permissions
        }),

        // 异步方法实现
        login: async (type: LoginType, params: LoginParams) => {
          const { setLoading, setError, setUser } = get()

          setLoading(true)
          setError(null)

          try {
            // 调用真实的登录API，响应拦截器会自动提取data字段
            const loginResponse = await authAPI.login(type, params)

            const { user, token } = loginResponse

            // 存储token到cookies和localStorage
            tokenManager.setToken(token)

            setUser(user)

            // 加载权限配置
            await get().loadUserPermissions()

            return { success: true, isToStep: user.step < 5 }
          } catch (error: any) {
            console.error('Login error:', error)
            setError(error.message || 'Login failed')
            return { success: false }
          } finally {
            setLoading(false)
          }
        },

        logout: async () => {
          const { setLoading, clearAuth } = get()

          try {
            setLoading(true)

            // 调用退出登录API
            await authAPI.logout()

            // 清除本地状态和token
            clearAuth()

            // 清除token存储
            tokenManager.removeTokens()

            // 重定向到登录页面（考虑国际化）
            if (typeof window !== 'undefined') {
              // 获取当前locale，默认为中文
              const currentPath = window.location.pathname
              const localeMatch = currentPath.match(/^\/([a-z]{2})\//)
              const locale = localeMatch ? localeMatch[1] : 'zh'
              window.location.href = `/${locale}/login`
            }
          } catch (error) {
            console.error('Logout failed:', error)
            // 即使API调用失败，也要清除本地状态
            clearAuth()

            // 清除token存储
            try {
              tokenManager.removeTokens()
            } catch (tokenError) {
              console.error('Failed to clear tokens:', tokenError)
            }

            if (typeof window !== 'undefined') {
              // 获取当前locale，默认为中文
              const currentPath = window.location.pathname
              const localeMatch = currentPath.match(/^\/([a-z]{2})\//)
              const locale = localeMatch ? localeMatch[1] : 'zh'
              window.location.href = `/${locale}/login`
            }
          } finally {
            setLoading(false)
          }
        },

        validateToken: async () => {
          const { setLoading, setUser, clearAuth, user, isAuthenticated } = get()

          try {
            // 检查是否有token
            const token = tokenManager.getToken()
            if (!token) {
              clearAuth()
              return false
            }

            // 🔧 如果已经认证且有用户信息，直接返回成功，避免重复API调用
            if (isAuthenticated && user) {
              console.log('✅ validateToken: 已有用户信息，跳过API调用')
              return true
            }

            console.log('🔍 validateToken: 验证token并获取用户信息')
            setLoading(true)

            // 调用真实的API获取当前用户信息，响应拦截器会自动提取data字段
            const userData = await authAPI.getCurrentUser()

            setUser(userData)
            await get().loadUserPermissions()
            return true
          } catch (error) {
            console.error('Token validation failed:', error)
            clearAuth()
            return false
          } finally {
            setLoading(false)
          }
        },

        loadUserPermissions: async () => {
          const { user, setMenuConfig, setFeatureConfig, setUserRoles, setUserPermissions } = get()
          if (!user) return

          try {
            // 从用户数据中提取权限配置
            const menuConfig: MenuConfig[] = Object.entries(user.company.menu).map(([id, status]) => ({
              id,
              status: Boolean(status)
            }))

            const featureConfig: FeatureConfig[] = Object.entries(user.company.feature).map(([id, status]) => ({
              id,
              status: Boolean(status)
            }))

            // 根据用户类型设置角色
            const userRoles: UserRole[] = [
              {
                id: user.id,
                roleCode: user.user_type === 1 ? 'admin' : user.user_type === 2 ? 'premium' : 'user',
                roleName: user.user_type === 1 ? 'Administrator' : user.user_type === 2 ? 'Premium User' : 'User'
              }
            ]

            // 根据公司配置设置权限
            const userPermissions: UserPermission[] = []

            // PDF相关权限
            if (user.company.config.permissions.pdf_review) {
              userPermissions.push({ id: 'pdf_review', code: 'pdf_review', name: 'PDF Review', resource: 'pdf', action: 'review' })
            }
            if (user.company.config.permissions.pdf_download) {
              userPermissions.push({ id: 'pdf_download', code: 'pdf_download', name: 'PDF Download', resource: 'pdf', action: 'download' })
            }
            if (user.company.config.permissions.pdf_setting) {
              userPermissions.push({ id: 'pdf_setting', code: 'pdf_setting', name: 'PDF Setting', resource: 'pdf', action: 'setting' })
            }

            // 基础权限
            userPermissions.push(
              { id: 'read', code: 'read', name: 'Read', resource: 'general', action: 'read' },
              { id: 'write', code: 'write', name: 'Write', resource: 'general', action: 'write' }
            )

            setMenuConfig(menuConfig)
            setFeatureConfig(featureConfig)
            setUserRoles(userRoles)
            setUserPermissions(userPermissions)
          } catch (error) {
            console.error('Failed to load user permissions:', error)
          }
        },

        // 权限检查方法
        checkAccess: (accessParams: AccessParams) => {
          const { featureConfig, userRoles, userPermissions } = get()
          const { features, roles, permissions, fallback } = accessParams

          // 检查功能权限
          if (features?.length) {
            const hasFeatureAccess = features.some(feature =>
              featureConfig.find(f => f.id === feature && f.status)
            )
            if (!hasFeatureAccess) {
              // 检查是否有fallback选项
              if (fallback?.length) {
                return fallback.some(fb =>
                  featureConfig.find(f => f.id === fb && f.status)
                )
              }
              return false
            }
          }

          // 检查角色权限
          if (roles?.length) {
            const userRoleCodes = userRoles.map(r => r.roleCode)
            const hasRoleAccess = roles.some(role => userRoleCodes.includes(role))
            if (!hasRoleAccess) return false
          }

          // 检查具体权限
          if (permissions?.length) {
            const userPermissionCodes = userPermissions.map(p => p.code)
            const hasPermissionAccess = permissions.some(permission =>
              userPermissionCodes.includes(permission)
            )
            if (!hasPermissionAccess) return false
          }

          return true
        },

        hasPermission: (permission: string) => {
          const { userPermissions } = get()
          return userPermissions.some(p => p.code === permission)
        },

        hasFeatureAccess: (feature: string) => {
          const { featureConfig } = get()
          return featureConfig.some(f => f.id === feature && f.status)
        },

        hasRoleAccess: (role: string) => {
          const { userRoles } = get()
          return userRoles.some(r => r.roleCode === role)
        },
      })),
    { name: 'auth-store' }
  )
)
