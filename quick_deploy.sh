#!/bin/bash

set -e

# 解析命令行参数
ENVIRONMENT="pd"  # 默认为生产环境
IMAGE_NAME="nextjs-web-ui"

# 解析环境参数
if [ "$1" == "--dev" ] || [ "$1" == "-dev" ]; then
    ENVIRONMENT="dev"
    echo "🔧 使用开发环境配置"
elif [ "$1" == "--pd" ] || [ "$1" == "-pd" ]; then
    ENVIRONMENT="pd"
    echo "🏭 使用生产环境配置"
elif [ -n "$1" ]; then
    echo "❌ 无效参数: $1"
    echo "用法: $0 [--dev|--pd]"
    echo "  --dev: 开发环境"
    echo "  --pd:  生产环境 (默认)"
    exit 1
fi

CONTAINER_NAME="${IMAGE_NAME}-${ENVIRONMENT}"

echo "=============================================="
echo "🚀 快速重新部署 ${IMAGE_NAME} (${ENVIRONMENT}环境)"
echo "=============================================="

# 步骤1: 构建和推送镜像
echo ""
echo "📦 步骤1: 构建和推送Docker镜像..."
echo "----------------------------------------------"

# 检查脚本是否存在
if [ ! -f "./build_push_image.sh" ]; then
    echo "❌ 找不到 build_push_image.sh 脚本"
    exit 1
fi

chmod +x ./build_push_image.sh
./build_push_image.sh --${ENVIRONMENT}

if [ $? -eq 0 ]; then
    echo "✅ 镜像构建和推送成功"
else
    echo "❌ 镜像构建和推送失败"
    exit 1
fi

# 步骤2: 部署服务
echo ""
echo "🚀 步骤2: 部署服务..."
echo "----------------------------------------------"

# 检查脚本是否存在
if [ ! -f "./deploy.sh" ]; then
    echo "❌ 找不到 deploy.sh 脚本"
    exit 1
fi

chmod +x ./deploy.sh
./deploy.sh --${ENVIRONMENT}

if [ $? -eq 0 ]; then
    echo "✅ 服务部署成功"
else
    echo "❌ 服务部署失败"
    exit 1
fi

# 步骤3: 检查容器状态并获取容器ID
echo ""
echo "🔍 步骤3: 检查容器状态..."
echo "----------------------------------------------"

CONTAINER_ID=$(docker ps --filter "name=${CONTAINER_NAME}" --format "{{.ID}}" | head -1)

if [ -z "$CONTAINER_ID" ]; then
    echo "❌ 无法找到运行中的容器: ${CONTAINER_NAME}"
    echo "尝试查看所有容器:"
    docker ps -a --filter "name=${CONTAINER_NAME}"
    exit 1
fi

echo "✅ 找到运行中的容器:"
echo "   容器名称: ${CONTAINER_NAME}"
echo "   容器ID: ${CONTAINER_ID}"

# 显示容器详细信息
echo ""
echo "📊 容器详细信息:"
docker ps --filter "id=${CONTAINER_ID}" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.Image}}"

# 健康检查
echo ""
echo "🩺 最终健康检查..."
echo "----------------------------------------------"


# 步骤4: 提供日志查看选项
echo ""
echo "📋 步骤4: 查看实时日志 (可选)..."
echo "----------------------------------------------"
echo "如需查看实时日志，请运行："
echo "  docker logs -f ${CONTAINER_ID}"
echo ""
echo "按 Enter 查看实时日志，或 Ctrl+C 退出"

# 实时跟踪日志
docker logs -f "${CONTAINER_ID}" 