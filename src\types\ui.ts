// UI相关类型
export type Theme = 'light' | 'dark' | 'system'

export type NotificationType = 'success' | 'error' | 'warning' | 'info'

export interface Notification {
  id: string
  type: NotificationType
  title: string
  message?: string
  duration?: number
  timestamp: number
  action?: {
    label: string
    onClick: () => void
  }
}

export interface ModalState {
  [key: string]: boolean
}

export interface SidebarState {
  open: boolean
  collapsed: boolean
}

export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

export interface FormFieldError {
  field: string
  message: string
}

export interface FormState<T = any> {
  data: T
  errors: FormFieldError[]
  isSubmitting: boolean
  isDirty: boolean
  isValid: boolean
}

export interface Breadcrumb {
  label: string
  href?: string
  icon?: string
}

// 组件Props类型
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  disabled?: boolean
  loading?: boolean
  onClick?: () => void
}

export interface InputProps extends BaseComponentProps {
  type?: string
  placeholder?: string
  value?: string
  defaultValue?: string
  disabled?: boolean
  error?: string
  onChange?: (value: string) => void
}
