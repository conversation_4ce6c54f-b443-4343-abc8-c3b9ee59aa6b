/**
 * 认证相关API Hooks
 * 基于统一的useApi hook，提供认证相关的API调用
 */

import React from 'react'
import { useApi, useApiAuto, useApiPresets, useApiFull, useApiSave } from '@/hooks/use-api'
import type { LoginType, LoginParams, ApiUser as User } from '@/stores/auth-store'
import { devDebugger } from '@/lib/config/dev-debug'

// API响应类型
interface LoginResponse {
  token: string
  user: User
}

/**
 * 认证相关API Hooks
 */
export const useAuthApi = {
  /**
   * 登录Hook
   */
  useLogin: () => {
    const [state, execute] = useApiPresets.auth<LoginResponse>('/api/user/login', {
      method: 'POST',
      skipAuth: true,
    })

    const login = async (type: LoginType, params: LoginParams) => {
      // 根据登录类型转换参数
      let loginData: { user_name: string; password?: string; phone?: string; code?: string }

      if (type === 'password') {
        const passwordParams = params as Extract<LoginParams, { username: string }>
        loginData = {
          user_name: passwordParams.username,
          password: passwordParams.password
        }
      } else if (type === 'sms') {
        const smsParams = params as Extract<LoginParams, { phone: string }>
        loginData = {
          user_name: smsParams.phone,
          phone: smsParams.phone,
          code: smsParams.code
        }
      } else if (type === 'email') {
        const emailParams = params as Extract<LoginParams, { email: string }>
        loginData = {
          user_name: emailParams.email,
          code: emailParams.code
        }
      } else {
        throw new Error(`Unsupported login type: ${type}`)
      }

      return execute({
        body: JSON.stringify(loginData)
      })
    }

    return [state, login] as const
  },

  /**
   * 获取当前用户Hook
   */
  useGetCurrentUser: () => {
    return useApiPresets.silent<User>('/api/user/get_current')
  },

  /**
   * 自动获取当前用户Hook (组件加载时自动执行)
   */
  useCurrentUserAuto: () => {
    return useApiAuto<User>('/api/user/get_current', {
      showError: false,
      showSuccess: false
    })
  },

  /**
   * 条件获取当前用户Hook (仅供IframeContainer使用)
   * 这是iframe环境下唯一的用户认证入口
   */
  useCurrentUserConditional: (shouldFetch: boolean) => {
    const [state, execute] = useApiPresets.silent<User>('/api/user/get_current')
    const [hasExecuted, setHasExecuted] = React.useState(false)
    const [isExecuting, setIsExecuting] = React.useState(false)
    const executeRef = React.useRef(execute)
    const stateRef = React.useRef(state)
    const abortControllerRef = React.useRef<AbortController | null>(null)
    
    // 🔍 调试：记录状态变化
    const prevStateRef = React.useRef(state)
    React.useEffect(() => {
      if (stateRef.current !== state) {
        devDebugger.logHookStateChange(
          'useCurrentUserConditional', 
          prevStateRef.current, 
          state,
          'IframeContainer认证Hook'
        )
        prevStateRef.current = state
        stateRef.current = state
      }
    }, [state])
    
    // 更新execute引用，但不触发useEffect
    executeRef.current = execute
    
    // 强化防护：防止重复执行和无限循环
    React.useEffect(() => {
      console.log('🔄 useCurrentUserConditional useEffect triggered:', {
        shouldFetch,
        hasExecuted,
        isExecuting,
        stateSnapshot: {
          loading: state.loading,
          hasValue: !!state.value,
          hasError: !!state.error
        }
      })
      
      // 关键防护：多重检查防止重复执行
      if (shouldFetch && !hasExecuted && !isExecuting) {
        // 额外检查避免重复执行
        if (!state.loading && !state.value && !state.error) {
          console.log('🔄 IframeContainer认证: 执行唯一的用户信息获取')
          devDebugger.logApiCall('/api/user/get_current', 'GET', 'useCurrentUserConditional初次执行')
          
          // 防重复执行标记
          setIsExecuting(true)
          
          // 创建AbortController以便取消请求
          abortControllerRef.current = new AbortController()
          
          executeRef.current()
            .then(() => {
              setHasExecuted(true)
              setIsExecuting(false)
            })
            .catch((error) => {
              console.error('API调用失败:', error)
              setIsExecuting(false)
              // 只有在非取消错误时才标记为已执行
              if (error.name !== 'AbortError') {
                setHasExecuted(true)
              }
            })
        } else if (state.value || state.error) {
          // 如果已有数据或错误，也标记为已执行
          console.log('🔄 IframeContainer认证: 检测到已有状态，标记为已执行')
          setHasExecuted(true)
          setIsExecuting(false)
        }
      }
      
      // 清理：取消进行中的请求
      return () => {
        if (abortControllerRef.current) {
          abortControllerRef.current.abort()
          abortControllerRef.current = null
        }
      }
    }, [shouldFetch, hasExecuted, isExecuting])
    
    // 优化重置逻辑：只在真正需要时重置
    React.useEffect(() => {
      if (!shouldFetch && (hasExecuted || isExecuting)) {
        console.log('🔄 IframeContainer认证: 重置执行状态')
        
        // 取消进行中的请求
        if (abortControllerRef.current) {
          abortControllerRef.current.abort()
          abortControllerRef.current = null
        }
        
        setHasExecuted(false)
        setIsExecuting(false)
      }
    }, [shouldFetch, hasExecuted, isExecuting])
    
    // 关键修复：使用 useMemo 稳定返回的状态对象引用
    // 只有当真正的状态值变化时才创建新对象，避免无限循环
    const stableState = React.useMemo(() => {
      const newState = {
        loading: state.loading || isExecuting,
        error: state.error,
        value: state.value
      }
      
      console.log('🔄 useCurrentUserConditional: 创建稳定状态对象', newState)
      return newState
    }, [state.loading, state.error, state.value, isExecuting])
    
    return stableState
  },

  /**
   * 登出Hook
   */
  useLogout: () => {
    const [state, execute] = useApiPresets.silent<void>('/api/user/logout', {
      method: 'POST',
    })

    const logout = async () => {
      try {
        await execute({
          body: JSON.stringify({})
        })
      } catch (error) {
        // 即使API调用失败，也要继续本地清理
        console.warn('Logout API call failed, but continuing with local cleanup:', error)
      }
    }

    return [state, logout] as const
  },

  /**
   * 刷新Token Hook
   */
  useRefreshToken: () => {
    return useApiPresets.silent<{ token: string; refreshToken: string }>('/auth/refresh', {
      method: 'POST',
    })
  },

  /**
   * 修改密码Hook
   */
  useChangePassword: () => {
    return useApiPresets.save<void>('/auth/change-password', {
      method: 'POST',
    })
  },

  /**
   * 忘记密码Hook
   */
  useForgotPassword: () => {
    return useApiFull<void>('/auth/forgot-password', {
      method: 'POST',
    })
  },

  /**
   * 重置密码Hook
   */
  useResetPassword: () => {
    return useApiSave<void>('/auth/reset-password', {
      method: 'POST',
    })
  },
}

/**
 * 用户管理相关API Hooks
 */
export const useUserApi = {
  /**
   * 获取用户列表Hook
   */
  useGetUsers: () => {
    return useApi<User[]>('/api/users', {
      showError: true,
      showSuccess: false,
    })
  },

  /**
   * 获取用户详情Hook
   */
  useGetUser: () => {
    // 这个Hook需要在使用时提供完整的endpoint
    return useApi<User>('/api/users/placeholder', {
      showError: true,
      showSuccess: false,
    })
  },

  /**
   * 创建用户Hook
   */
  useCreateUser: () => {
    return useApiPresets.save<User>('/api/users', {
      method: 'POST',
    })
  },

  /**
   * 更新用户Hook
   */
  useUpdateUser: () => {
    return useApiSave<User>('/api/users/placeholder', {
      method: 'PUT',
    })
  },

  /**
   * 删除用户Hook
   */
  useDeleteUser: () => {
    return useApiPresets.delete<void>('/api/users/placeholder', {
      method: 'DELETE',
    })
  },
}

/**
 * 权限管理相关API Hooks
 */
export const usePermissionApi = {
  /**
   * 获取菜单配置Hook
   */
  useGetMenuConfig: () => {
    return useApiAuto<any[]>('/auth/menu-config', {
      showError: true,
      showSuccess: false,
    })
  },

  /**
   * 获取功能配置Hook
   */
  useGetFeatureConfig: () => {
    return useApiAuto<any[]>('/auth/feature-config', {
      showError: true,
      showSuccess: false,
    })
  },

  /**
   * 获取用户角色Hook
   */
  useGetUserRoles: () => {
    return useApiAuto<any[]>('/auth/user-roles', {
      showError: true,
      showSuccess: false,
    })
  },

  /**
   * 获取用户权限Hook
   */
  useGetUserPermissions: () => {
    return useApiAuto<any[]>('/auth/user-permissions', {
      showError: true,
      showSuccess: false,
    })
  },
}

/**
 * 组合Hook：完整的认证流程
 */
export function useAuthFlow() {
  const [loginState, login] = useAuthApi.useLogin()
  const [logoutState, logout] = useAuthApi.useLogout()
  const [getCurrentUserState, getCurrentUser] = useAuthApi.useGetCurrentUser()

  return {
    // 状态
    loginState,
    logoutState,
    getCurrentUserState,
    
    // 操作
    login,
    logout,
    getCurrentUser,
    
    // 便捷状态
    isLoading: loginState.loading || logoutState.loading || getCurrentUserState.loading,
    hasError: !!(loginState.error || logoutState.error || getCurrentUserState.error),
  }
}

/**
 * 组合Hook：用户管理流程
 */
export function useUserManagement() {
  const [getUsersState, getUsers] = useUserApi.useGetUsers()
  const [getUserState, getUser] = useUserApi.useGetUser()
  const [createUserState, createUser] = useUserApi.useCreateUser()
  const [updateUserState, updateUser] = useUserApi.useUpdateUser()
  const [deleteUserState, deleteUser] = useUserApi.useDeleteUser()

  return {
    // 状态
    getUsersState,
    getUserState,
    createUserState,
    updateUserState,
    deleteUserState,
    
    // 操作
    getUsers,
    getUser,
    createUser,
    updateUser,
    deleteUser,
    
    // 便捷状态
    isLoading: getUsersState.loading || getUserState.loading || 
               createUserState.loading || updateUserState.loading || deleteUserState.loading,
    
    hasError: !!(getUsersState.error || getUserState.error || 
                 createUserState.error || updateUserState.error || deleteUserState.error),
  }
}
