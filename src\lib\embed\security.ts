/**
 * 嵌入页面安全策略
 */

import { NextResponse } from 'next/server'
import { domainValidator } from './domain-validator'

export interface SecurityHeaders {
  'X-Frame-Options'?: string
  'Content-Security-Policy'?: string
  'Access-Control-Allow-Origin'?: string
  'Access-Control-Allow-Credentials'?: string
  'X-Content-Type-Options'?: string
  'Referrer-Policy'?: string
}

export class EmbedSecurity {
  /**
   * 设置 iframe 安全头部
   */
  static setIframeHeaders(response: NextResponse, allowedOrigin: string): void {
    const headers: SecurityHeaders = {
      // 允许特定域名嵌入
      'X-Frame-Options': `ALLOW-FROM ${allowedOrigin}`,
      
      // CSP 策略
      'Content-Security-Policy': [
        `frame-ancestors ${allowedOrigin}`,
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
        "style-src 'self' 'unsafe-inline'",
        "img-src 'self' data: https:",
        "font-src 'self' data:",
        "connect-src 'self' https:"
      ].join('; '),
      
      // CORS 设置
      'Access-Control-Allow-Origin': allowedOrigin,
      'Access-Control-Allow-Credentials': 'true',
      
      // 其他安全头部
      'X-Content-Type-Options': 'nosniff',
      'Referrer-Policy': 'strict-origin-when-cross-origin'
    }

    Object.entries(headers).forEach(([key, value]) => {
      if (value) {
        response.headers.set(key, value)
      }
    })
  }

  /**
   * 验证请求安全性
   */
  static validateRequest(request: Request): {
    isValid: boolean
    origin?: string | undefined
    error?: string | undefined
  } {
    const referer = request.headers.get('referer')
    const origin = request.headers.get('origin')

    // 检查 Referer
    if (!domainValidator.isRefererAllowed(referer)) {
      return {
        isValid: false,
        error: 'Invalid referer domain'
      }
    }

    // 获取允许的源
    const allowedOrigin = referer ? new URL(referer).origin : origin

    return {
      isValid: true,
      origin: allowedOrigin || undefined
    }
  }

  /**
   * 创建安全的响应
   */
  static createSecureResponse(
    content: string | Response,
    allowedOrigin: string,
    options?: ResponseInit
  ): Response {
    const response = typeof content === 'string' 
      ? new Response(content, options)
      : content

    const nextResponse = NextResponse.next({ request: { headers: new Headers() } })
    this.setIframeHeaders(nextResponse, allowedOrigin)

    // 复制安全头部到响应
    nextResponse.headers.forEach((value, key) => {
      response.headers.set(key, value)
    })

    return response
  }

  /**
   * 生成 CSP nonce
   */
  static generateNonce(): string {
    const array = new Uint8Array(16)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }

  /**
   * 验证 token 格式
   */
  static isValidTokenFormat(token: string): boolean {
    if (!token || typeof token !== 'string') {
      return false
    }

    // JWT 格式验证
    const parts = token.split('.')
    if (parts.length !== 3) {
      return false
    }

    try {
      // 尝试解析 header 和 payload
      JSON.parse(atob(parts[0]))
      JSON.parse(atob(parts[1]))
      return true
    } catch {
      return false
    }
  }

  /**
   * 清理和验证 URL 参数
   */
  static sanitizeUrlParams(params: URLSearchParams): URLSearchParams {
    const sanitized = new URLSearchParams()
    
    params.forEach((value, key) => {
      // 移除潜在的危险字符
      const cleanKey = key.replace(/[<>"'&]/g, '')
      const cleanValue = value.replace(/[<>"'&]/g, '')
      
      if (cleanKey && cleanValue) {
        sanitized.set(cleanKey, cleanValue)
      }
    })
    
    return sanitized
  }

  /**
   * 检查是否为安全的重定向URL
   */
  static isSafeRedirectUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url)
      
      // 只允许相对路径或同域名的绝对路径
      if (parsedUrl.protocol === 'javascript:' || parsedUrl.protocol === 'data:') {
        return false
      }
      
      // 检查是否为允许的域名
      const origin = `${parsedUrl.protocol}//${parsedUrl.hostname}`
      return domainValidator.isAllowed(origin)
    } catch {
      // 如果不是有效URL，检查是否为相对路径
      return url.startsWith('/') && !url.startsWith('//')
    }
  }
}
