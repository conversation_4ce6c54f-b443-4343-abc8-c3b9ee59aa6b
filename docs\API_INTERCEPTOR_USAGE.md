# API拦截器使用指南

## 概述

`src/lib/api-interceptor.ts` 提供了统一的API请求和响应拦截器，自动处理认证头、语言头和错误处理。

## 🔧 新增功能

### 请求拦截器
自动为所有API请求添加：
- **language头**: 根据URL路径语言 > localStorage用户语言 > 默认中文的优先级
- **authorization头**: 自动从tokenManager获取JWT token

### 语言头智能获取
```typescript
// 优先级顺序：
// 1. URL路径语言 (/en/dashboard -> 'english')
// 2. localStorage用户语言偏好
// 3. 默认中文 ('chinese')
```

## 📚 使用方法

### 1. 使用interceptedFetch (推荐)
```typescript
import { interceptedFetch, interceptorConfigs } from '@/lib/api-interceptor'

// 基础使用
const userData = await interceptedFetch<UserData>('/api/user/profile')

// 带配置使用
const result = await interceptedFetch<SaveResult>(
  '/api/user/update',
  {
    method: 'POST',
    body: JSON.stringify({ name: 'John' })
  },
  interceptorConfigs.save // 显示保存成功/失败提示
)
```

### 2. 使用requestInterceptor
```typescript
import { requestInterceptor } from '@/lib/api-interceptor'

// 手动应用请求拦截器
const { url, options } = requestInterceptor('/api/data', {
  method: 'GET'
})

const response = await fetch(url, options)
// options.headers 已自动包含 language 和 authorization
```

### 3. 在现有API函数中使用
```typescript
// 更新现有的API函数
async function getUserProfile(): Promise<UserProfile> {
  return interceptedFetch<UserProfile>('/api/user/profile', {}, {
    showError: true,
    showSuccess: false
  })
}

async function updateUserProfile(data: Partial<UserProfile>): Promise<void> {
  return interceptedFetch<void>('/api/user/profile', {
    method: 'PUT',
    body: JSON.stringify(data)
  }, interceptorConfigs.save)
}
```

## 🌍 语言头处理

### 自动语言检测
拦截器会自动检测当前语言环境：

```typescript
// URL: /en/dashboard -> language: 'english'
// URL: /zh/dashboard -> language: 'chinese'  
// URL: /ja/dashboard -> language: 'japanese'
// URL: /dashboard (无语言前缀) -> 使用localStorage或默认中文
```

### 手动设置语言
```typescript
// 通过localStorage设置用户语言偏好
localStorage.setItem('user-language', 'english')

// 下次API请求会自动使用这个语言设置
```

## 🔐 认证头处理

### 自动Token管理
```typescript
// 拦截器会自动：
// 1. 从tokenManager获取当前token
// 2. 添加到authorization头
// 3. 处理token过期等认证错误
```

### 跳过认证
```typescript
// 对于不需要认证的接口，手动移除authorization头
const { url, options } = requestInterceptor('/api/public/data')
delete options.headers['authorization']
```

## 📊 错误处理

### 自动错误处理
```typescript
try {
  const data = await interceptedFetch<Data>('/api/data')
  // 成功处理
} catch (error) {
  if (error instanceof ApiError) {
    console.log('API错误:', error.code, error.message)
    // 错误已自动显示toast提示
  }
}
```

### 自定义错误处理
```typescript
const data = await interceptedFetch<Data>('/api/data', {}, {
  showError: false, // 不显示默认错误提示
  errorMessage: '自定义错误消息'
})
```

## 🔄 迁移现有代码

### 从auth-api.ts迁移
```typescript
// 旧代码
import { authAPI } from '@/lib/auth-api'
const user = await authAPI.getCurrentUser()

// 新代码 (推荐)
import { interceptedFetch } from '@/lib/api-interceptor'
const user = await interceptedFetch<User>('/api/user/get_current')
```

### 从use-api.ts迁移
```typescript
// 旧代码
import { useApi } from '@/hooks/use-api'
const [state, fetchData] = useApi<Data>('/api/data')

// 新代码 (在组件中)
import { interceptedFetch } from '@/lib/api-interceptor'
const [data, setData] = useState<Data>()
const [loading, setLoading] = useState(false)

const fetchData = async () => {
  setLoading(true)
  try {
    const result = await interceptedFetch<Data>('/api/data')
    setData(result)
  } catch (error) {
    // 错误已自动处理
  } finally {
    setLoading(false)
  }
}
```

## 🎯 最佳实践

### 1. 统一使用interceptedFetch
```typescript
// ✅ 推荐
const data = await interceptedFetch<Data>('/api/data')

// ❌ 不推荐 (缺少语言头)
const response = await fetch('/api/data')
```

### 2. 使用预定义配置
```typescript
// ✅ 推荐
import { interceptorConfigs } from '@/lib/api-interceptor'
await interceptedFetch('/api/save', options, interceptorConfigs.save)

// ❌ 不推荐 (重复配置)
await interceptedFetch('/api/save', options, {
  showError: true,
  showSuccess: true,
  successMessage: '保存成功'
})
```

### 3. 类型安全
```typescript
// ✅ 推荐 (明确类型)
interface UserProfile {
  id: string
  name: string
  email: string
}
const profile = await interceptedFetch<UserProfile>('/api/user/profile')

// ❌ 不推荐 (any类型)
const profile = await interceptedFetch('/api/user/profile')
```

## 🐛 调试

### 查看请求头
```typescript
// 开发环境下查看实际发送的请求头
const { url, options } = requestInterceptor('/api/test')
console.log('请求头:', options.headers)
// 输出: { 'Content-Type': 'application/json', 'language': 'english', 'authorization': 'Bearer ...' }
```

### 语言检测调试
```typescript
// 在浏览器控制台中检查当前语言
console.log('当前URL:', window.location.pathname)
console.log('localStorage语言:', localStorage.getItem('user-language'))
```

---

*这个拦截器确保所有API请求都包含正确的语言头，让后端能够返回对应语言的内容。*
