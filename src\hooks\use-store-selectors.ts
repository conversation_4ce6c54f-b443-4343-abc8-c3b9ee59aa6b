import { useAuthStore, useUIStore, useAppStore } from '@/stores'
import { useMemo } from 'react'
import { useLocalStorage, useSessionStorage, useToggle, useBoolean } from 'react-use'

// 认证相关选择器
export function useAuth() {
  const auth = useAuthStore()

  return useMemo(() => ({
    user: auth.user,
    isAuthenticated: auth.isAuthenticated,
    isLoading: auth.isLoading,
    error: auth.error,
    setUser: auth.setUser,
    clearAuth: auth.clearAuth,
    login: auth.login,
    logout: auth.logout,
    validateToken: auth.validateToken,
  }), [auth])
}

export function useAuthUser() {
  return useAuthStore(state => state.user)
}

export function useAuthStatus() {
  return useAuthStore(state => ({
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    error: state.error,
  }))
}

// UI相关选择器
export function useUI() {
  const ui = useUIStore()

  return useMemo(() => ({
    sidebarOpen: ui.sidebarOpen,
    sidebarCollapsed: ui.sidebarCollapsed,
    globalLoading: ui.globalLoading,
    notifications: ui.notifications,

    setSidebarOpen: ui.setSidebarOpen,
    setSidebarCollapsed: ui.setSidebarCollapsed,
    toggleSidebar: ui.toggleSidebar,
    setGlobalLoading: ui.setGlobalLoading,
    addNotification: ui.addNotification,
    removeNotification: ui.removeNotification,
  }), [ui])
}

export function useSidebar() {
  return useUIStore(state => ({
    open: state.sidebarOpen,
    collapsed: state.sidebarCollapsed,
    setOpen: state.setSidebarOpen,
    setCollapsed: state.setSidebarCollapsed,
    toggle: state.toggleSidebar,
  }))
}

export function useNotifications() {
  return useUIStore(state => ({
    notifications: state.notifications,
    add: state.addNotification,
    remove: state.removeNotification,
    clear: state.clearNotifications,
  }))
}

// 应用相关选择器
export function useApp() {
  const app = useAppStore()

  return useMemo(() => ({
    config: app.config,
    isInitialized: app.isInitialized,
    isOnline: app.isOnline,
    preferences: app.preferences,

    setInitialized: app.setInitialized,
    setOnlineStatus: app.setOnlineStatus,
    updatePreferences: app.updatePreferences,
    isFeatureEnabled: app.isFeatureEnabled,
    toggleFeature: app.toggleFeature,
  }), [app])
}

export function useAppConfig() {
  return useAppStore(state => state.config)
}

export function useUserPreferences() {
  return useAppStore(state => ({
    preferences: state.preferences,
    update: state.updatePreferences,
  }))
}

export function useFeatureFlags() {
  return useAppStore(state => ({
    isEnabled: state.isFeatureEnabled,
    toggle: state.toggleFeature,
    features: state.config.features,
  }))
}

// 使用react-use的实用hooks
export function useLocalStorageState<T>(key: string, defaultValue: T) {
  return useLocalStorage(key, defaultValue)
}

export function useSessionStorageState<T>(key: string, defaultValue: T) {
  return useSessionStorage(key, defaultValue)
}

export function useToggleState(defaultValue = false) {
  return useToggle(defaultValue)
}

export function useBooleanState(defaultValue = false) {
  return useBoolean(defaultValue)
}
