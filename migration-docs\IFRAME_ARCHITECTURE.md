# iframe 架构设计文档

## 🎯 核心设计原则

### 单一职责原则 (Single Responsibility Principle)

在iframe嵌入系统中，**认证逻辑统一由 `IframeContainer.tsx` 处理**，这是整个iframe环境下的唯一认证入口点。

## 📐 架构分层

```
┌─────────────────────────────────────────┐
│              父页面环境                   │
│  ┌─────────────────────────────────────┐ │
│  │           iframe 容器               │ │
│  │  ┌───────────────────────────────┐  │ │
│  │  │      IframeContainer.tsx      │  │ │  <- 🔑 唯一认证处理点
│  │  │  ✅ Token 管理               │  │ │
│  │  │  ✅ 用户信息获取             │  │ │
│  │  │  ✅ 认证状态管理             │  │ │
│  │  │  ✅ 错误处理                │  │ │
│  │  │  ┌─────────────────────────┐  │  │ │
│  │  │  │    DashboardPage.tsx    │  │  │ │  <- 🎨 纯展示组件
│  │  │  │  ❌ 无认证逻辑          │  │  │ │
│  │  │  │  ✅ 接收用户信息        │  │  │ │
│  │  │  │  ✅ 纯展示逻辑          │  │  │ │
│  │  │  └─────────────────────────┘  │  │ │
│  │  └───────────────────────────────┘  │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## 🔧 实现细节

### IframeContainer.tsx - 认证核心

```typescript
// 唯一的用户信息获取点
const userState = useAuthApi.useCurrentUserConditional(shouldFetchUser)

// 通过props传递给子组件
{React.cloneElement(children as React.ReactElement, {
  user: shouldFetchUser ? userState.value : null,
  userLoading: shouldFetchUser ? userState.loading : false,
  userError: shouldFetchUser ? userState.error : null
})}
```

### 子组件 - 纯展示逻辑

```typescript
interface DashboardPageProps {
  user?: User // 从IframeContainer传递
  userLoading?: boolean
  userError?: any
}

export default function DashboardPage({ user }: DashboardPageProps) {
  // 只负责展示，不处理认证
  if (!user) {
    return <div>暂无用户信息</div>
  }
  
  return <div>展示用户信息: {user.user_name}</div>
}
```

## ✅ 架构优势

### 1. 避免重复请求
- **问题**：多个组件同时调用用户信息API
- **解决**：统一在IframeContainer中处理，子组件被动接收

### 2. 清晰的职责分离
- **IframeContainer**：负责数据获取和状态管理
- **子组件**：负责UI展示和用户交互

### 3. 易于测试和维护
- 认证逻辑集中，便于单元测试
- 子组件作为纯函数，测试简单

### 4. 状态管理简化
- 避免了props drilling问题
- 统一的错误处理和加载状态

## ⚠️ 重要规约

### ❌ 禁止的做法

```typescript
// ❌ 错误：在子组件中直接调用认证API
function SomeEmbedPage() {
  const userState = useAuthApi.useCurrentUserAuto() // 禁止!
  // ...
}

// ❌ 错误：在AuthProvider中处理embed路径
function AuthProvider() {
  // 对embed路径也执行认证检查 // 禁止!
}
```

### ✅ 推荐的做法

```typescript
// ✅ 正确：子组件通过props接收数据
function SomeEmbedPage({ user, userLoading, userError }: EmbedPageProps) {
  // 纯展示逻辑
}

// ✅ 正确：AuthProvider跳过embed路径
function AuthProvider() {
  if (pathname.startsWith('/embed/')) {
    return // 跳过，让IframeContainer处理
  }
}
```

## 🧪 测试验证

### 检查重复请求

1. 打开浏览器开发者工具
2. 访问 `/embed/dashboard`
3. 检查Network面板中的 `/api/user/get_current` 请求
4. ✅ 期望：只有1次请求
5. ❌ 问题：多次重复请求

### 检查日志输出

期望看到的日志顺序：
```
🔓 AuthProvider: 跳过embed路径，认证由IframeContainer统一处理
🔑 IframeContainer统一认证 - shouldFetchUser: true
🔄 IframeContainer认证: 执行唯一的用户信息获取
```

## 📋 未来扩展

当需要添加新的embed页面时：

1. 创建新的页面组件（如 `ProfilePage.tsx`）
2. 确保它接收 `user`, `userLoading`, `userError` props
3. 在 `EmbedPageWrapper` 中添加路由映射
4. **不要**在新组件中添加认证逻辑

## 🔗 相关文件

- `src/components/embed/IframeContainer.tsx` - 认证核心
- `src/components/embed/pages/DashboardPage.tsx` - 示例子组件
- `src/lib/auth-api-hooks.ts` - 认证API hooks
- `src/components/providers/auth-provider.tsx` - 全局认证提供者

---

**原则：一个职责，一个地方。认证逻辑只在IframeContainer中处理。** 