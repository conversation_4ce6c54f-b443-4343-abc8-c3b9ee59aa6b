/**
 * 路由守卫系统类型定义
 */

import { NextRequest, NextResponse } from 'next/server'
import type { ApiUser as User, UserPermission, UserRole, MenuConfig, FeatureConfig } from '@/stores/auth-store'

/**
 * 路由配置接口
 */
export interface RouteConfig {
  path: string
  requiresAuth?: boolean
  requiresGuest?: boolean
  tryAuth?: boolean
  redirectIfUnauthorized?: string
  access?: {
    features?: string[]
    roles?: string[]
    permissions?: string[]
    rightSchemeType?: string[]
    fallback?: string[]
  }
  redirectIfNoAccess?: string
  menuId?: string
  keepAlive?: boolean
}

/**
 * 守卫上下文接口
 */
export interface GuardContext {
  request: NextRequest
  pathname: string
  searchParams: URLSearchParams
  routeConfig: RouteConfig | null
  user: User | null
  userPermissions: UserPermission[]
  userRoles: UserRole[]
  menuConfig: MenuConfig[]
  featureConfig: FeatureConfig[]
}

/**
 * 守卫结果接口
 */
export interface GuardResult {
  type: 'continue' | 'redirect' | 'rewrite'
  response?: NextResponse
  url?: string
  query?: Record<string, string>
}

/**
 * 守卫函数类型
 */
export type GuardFunction = (context: GuardContext) => Promise<GuardResult>

/**
 * 中间件函数类型
 */
export type MiddlewareFunction = (
  request: NextRequest,
  next: () => Promise<NextResponse>
) => Promise<NextResponse>

/**
 * 认证数据接口
 */
export interface AuthData {
  user: User
  permissions: UserPermission[]
  roles: UserRole[]
  menuConfig: MenuConfig[]
  featureConfig: FeatureConfig[]
}

/**
 * 重定向选项接口
 */
export interface RedirectOptions {
  addFromQuery?: boolean
  addSSORedirectQuery?: boolean
  replaceRedirectFromQuery?: boolean
}

/**
 * 业务流程重定向策略接口
 */
export interface PollRedirectStrategy {
  handle(pathname: string, stepNum: number, baseUrl?: string): {
    shouldRedirect: boolean
    redirectPath?: string
  }
}

/**
 * 权限检查上下文
 */
export interface PermissionContext {
  userPermissions: UserPermission[]
  userRoles: UserRole[]
  featureConfig: FeatureConfig[]
}
