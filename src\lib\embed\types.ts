/**
 * 嵌入系统类型定义
 */

export interface EmbedConfig {
  allowedDomains: string[]
  tokenParamName: string
  enableCommunication: boolean
  securityHeaders: boolean
}

export interface EmbedAuthResult {
  success: boolean
  user?: any
  error?: string
}

export interface IframeMessage {
  type: string
  data?: any
  timestamp: number
}

export interface EmbedContext {
  isEmbedded: boolean
  parentOrigin?: string | undefined
  token?: string | undefined
  user?: any
}

export type MessageType = 
  | 'RESIZE_REQUEST'
  | 'RESIZE_RESPONSE'
  | 'NAVIGATION_REQUEST'
  | 'NAVIGATION_RESPONSE'
  | 'AUTH_STATUS'
  | 'PAGE_LOADED'
  | 'PING'
  | 'PONG'
  | 'ERROR'

export interface ResizeMessage extends IframeMessage {
  type: 'RESIZE_RESPONSE'
  data: {
    height: number
    width?: number
  }
}

export interface NavigationMessage extends IframeMessage {
  type: 'NAVIGATION_REQUEST' | 'NAVIGATION_RESPONSE'
  data: {
    path: string
    query?: Record<string, string>
  }
}

export interface AuthStatusMessage extends IframeMessage {
  type: 'AUTH_STATUS'
  data: {
    isAuthenticated: boolean
    userId?: string
    userName?: string
  }
}

export interface PageLoadedMessage extends IframeMessage {
  type: 'PAGE_LOADED'
  data: {
    title: string
    path: string
    timestamp: number
  }
}

export interface ErrorMessage extends IframeMessage {
  type: 'ERROR'
  data: {
    message: string
    code?: string
    details?: any
  }
}
