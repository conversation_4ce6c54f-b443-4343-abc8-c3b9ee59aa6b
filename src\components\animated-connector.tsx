"use client"

import { motion } from "framer-motion"

interface AnimatedConnectorProps {
  isActive: boolean
  direction: "left" | "right" | "down"
  isLastInRow: boolean
}

export function AnimatedConnector({ isActive, direction, isLastInRow }: AnimatedConnectorProps) {
  if (direction === "down") {
    return (
      <div className="flex justify-center">
        <motion.div
          className="w-0.5 h-8 bg-slate-300 relative overflow-hidden"
          initial={{ opacity: 0.3 }}
          animate={{ opacity: isActive ? 1 : 0.3 }}
        >
          {isActive && (
            <motion.div
              className="absolute inset-0 bg-gradient-to-b from-green-400 to-green-600"
              initial={{ height: 0, top: 0 }}
              animate={{ height: "100%" }}
              transition={{ duration: 0.8, ease: "easeInOut" }}
            />
          )}
          <motion.div
            className="absolute w-2 h-2 bg-green-500 rounded-full -bottom-1 -left-0.75"
            initial={{ scale: 0, opacity: 0 }}
            animate={{
              scale: isActive ? 1 : 0,
              opacity: isActive ? 1 : 0,
            }}
            transition={{ delay: 0.8, duration: 0.3 }}
          />
        </motion.div>
      </div>
    )
  }

  if (isLastInRow) {
    // Curved connector to next row
    return (
      <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2">
        <svg width="100" height="48" viewBox="0 0 100 48" className="overflow-visible">
          <motion.path
            d="M 50 0 Q 50 24 0 48"
            stroke={isActive ? "#10b981" : "#cbd5e1"}
            strokeWidth="2"
            fill="none"
            strokeDasharray={isActive ? "0" : "5,5"}
            initial={{ pathLength: 0 }}
            animate={{ pathLength: isActive ? 1 : 0.3 }}
            transition={{ duration: 1, ease: "easeInOut" }}
          />
          {isActive && (
            <motion.circle
              cx="0"
              cy="48"
              r="4"
              fill="#10b981"
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 1, duration: 0.3 }}
            />
          )}
        </svg>
      </div>
    )
  }

  // Horizontal connector
  return (
    <div className={`absolute top-1/2 transform -translate-y-1/2 ${direction === "right" ? "-right-12" : "-left-12"}`}>
      <svg width="48" height="4" viewBox="0 0 48 4" className="overflow-visible">
        <motion.path
          d="M 0 2 L 48 2"
          stroke={isActive ? "#10b981" : "#cbd5e1"}
          strokeWidth="2"
          fill="none"
          strokeDasharray={isActive ? "0" : "3,3"}
          initial={{ pathLength: 0 }}
          animate={{ pathLength: isActive ? 1 : 0.3 }}
          transition={{ duration: 0.8, ease: "easeInOut" }}
        />
        {isActive && (
          <motion.circle
            cx="48"
            cy="2"
            r="3"
            fill="#10b981"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.3 }}
          />
        )}
      </svg>
    </div>
  )
}
