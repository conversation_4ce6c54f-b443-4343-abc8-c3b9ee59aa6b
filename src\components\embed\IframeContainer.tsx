/**
 * 通用Iframe容器组件
 * 
 * 🎯 核心职责：
 * 1. iframe环境下的唯一认证处理点 - 统一管理token和用户信息
 * 2. iframe通信 - 与父窗口的PostMessage通信
 * 3. 错误处理和加载状态管理
 * 4. 高度自适应和页面监控
 * 
 * ⚡ 架构原则：
 * - 所有embed页面组件都应通过此容器获取用户信息
 * - 不应在其他地方重复处理认证逻辑
 * - 子组件保持纯函数特性，只负责展示逻辑
 */

'use client'

import React, { useEffect, ReactNode, useRef, useState } from 'react'
import { useAuthHttp } from '@/hooks/useAuthHttp'
import { tokenManager } from '@/lib/token'

interface IframeContainerProps {
  children: ReactNode
  enableAuth?: boolean // 是否启用用户认证
  showLoading?: boolean // 是否显示加载状态
  className?: string
  style?: React.CSSProperties
}

/**
 * 通知父窗口消息
 */
function notifyParent(type: string, data: any) {
  if (window.parent && window.parent !== window) {
    window.parent.postMessage({ type, data }, '*')
  }
}

export default function IframeContainer({
  children,
  enableAuth = true,
  showLoading = true,
  className = '',
  style = {}
}: IframeContainerProps) {
  const [tokenInitialized, setTokenInitialized] = useState(false)
  const [hasValidToken, setHasValidToken] = useState(false)
  const [fromOg, setFromOg] = useState(false)
  const lastHeightRef = useRef<number>(0)
  const resizeTimeoutRef = useRef<NodeJS.Timeout>()
  
  useEffect(() => {
    // 在iframe环境中，从URL参数获取token并设置到tokenManager
    // 只执行一次避免重复请求
    const urlParams = new URLSearchParams(window.location.search)
    const urlToken = urlParams.get('token')
    const isFromOg = urlParams.get('from-og') === 'true'

    console.log('🔑 iframe环境初始化:', {
      hasUrlToken: !!urlToken,
      fromOg: isFromOg,
      url: window.location.href
    })

    // 设置from-og状态
    setFromOg(isFromOg)

    if (isFromOg && urlToken) {
      // 来自Vue3旧项目的iframe页面，使用URL中的token
      console.log('🔑 Vue3旧项目iframe：从URL参数获取token并设置到tokenManager')
      tokenManager.setToken(urlToken)
      setHasValidToken(true)
    } else if (!isFromOg && urlToken) {
      // 普通iframe嵌入，使用URL中的token
      console.log('🔑 普通iframe环境：从URL参数获取token并设置到tokenManager')
      tokenManager.setToken(urlToken)
      setHasValidToken(true)
    } else if (!isFromOg) {
      // 普通iframe嵌入但没有URL token，检查是否已有有效token
      const existingToken = tokenManager.getToken()
      setHasValidToken(!!existingToken)
      console.log('🔑 普通iframe环境：检查现有token', { hasExistingToken: !!existingToken })
    } else {
      // fromOg=true但没有token，这是错误情况
      console.warn('⚠️ Vue3旧项目iframe：from-og=true但未提供token参数')
      setHasValidToken(false)
    }

    setTokenInitialized(true)

    // 移除from-og模式下的直接就绪信号发送
    // 现在所有模式都需要等待API调用完成后再发送就绪信号
  }, []) // 空依赖数组，只执行一次

  // 使用现有的认证hook - 只有在token初始化且有有效token时才调用
  // 修正：即使from-og=true，也需要使用token请求get_current接口获取真实用户信息
  const shouldFetchUser = enableAuth && tokenInitialized && hasValidToken

  // 统一认证逻辑：IframeContainer是iframe环境下唯一的认证处理点
  console.log(`🔑 IframeContainer统一认证 - shouldFetchUser: ${shouldFetchUser}, fromOg: ${fromOg}`)
  const userState = useAuthHttp.useCurrentUserConditional(shouldFetchUser)
  
  useEffect(() => {
    // 页面加载完成通知 - 需要等待token初始化完成后才能获取fromOg状态
    if (tokenInitialized) {
      notifyParent('PAGE_LOADED', {
        timestamp: Date.now(),
        url: window.location.href,
        fromOg,
        authMode: fromOg ? 'vue3-legacy' : 'nextjs-standard'
      })
    }

    // 优化的高度调整函数
    function notifyResize() {
      // 清除之前的延时
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current)
      }
      
      // 延时执行，避免频繁调整
      resizeTimeoutRef.current = setTimeout(() => {
        const currentHeight = Math.max(
          document.body.scrollHeight,
          document.documentElement.scrollHeight
        )
        
        // 只有高度变化超过20px才通知，避免无限循环
        if (Math.abs(currentHeight - lastHeightRef.current) > 20) {
          console.log(`📏 高度调整: ${lastHeightRef.current} → ${currentHeight}`)
          lastHeightRef.current = currentHeight
          notifyParent('RESIZE', { height: currentHeight + 20 })
        }
      }, 200) // 增加延时到200ms
    }

    // 监听内容变化
    if (window.ResizeObserver) {
      const observer = new ResizeObserver(() => {
        notifyResize()
      })
      observer.observe(document.body)
      
      // 初始高度设置，延时执行确保DOM完全渲染
      setTimeout(() => {
        notifyResize()
      }, 500)
      
      return () => {
        observer.disconnect()
        if (resizeTimeoutRef.current) {
          clearTimeout(resizeTimeoutRef.current)
        }
      }
    } else {
      // 兜底方案：不太频繁的定时检查
      const interval = setInterval(notifyResize, 10000) // 改为10秒
      setTimeout(notifyResize, 500) // 延时初始调整

      return () => {
        clearInterval(interval)
        if (resizeTimeoutRef.current) {
          clearTimeout(resizeTimeoutRef.current)
        }
      }
    }
  }, [tokenInitialized, fromOg]) // 依赖token初始化状态

  useEffect(() => {
    // API调用状态通知 - 只有在应该获取用户信息时才处理
    if (!shouldFetchUser) return
    
    console.log('🔄 IframeContainer useEffect triggered:', {
      loading: userState.loading,
      hasError: !!userState.error,
      hasValue: !!userState.data,
      errorMessage: userState.error?.message
    })
    
    if (userState.loading) {
      notifyParent('API_CALL', {
        method: 'GET',
        url: '/api/user/get_current',
        timestamp: new Date().toISOString(),
        token: tokenManager.getToken() ? '已设置' : '未设置'
      })
    } else if (userState.error) {
      notifyParent('API_ERROR', {
        url: '/api/user/get_current',
        message: userState.error.message || '获取用户信息失败',
        tokenStatus: tokenManager.getToken() ? '已设置' : '未设置'
      })
    } else if (userState.data) {
      notifyParent('API_SUCCESS', {
        url: '/api/user/get_current',
        message: '用户信息获取成功',
        user: userState.data.user_name
      })
      
      // API成功后发送iframe就绪信号
      notifyParent('IFRAME_READY', {
        timestamp: new Date().toISOString(),
        url: window.location.href,
        fromOg,
        authMode: fromOg ? 'vue3-legacy-with-api' : 'nextjs-standard',
        message: fromOg
          ? 'Vue3旧项目iframe就绪，已通过token获取用户信息'
          : 'Next.js标准iframe就绪，用户认证完成'
      })
    }
  }, [shouldFetchUser, userState.loading, userState.error, userState.data, fromOg])

  useEffect(() => {
    // 全局错误处理
    const handleError = (event: ErrorEvent) => {
      notifyParent('ERROR', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      })
    }

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      notifyParent('ERROR', {
        message: 'Unhandled Promise Rejection',
        reason: event.reason
      })
    }

    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)

    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])

  // 等待token初始化
  if (!tokenInitialized) {
    return (
      <div className={`iframe-container initializing-container ${className}`} style={{
        width: '100%',
        height: '100%',
        padding: '20px',
        textAlign: 'center',
        ...style
      }}>
        <div style={{
          background: '#f8f9fa',
          padding: '40px',
          borderRadius: '8px',
          border: '1px solid #e9ecef'
        }}>
          <div style={{ fontSize: '18px', marginBottom: '10px' }}>🔧 初始化中...</div>
          <div style={{ color: '#6c757d' }}>正在设置认证信息</div>
        </div>
      </div>
    )
  }

  // 检查是否有有效token
  if (enableAuth && !hasValidToken) {
    const errorMessage = fromOg
      ? '来自Vue3旧项目的iframe页面需要提供token参数'
      : '未提供有效的认证token'

    const formatHint = fromOg
      ? '格式: ?from-og=true&token=YOUR_JWT_TOKEN'
      : '格式: ?token=YOUR_JWT_TOKEN'

    notifyParent('AUTH_ERROR', {
      message: errorMessage,
      tokenStatus: '未设置',
      fromOg
    })

    return (
      <div className={`iframe-container error-container ${className}`} style={{
        width: '100%',
        height: '100%',
        padding: '20px',
        textAlign: 'center',
        ...style
      }}>
        <div style={{
          background: '#f8d7da',
          color: '#721c24',
          padding: '20px',
          borderRadius: '8px',
          border: '1px solid #f5c6cb'
        }}>
          <h3 style={{ margin: '0 0 10px 0' }}>❌ 认证错误</h3>
          <p style={{ margin: 0 }}>{errorMessage}</p>
          <p style={{ margin: '10px 0 0 0', fontSize: '12px', opacity: 0.7 }}>
            {formatHint}
          </p>
          {fromOg && (
            <p style={{ margin: '10px 0 0 0', fontSize: '12px', opacity: 0.7 }}>
              Vue3旧项目iframe模式：需要使用token获取用户信息
            </p>
          )}
        </div>
      </div>
    )
  }

  // 认证错误处理
  if (enableAuth && shouldFetchUser && userState.error) {
    notifyParent('AUTH_ERROR', {
      message: userState.error.message || '认证失败',
      tokenStatus: tokenManager.getToken() ? '已设置' : '未设置'
    })
    
    return (
      <div className={`iframe-container error-container ${className}`} style={{
        width: '100%',
        height: '100%',
        padding: '20px',
        textAlign: 'center',
        ...style
      }}>
        <div style={{
          background: '#f8d7da',
          color: '#721c24',
          padding: '20px',
          borderRadius: '8px',
          border: '1px solid #f5c6cb'
        }}>
          <h3 style={{ margin: '0 0 10px 0' }}>❌ 认证错误</h3>
          <p style={{ margin: 0 }}>{userState.error.message}</p>
          <p style={{ margin: '10px 0 0 0', fontSize: '12px', opacity: 0.7 }}>
            Token状态: {tokenManager.getToken() ? '已设置' : '未设置'}
          </p>
        </div>
      </div>
    )
  }

  // 加载状态
  if (enableAuth && showLoading && shouldFetchUser && userState.loading) {
    return (
      <div className={`iframe-container loading-container ${className}`} style={{
        width: '100%',
        height: '100%',
        padding: '20px',
        textAlign: 'center',
        ...style
      }}>
        <div style={{
          background: '#f8f9fa',
          padding: '40px',
          borderRadius: '8px',
          border: '1px solid #e9ecef'
        }}>
          <div style={{ fontSize: '18px', marginBottom: '10px' }}>🔄 加载中...</div>
          <div style={{ color: '#6c757d' }}>正在获取用户信息</div>
          <div style={{ fontSize: '12px', color: '#6c757d', marginTop: '10px' }}>
            Token状态: {tokenManager.getToken() ? '已设置' : '未设置'}
          </div>
        </div>
      </div>
    )
  }

  // 递归地为所有子组件注入user信息
  const injectUserProps = (element: React.ReactNode): React.ReactNode => {
    if (!React.isValidElement(element)) {
      return element
    }

    const userProps = {
      user: shouldFetchUser ? userState.data : null,
      userLoading: shouldFetchUser ? userState.loading : false,
      userError: shouldFetchUser ? userState.error : null,
      fromOg,
      token: hasValidToken ? tokenManager.getToken() : null
    }

    // 如果元素有children，递归处理
    const newChildren = React.Children.map(element.props.children, injectUserProps)

    return React.cloneElement(element, userProps, newChildren)
  }

  // 正常内容渲染
  return (
    <div className={`iframe-container ${fromOg ? 'from-og' : ''} ${className}`} style={{
      width: '100%',
      height: '100%',
      padding: '20px',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      background: '#fff',
      ...style
    }}>
      {injectUserProps(children)}
    </div>
  )
} 