# Iframe 集成文档总结

## 📋 文档概述

基于最新的 `src/app/[locale]/embed` 逻辑，为外部服务提供完整的iframe集成指南。

## 🎯 核心功能

### 支持的页面
1. **Dashboard** - `/[locale]/embed/dashboard` - 主要仪表板界面
2. **Report** - `/[locale]/embed/report` - 报告和分析界面  
3. **Test** - `/[locale]/embed/test` - 测试和调试界面

### 认证机制
- **JWT Token认证** - 必需参数，通过URL传递
- **自动验证** - 调用 `/api/user/get_current` 验证token
- **错误处理** - 认证失败时显示友好错误页面

### 通信机制
- **PostMessage通信** - 完整的生命周期消息
- **高度自适应** - 自动调整iframe高度
- **错误监控** - 全面的错误报告和处理

## 📄 文档结构

### 主要章节
1. **快速开始** - 基本iframe嵌入示例
2. **URL参数配置** - 详细的参数说明
3. **认证机制** - JWT token要求和流程
4. **PostMessage通信** - 完整的消息类型和格式
5. **JavaScript集成** - 基本和高级集成示例
6. **样式定制** - CSS样式和响应式设计
7. **最佳实践** - Token管理、错误处理、性能监控
8. **测试工具** - 完整的测试页面
9. **故障排除** - 常见问题和解决方案

### 关键特性
- ✅ **完整的API文档** - 所有参数和消息类型
- ✅ **实用代码示例** - HTML、JavaScript、React示例
- ✅ **最佳实践指南** - Token管理、错误处理、性能优化
- ✅ **测试工具** - 可直接使用的测试页面
- ✅ **故障排除** - 常见问题和解决方案

## 🔧 技术亮点

### 1. 完整的消息系统
```javascript
// 支持的消息类型
- PAGE_LOADED    // 页面加载完成
- IFRAME_READY   // iframe就绪
- API_CALL       // API调用开始
- API_SUCCESS    // API调用成功
- API_ERROR      // API调用失败
- AUTH_ERROR     // 认证错误
- RESIZE         // 高度调整
- ERROR          // 一般错误
```

### 2. 高级集成示例
- **React组件** - 完整的React集成示例
- **Token管理** - 自动刷新和更新机制
- **错误处理** - 重试机制和错误恢复
- **多页面管理** - 动态切换不同embed页面
- **性能监控** - 加载时间和性能指标

### 3. 测试工具
- **交互式测试页面** - 可直接测试不同配置
- **实时日志** - 详细的消息和错误日志
- **参数配置** - 灵活的测试参数设置

## 📊 使用示例

### 基本使用
```html
<iframe 
  src="https://your-domain.com/zh/embed/dashboard?token=YOUR_JWT_TOKEN"
  width="100%" 
  height="600px"
  frameborder="0">
</iframe>
```

### 高级使用（React）
```jsx
<VibeIframe 
  token="YOUR_JWT_TOKEN"
  page="dashboard"
  locale="zh"
/>
```

### 消息监听
```javascript
window.addEventListener('message', function(event) {
  if (event.data.type === 'IFRAME_READY') {
    console.log('Iframe已就绪');
  }
});
```

## 🎯 文档价值

### 对外部开发者
- **快速集成** - 5分钟内完成基本集成
- **完整参考** - 所有参数和API的详细说明
- **最佳实践** - 避免常见陷阱和问题
- **测试工具** - 快速验证集成效果

### 对产品团队
- **标准化** - 统一的集成标准和规范
- **可维护性** - 清晰的文档结构和版本管理
- **支持效率** - 减少技术支持工作量
- **用户体验** - 提升第三方集成体验

## 📋 文件清单

### 主要文件
- `iframe-integration-guide.md` - 完整的集成指南（888行）
- `iframe-integration-summary.md` - 文档总结（本文件）

### 文档特点
- **详细完整** - 覆盖所有集成场景
- **实用性强** - 大量可直接使用的代码示例
- **易于维护** - 清晰的结构和版本管理
- **用户友好** - 从基础到高级的渐进式指南

---

*总结完成时间：2025-01-27*
*文档状态：✅ 完整可用*
