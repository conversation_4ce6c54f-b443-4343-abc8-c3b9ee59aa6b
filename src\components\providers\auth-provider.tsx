'use client'

import { useEffect, useState, ReactNode } from 'react'
import { usePathname } from 'next/navigation'
import { useAuth } from '@/hooks/use-auth-selectors'
import { Skeleton } from '@/components/ui/skeleton'
import { tokenManager } from '@/lib/token'
import { motion } from "framer-motion"
import IframeAccessGuard from '@/components/iframe-access-guard'
// import { FetchInterceptorProvider } from './fetch-interceptor-provider' // 不再需要，使用新的useHttp系统
interface AuthProviderProps {
  children: ReactNode
}

/**
 * 认证状态提供者
 * 在应用启动时验证用户token并初始化认证状态
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const { validateToken, clearAuth, user, isAuthenticated } = useAuth()
  const [isInitialized, setIsInitialized] = useState(false)
  const [isValidating, setIsValidating] = useState(false)
  const pathname = usePathname()

  // 只在应用启动时或用户状态丢失时验证，避免路由变化时的重复请求
  useEffect(() => {
    const initAuth = async () => {
      // 跳过不需要认证的路径（支持国际化路由）
      const isEmbedPath = pathname.startsWith('/embed/') || pathname.match(/^\/[a-z]{2}\/embed\//)
      const isUIGalleryPath = pathname.startsWith('/ui-gallery') || pathname.match(/^\/[a-z]{2}\/ui-gallery/)
      const isLoginPath = pathname.startsWith('/login') || pathname.match(/^\/[a-z]{2}\/login/)
      
      if (isEmbedPath || isUIGalleryPath || isLoginPath) {
        console.log('🔓 AuthProvider: 跳过认证检查 -', pathname)
        
        // 🔧 确保auth store状态一致，避免PublicRoute等组件的异步检查
        const token = tokenManager.getToken()
        if (!token) {
          // 如果没有token，确保清除认证状态
          clearAuth()
        }
        
        setIsInitialized(true)
        return
      }

      // 🔧 如果已经有用户信息且正在验证中，直接返回
      if (isValidating) {
        console.log('🔄 AuthProvider: 验证中，跳过重复请求')
        return
      }

      // 🔧 如果已经认证且有用户信息，且已初始化，不需要重新验证
      if (isAuthenticated && user && isInitialized) {
        console.log('✅ AuthProvider: 已认证，跳过验证 -', pathname)
        return
      }

      console.log('🔍 AuthProvider: 开始验证用户信息 -', pathname)
      setIsValidating(true)
      setIsInitialized(false)
      
      try {
        const isValid = await validateToken()
        
        if (!isValid) {
          clearAuth()
        }
      } catch (error) {
        console.error('Auth initialization failed:', error)
        clearAuth()
      } finally {
        setIsInitialized(true)
        setIsValidating(false)
      }
    }

    initAuth()
  }, [pathname]) // 🔧移除validateToken, clearAuth依赖，避免因函数引用变化导致的重复调用

  // 显示加载状态直到认证初始化完成
  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center gap-1 h-full w-full">
            {[...Array(3)].map((_, i) => (
                <motion.div
                    key={i}
                    className="h-3 w-3 rounded-full bg-blue-500"
                    initial={{ x: 0 }}
                    animate={{
                        x: [0, 10, 0],
                        opacity: [0.5, 1, 0.5],
                        scale: [1, 1.2, 1],
                    }}
                    transition={{
                        duration: 1,
                        repeat: Infinity,
                        delay: i * 0.2,
                    }}
                />
            ))}
        </div>
    )
  }

  return <>{children}</>
}

/**
 * 受保护的路由组件
 * 要求用户必须登录才能访问
 */
interface ProtectedRouteProps {
  children: ReactNode
  fallback?: ReactNode
}

export function ProtectedRoute({ children, fallback }: ProtectedRouteProps) {
  const { isAuthenticated, isLoading } = useAuth()

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="space-y-4 w-full max-w-md">
          <div className="text-center">
            <h2 className="text-lg font-semibold mb-4">验证身份中...</h2>
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </div>
      </div>
    )
  }

  // 如果未认证，显示fallback或重定向到登录页
  if (!isAuthenticated) {
    if (fallback) {
      return <>{fallback}</>
    }

    // 在客户端重定向到登录页，支持国际化路由
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname
      const targetPath = currentPath === '/' ? '/' : currentPath
      
      // 检测当前语言或使用默认语言
      const localeMatch = currentPath.match(/^\/([a-z]{2})\//)
      const locale = localeMatch ? localeMatch[1] : 'zh'
      
      window.location.href = `/${locale}/login?redirect=${encodeURIComponent(targetPath)}`
    }
    
    return null
  }

  // 集成iframe访问守卫
  return (
    <IframeAccessGuard requireAuth={true} isAuthenticated={isAuthenticated}>
      {children}
    </IframeAccessGuard>
  )
}

/**
 * 公开路由组件
 * 已登录用户访问时重定向到主页面
 */
interface PublicRouteProps {
  children: ReactNode
  redirectTo?: string
}

export function PublicRoute({ children, redirectTo = '/' }: PublicRouteProps) {
  const { isAuthenticated, isLoading } = useAuth()

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="space-y-4 w-full max-w-md">
          <div className="text-center">
            <h2 className="text-lg font-semibold mb-4">检查登录状态...</h2>
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </div>
      </div>
    )
  }

  // 如果已认证，重定向到指定页面（支持国际化）
  if (isAuthenticated) {
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname
      const localeMatch = currentPath.match(/^\/([a-z]{2})\//)
      const locale = localeMatch ? localeMatch[1] : 'zh'
      
      // 如果redirectTo是相对路径，添加语言前缀
      const finalRedirectTo = redirectTo.startsWith('/') && !redirectTo.match(/^\/[a-z]{2}\//) 
        ? `/${locale}${redirectTo}` 
        : redirectTo
      
      window.location.href = finalRedirectTo
    }
    return null
  }

  return <>{children}</>
}

/**
 * 权限路由组件
 * 基于权限控制访问
 */
interface PermissionRouteProps {
  children: ReactNode
  permissions?: string[]
  features?: string[]
  roles?: string[]
  fallback?: ReactNode
}

export function PermissionRoute({ 
  children, 
  permissions, 
  features, 
  roles, 
  fallback 
}: PermissionRouteProps) {
  const { isAuthenticated, checkAccess } = useAuth()

  // 如果未认证，不显示内容
  if (!isAuthenticated) {
    return null
  }

  // 检查权限
  const accessParams: any = {}
  if (permissions) accessParams.permissions = permissions
  if (features) accessParams.features = features
  if (roles) accessParams.roles = roles

  const hasAccess = checkAccess(accessParams)

  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            访问受限
          </h3>
          <p className="text-gray-600 dark:text-gray-300">
            您没有权限访问此内容
          </p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
