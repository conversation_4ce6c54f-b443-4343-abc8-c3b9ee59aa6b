# Vibe Coding Refactory - 详细开发文档

## 📋 项目概览

### 技术栈
- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript 5.7
- **样式**: TailwindCSS v4 + CSS-first配置
- **状态管理**: Zustand 5.0
- **UI组件**: Radix UI + shadcn/ui
- **图标**: Lucide React
- **动画**: Framer Motion
- **工具库**: react-use
- **通知**: Sonner
- **构建工具**: Turbopack

### 项目架构
```
src/
├── app/                    # Next.js App Router页面
│   ├── (auth)/            # 认证相关页面组
│   ├── (dashboard)/       # 仪表板页面组
│   ├── (public)/          # 公开页面组
│   ├── api/               # API路由
│   ├── dashboard/         # 仪表板子页面
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 首页
│   ├── loading.tsx        # 全局加载页面
│   ├── error.tsx          # 全局错误页面
│   └── not-found.tsx      # 404页面
├── components/            # 可复用组件
│   ├── ui/               # 基础UI组件
│   ├── forms/            # 表单组件
│   ├── layout/           # 布局组件
│   └── providers/        # Context提供者
├── hooks/                # 自定义Hooks
├── lib/                  # 工具库和配置
│   ├── auth/            # 认证相关工具
│   ├── middleware/      # 中间件
│   └── utils.ts         # 通用工具函数
├── stores/              # Zustand状态管理
├── types/               # TypeScript类型定义
└── middleware.ts        # Next.js中间件
```

## 🚀 项目初始化和启动

### 1. 环境要求
```bash
Node.js >= 18.17.0
npm >= 9.0.0
```

### 2. 安装依赖
```bash
npm install
```

### 3. 环境配置
创建 `.env.local` 文件：
```env
# API配置
NEXT_PUBLIC_API_BASE_URL=https://dev.goglobalsp.com
NEXT_PUBLIC_ENV=development

# 其他配置
NEXT_PUBLIC_APP_NAME=Vibe Coding Refactory
```

### 4. 启动开发服务器
```bash
# 开发模式 (使用Turbopack)
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm run start

# 类型检查
npm run type-check

# 代码格式化
npm run format
```

## 🏗️ 核心架构详解

### 1. Next.js App Router结构

#### 路由组织
- `(auth)`: 认证相关页面，包含登录、注册等
- `(dashboard)`: 需要认证的仪表板页面
- `(public)`: 公开访问的页面
- `api`: API路由处理器

#### 特殊文件
- `layout.tsx`: 布局文件，定义页面结构
- `page.tsx`: 页面组件
- `loading.tsx`: 加载状态
- `error.tsx`: 错误处理
- `not-found.tsx`: 404页面

### 2. 状态管理系统 (Zustand)

#### 核心Store结构
```typescript
// src/stores/auth-store.ts
interface AuthState {
  user: User | null
  isAuthenticated: boolean
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => Promise<void>
  getCurrentUser: () => Promise<void>
}

// src/stores/ui-store.ts
interface UIState {
  notifications: Notification[]
  addNotification: (notification: Notification) => void
  removeNotification: (id: string) => void
}

// src/stores/app-store.ts
interface AppState {
  preferences: UserPreferences
  updatePreferences: (preferences: Partial<UserPreferences>) => void
}
```

#### 状态持久化
使用Zustand的persist中间件实现状态持久化：
```typescript
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // state and actions
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
)
```

### 3. 认证系统

#### Token管理
- 基于Cookies + localStorage双重存储
- 环境隔离 (dev_/prod_前缀)
- 自动过期处理

#### 路由守卫
5层路由保护机制：
1. **认证守卫**: 验证用户登录状态
2. **访问控制守卫**: 检查页面访问权限
3. **业务流程守卫**: 控制业务流程步骤
4. **菜单守卫**: 动态菜单权限
5. **反馈守卫**: 特定路径重定向

### 4. API系统

#### 反向代理
```typescript
// src/app/api/[...proxy]/route.ts
export async function POST(request: Request, { params }: { params: { proxy: string[] } }) {
  const proxyPath = params.proxy.join('/')
  const targetUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/${proxyPath}`
  
  // 转发请求到后端API
  const response = await fetch(targetUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'authorization': token,
      'language': language,
    },
    body: JSON.stringify(body),
  })
  
  return response
}
```

#### 响应拦截器
```typescript
// src/lib/api-interceptor.ts
export async function apiRequest<T>(
  url: string,
  options: RequestInit = {}
): Promise<T> {
  const response = await fetch(url, options)
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }
  
  const data = await response.json()
  
  // 自动提取data字段
  return data.data || data
}
```

## 📄 如何开发新页面

### 1. 确定页面类型和路由

#### 选择页面组
- **公开页面**: 放在 `src/app/(public)/` 下
- **认证页面**: 放在 `src/app/(auth)/` 下  
- **仪表板页面**: 放在 `src/app/(dashboard)/` 下

#### 创建页面文件
```bash
# 例如创建一个新的仪表板页面
mkdir src/app/(dashboard)/my-new-page
touch src/app/(dashboard)/my-new-page/page.tsx
```

### 2. 页面组件基础模板

```typescript
'use client'

import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useState } from 'react'

export default function MyNewPage() {
  const [loading, setLoading] = useState(false)

  const breadcrumbs = [
    { title: "首页", href: "/" },
    { title: "我的新页面", isActive: true },
  ]

  return (
    <DashboardLayout breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            我的新页面
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2">
            页面描述信息
          </p>
        </div>

        {/* 页面内容 */}
        <Card>
          <CardHeader>
            <CardTitle>卡片标题</CardTitle>
            <CardDescription>卡片描述</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => setLoading(!loading)}>
              {loading ? '加载中...' : '点击按钮'}
            </Button>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
```

### 3. 添加权限控制 (可选)

如果页面需要特定权限：
```typescript
import { ProtectedRoute, PermissionRoute } from '@/components/providers/auth-provider'

export default function MyNewPage() {
  return (
    <ProtectedRoute>
      <PermissionRoute features={['specific_feature']}>
        <MyNewPageContent />
      </PermissionRoute>
    </ProtectedRoute>
  )
}
```

### 4. 状态管理集成

#### 使用现有Store
```typescript
import { useAuth, useUI } from '@/hooks/use-auth-selectors'

function MyNewPage() {
  const { user, isAuthenticated } = useAuth()
  const { addNotification } = useUI()

  const handleAction = () => {
    addNotification({
      type: 'success',
      title: '操作成功',
      message: '您的操作已完成'
    })
  }

  return (
    // 页面内容
  )
}
```

### 6. 添加到导航菜单

#### 更新侧边栏配置
```typescript
// src/components/app-sidebar.tsx
function getMenuItems(key: string): Array<{ title: string; url: string }> {
  const items: Record<string, Array<{ title: string; url: string }>> = {
    dashboard: [
      { title: "概览", url: "/dashboard" },
      { title: "分析", url: "/dashboard/analytics" },
      { title: "我的新页面", url: "/my-new-page" }, // 添加新页面
    ],
    // ... 其他菜单项
  }
  return items[key] || []
}
```

### 7. 类型定义

#### 创建页面相关类型
```typescript
// src/types/my-feature.ts
export interface MyFeatureData {
  id: string
  name: string
  description: string
  createdAt: string
  updatedAt: string
}

export interface MyFeatureFilters {
  search?: string
  category?: string
  status?: 'active' | 'inactive'
}

export interface MyFeatureFormData {
  name: string
  description: string
}
```

#### 更新全局类型导出
```typescript
// src/types/index.ts
export * from './my-feature'
```

## 🎨 UI组件开发

### 1. 使用现有UI组件

项目使用shadcn/ui组件库，所有组件都在 `src/components/ui/` 目录下：

```typescript
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
```

### 2. 创建自定义组件

#### 组件文件结构
```typescript
// src/components/my-feature/my-component.tsx
'use client'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'

interface MyComponentProps {
  title: string
  description?: string
  variant?: 'default' | 'secondary'
  className?: string
  onClick?: () => void
}

export function MyComponent({
  title,
  description,
  variant = 'default',
  className,
  onClick
}: MyComponentProps) {
  return (
    <div className={cn(
      'p-4 border rounded-lg',
      variant === 'default' && 'bg-white dark:bg-gray-800',
      variant === 'secondary' && 'bg-gray-50 dark:bg-gray-900',
      className
    )}>
      <h3 className="text-lg font-semibold">{title}</h3>
      {description && (
        <p className="text-gray-600 dark:text-gray-300 mt-1">{description}</p>
      )}
      {onClick && (
        <Button onClick={onClick} className="mt-3">
          点击操作
        </Button>
      )}
    </div>
  )
}
```

#### 组件导出
```typescript
// src/components/my-feature/index.ts
export { MyComponent } from './my-component'
export type { MyComponentProps } from './my-component'
```

### 3. 表单组件开发

#### 表单组件模板
```typescript
// src/components/forms/my-feature-form.tsx
'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface MyFeatureFormProps {
  initialData?: MyFeatureFormData
  onSubmit: (data: MyFeatureFormData) => Promise<void>
  onCancel?: () => void
}

export function MyFeatureForm({ initialData, onSubmit, onCancel }: MyFeatureFormProps) {
  const [formData, setFormData] = useState<MyFeatureFormData>({
    name: initialData?.name || '',
    description: initialData?.description || '',
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = '名称不能为空'
    }

    if (!formData.description.trim()) {
      newErrors.description = '描述不能为空'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      await onSubmit(formData)
    } catch (error) {
      console.error('Form submission error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{initialData ? '编辑' : '创建'}功能</CardTitle>
        <CardDescription>
          {initialData ? '修改' : '填写'}功能信息
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">名称</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="输入名称"
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">描述</Label>
            <Input
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="输入描述"
              className={errors.description ? 'border-red-500' : ''}
            />
            {errors.description && (
              <p className="text-sm text-red-500">{errors.description}</p>
            )}
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                取消
              </Button>
            )}
            <Button type="submit" disabled={loading}>
              {loading ? '提交中...' : '提交'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
```

## 🔧 开发工具和最佳实践

### 1. TypeScript配置

项目使用严格的TypeScript配置：
```json
// tsconfig.json
{
  "compilerOptions": {
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true
  }
}
```

### 2. 代码格式化

使用Prettier进行代码格式化：
```bash
# 格式化所有文件
npm run format

# 检查格式化
npm run format:check
```

### 3. 类型检查

```bash
# 运行TypeScript类型检查
npm run type-check
```

### 4. 构建和部署

```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm run start
```

## 🚨 常见问题和解决方案

### 1. TypeScript错误

**问题**: 找不到模块或类型声明
```
Cannot find module '@/components/ui/button' or its corresponding type declarations.
```

**解决方案**:
- 检查文件路径是否正确
- 确认组件文件存在
- 重启TypeScript服务器
- 检查tsconfig.json中的路径映射

### 2. 样式问题

**问题**: TailwindCSS类名不生效

**解决方案**:
- 确认类名拼写正确
- 检查是否需要使用`cn()`函数合并类名
- 确认组件使用了正确的暗色模式类名

### 3. 状态管理问题

**问题**: Zustand状态不持久化

**解决方案**:
- 检查persist配置是否正确
- 确认localStorage可用
- 检查状态序列化是否有问题

### 4. API调用问题

**问题**: API请求失败或跨域错误

**解决方案**:
- 检查API代理配置
- 确认环境变量设置正确
- 检查Token是否有效
- 查看网络请求日志

## 📚 参考资源

### 官方文档
- [Next.js 15 文档](https://nextjs.org/docs)
- [React 18 文档](https://react.dev/)
- [TypeScript 文档](https://www.typescriptlang.org/docs/)
- [TailwindCSS v4 文档](https://tailwindcss.com/docs)
- [Zustand 文档](https://zustand-demo.pmnd.rs/)
- [Radix UI 文档](https://www.radix-ui.com/)

### 项目特定文档
- `migration-docs/`: 项目迁移相关文档
- `README.md`: 项目基础信息
- `Todo.md`: 项目进度和任务清单

### 开发工具
- VS Code + TypeScript插件
- React Developer Tools
- TailwindCSS IntelliSense
- Prettier插件
- ESLint插件

---

## 🎯 快速开始新页面开发

### 完整示例：创建用户管理页面

1. **创建页面文件**
```bash
mkdir src/app/(dashboard)/users
touch src/app/(dashboard)/users/page.tsx
```

2. **实现页面组件**
```typescript
// src/app/(dashboard)/users/page.tsx
'use client'

import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useState, useEffect } from 'react'
import { Users, Search, Plus } from 'lucide-react'

interface User {
  id: string
  name: string
  email: string
  role: string
  status: 'active' | 'inactive'
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')

  const breadcrumbs = [
    { title: "首页", href: "/" },
    { title: "用户管理", isActive: true },
  ]

  useEffect(() => {
    // 模拟API调用
    const fetchUsers = async () => {
      setLoading(true)
      // 这里替换为真实的API调用
      setTimeout(() => {
        setUsers([
          { id: '1', name: '张三', email: '<EMAIL>', role: '管理员', status: 'active' },
          { id: '2', name: '李四', email: '<EMAIL>', role: '用户', status: 'active' },
        ])
        setLoading(false)
      }, 1000)
    }

    fetchUsers()
  }, [])

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <DashboardLayout breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">用户管理</h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">
              管理系统用户和权限
            </p>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            添加用户
          </Button>
        </div>

        {/* 搜索和筛选 */}
        <div className="flex items-center space-x-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索用户..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* 用户列表 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>用户列表</span>
            </CardTitle>
            <CardDescription>
              共 {filteredUsers.length} 个用户
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">加载中...</div>
            ) : (
              <div className="space-y-4">
                {filteredUsers.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                          {user.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {user.name}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          {user.email}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{user.role}</Badge>
                      <Badge variant={user.status === 'active' ? 'default' : 'secondary'}>
                        {user.status === 'active' ? '活跃' : '非活跃'}
                      </Badge>
                      <Button variant="outline" size="sm">
                        编辑
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
```

3. **添加到导航菜单**
```typescript
// src/components/app-sidebar.tsx
const items: Record<string, Array<{ title: string; url: string }>> = {
  dashboard: [
    { title: "概览", url: "/dashboard" },
    { title: "分析", url: "/dashboard/analytics" },
    { title: "用户管理", url: "/users" }, // 添加新页面
  ],
  // ... 其他菜单项
}
```

4. **测试页面**
```bash
# 启动开发服务器
npm run dev

# 访问新页面
http://localhost:3000/users
```

这样就完成了一个完整的新页面开发！🎉
```

#### 创建新的Store (如需要)
```typescript
// src/stores/my-feature-store.ts
import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

interface MyFeatureState {
  data: any[]
  loading: boolean
  fetchData: () => Promise<void>
  updateData: (data: any) => void
}

export const useMyFeatureStore = create<MyFeatureState>()(
  persist(
    (set, get) => ({
      data: [],
      loading: false,
      
      fetchData: async () => {
        set({ loading: true })
        try {
          // API调用
          const response = await fetch('/api/my-feature')
          const data = await response.json()
          set({ data, loading: false })
        } catch (error) {
          set({ loading: false })
          throw error
        }
      },
      
      updateData: (newData) => {
        set(state => ({
          data: [...state.data, newData]
        }))
      }
    }),
    {
      name: 'my-feature-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
)
```

### 5. API集成 (推荐使用统一API Hook)

#### 使用统一API Hook (推荐)
项目提供了基于react-use的统一API Hook，集成了所有请求拦截器和错误处理：

```typescript
// 1. 基础API Hook - 手动触发
import { useApi } from '@/hooks/use-api'

function MyComponent() {
  const [state, execute] = useApi<MyFeatureData[]>('/api/my-feature', {
    showError: true,
    showSuccess: false,
  })

  const handleFetch = async () => {
    try {
      const data = await execute()
      console.log('获取到数据:', data)
    } catch (error) {
      console.error('请求失败:', error)
    }
  }

  return (
    <Button onClick={handleFetch} disabled={state.loading}>
      {state.loading ? '加载中...' : '获取数据'}
    </Button>
  )
}

// 2. 自动执行API Hook - 组件加载时自动执行
import { useApiAuto } from '@/hooks/use-api'

function MyComponent() {
  const state = useApiAuto<MyFeatureData[]>('/api/my-feature', {
    showError: true,
    showSuccess: false,
  })

  if (state.loading) return <div>加载中...</div>
  if (state.error) return <div>加载失败: {state.error.message}</div>
  if (state.value) return <div>数据: {JSON.stringify(state.value)}</div>

  return null
}

// 3. RESTful API Hook - 完整CRUD操作
import { useRestApi } from '@/hooks/use-api'

function MyComponent() {
  const api = useRestApi<MyFeatureData>('/api/my-feature')

  const handleCreate = async () => {
    await api.create({ name: '新项目', description: '描述' })
    api.fetchList() // 重新获取列表
  }

  const handleUpdate = async (id: string) => {
    await api.update(id, { name: '更新后的名称' })
    api.fetchList()
  }

  const handleDelete = async (id: string) => {
    await api.remove(id)
    api.fetchList()
  }

  return (
    <div>
      <Button onClick={api.fetchList}>获取列表</Button>
      <Button onClick={handleCreate}>创建</Button>
      {/* 其他操作 */}
    </div>
  )
}

// 4. 预配置API Hook - 使用预定义配置
import { useApiPresets } from '@/hooks/use-api'

function MyComponent() {
  // 静默请求（不显示任何提示）
  const [silentState, silentFetch] = useApiPresets.silent<MyFeatureData[]>('/api/my-feature')

  // 认证相关请求
  const [authState, authFetch] = useApiPresets.auth<LoginResponse>('/api/auth/login', {
    method: 'POST',
    skipAuth: true,
  })

  // 保存操作
  const [saveState, save] = useApiPresets.save<MyFeatureData>('/api/my-feature', {
    method: 'POST',
  })

  return (
    <div>
      <Button onClick={() => silentFetch()}>静默请求</Button>
      <Button onClick={() => save({ body: JSON.stringify(data) })}>保存数据</Button>
    </div>
  )
}

// 5. 分页API Hook
import { usePaginatedApi } from '@/hooks/use-api'

function MyComponent() {
  const paginatedData = usePaginatedApi<MyFeatureData>('/api/my-feature', {
    page: 1,
    pageSize: 10
  })

  return (
    <div>
      {/* 搜索 */}
      <Input
        placeholder="搜索..."
        onChange={(e) => paginatedData.updateParams({
          search: e.target.value,
          page: 1
        })}
      />

      {/* 数据列表 */}
      {paginatedData.value?.data.map(item => (
        <div key={item.id}>{item.name}</div>
      ))}

      {/* 分页控制 */}
      <Button
        onClick={paginatedData.prevPage}
        disabled={!paginatedData.hasPrevPage}
      >
        上一页
      </Button>
      <Button
        onClick={paginatedData.nextPage}
        disabled={!paginatedData.hasNextPage}
      >
        下一页
      </Button>
    </div>
  )
}
```

#### 传统API函数方式 (不推荐)
如果需要在非React组件中使用，可以创建传统的API函数：

```typescript
// src/lib/my-feature-api.ts
import { apiRequest } from '@/lib/api-interceptor'

export interface MyFeatureData {
  id: string
  name: string
  description: string
}

export const myFeatureApi = {
  getAll: (): Promise<MyFeatureData[]> =>
    apiRequest('/api/my-feature'),

  getById: (id: string): Promise<MyFeatureData> =>
    apiRequest(`/api/my-feature/${id}`),

  create: (data: Omit<MyFeatureData, 'id'>): Promise<MyFeatureData> =>
    apiRequest('/api/my-feature', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),
}
```

#### 在组件中使用API
```typescript
import { useEffect, useState } from 'react'
import { myFeatureApi, MyFeatureData } from '@/lib/my-feature-api'
import { useUI } from '@/hooks/use-auth-selectors'

function MyNewPage() {
  const [data, setData] = useState<MyFeatureData[]>([])
  const [loading, setLoading] = useState(true)
  const { addNotification } = useUI()

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await myFeatureApi.getAll()
        setData(result)
      } catch (error) {
        addNotification({
          type: 'error',
          title: '加载失败',
          message: '无法加载数据，请稍后重试'
        })
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [addNotification])

  const handleCreate = async (newData: Omit<MyFeatureData, 'id'>) => {
    try {
      const created = await myFeatureApi.create(newData)
      setData(prev => [...prev, created])
      addNotification({
        type: 'success',
        title: '创建成功',
        message: '数据已成功创建'
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: '创建失败',
        message: '无法创建数据，请稍后重试'
      })
    }
  }

  if (loading) {
    return <div>加载中...</div>
  }

  return (
    // 页面内容
  )
}
```
