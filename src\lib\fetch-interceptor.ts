/**
 * 全局Fetch拦截器
 * 在最底层重写fetch函数，确保所有HTTP请求都经过统一拦截
 */

import { getBackendLanguage, type Locale } from '@/lib/i18n/config'

// 保存原始的fetch函数
const originalFetch = globalThis.fetch

// 拦截器状态
let isInterceptorInstalled = false

/**
 * 获取当前用户语言设置
 * 优先级：URL路径语言 > localStorage用户语言 > 默认中文
 */
function getCurrentLanguage(): string {
  if (typeof window === 'undefined') {
    return 'chinese' // 服务端默认中文
  }

  try {
    // 1. 优先从URL路径获取语言（适用于国际化路由）
    const pathname = window.location.pathname
    const pathSegments = pathname.split('/').filter(Boolean)
    
    if (pathSegments.length > 0) {
      const firstSegment = pathSegments[0]
      // 检查是否是有效的语言代码
      if (['zh', 'en', 'ja'].includes(firstSegment)) {
        return getBackendLanguage(firstSegment as Locale)
      }
    }

    // 2. 从localStorage获取用户语言偏好
    const storedLanguage = localStorage.getItem('user-language')
    if (storedLanguage) {
      return storedLanguage
    }

    // 3. 默认中文
    return 'chinese'
  } catch (error) {
    console.warn('获取当前语言失败，使用默认中文:', error)
    return 'chinese'
  }
}

/**
 * 获取认证Token
 */
function getAuthToken(): string | null {
  if (typeof window === 'undefined') {
    return null
  }

  try {
    // 动态导入token管理器，避免循环依赖
    const tokenManager = require('@/lib/token').tokenManager
    return tokenManager.getToken()
  } catch (error) {
    console.warn('获取token失败:', error)
    return null
  }
}

/**
 * 检查是否需要拦截此请求
 */
function shouldInterceptRequest(url: string): boolean {
  // 只拦截API请求
  if (url.startsWith('/api/') || url.includes('/api/')) {
    return true
  }
  
  // 拦截绝对URL中的API请求
  try {
    const urlObj = new URL(url, window.location.origin)
    return urlObj.pathname.startsWith('/api/')
  } catch {
    return false
  }
}

/**
 * 拦截的fetch函数
 */
async function interceptedFetch(
  input: RequestInfo | URL,
  init?: RequestInit
): Promise<Response> {
  const url = typeof input === 'string' ? input : input instanceof URL ? input.toString() : input.url
  
  // 如果不需要拦截，直接调用原始fetch
  if (!shouldInterceptRequest(url)) {
    return originalFetch(input, init)
  }

  // 准备拦截后的请求配置
  const interceptedInit: RequestInit = {
    ...init,
    headers: {
      'Content-Type': 'application/json',
      ...init?.headers,
    }
  }

  // 添加语言头
  const language = getCurrentLanguage()
  if (language) {
    (interceptedInit.headers as Record<string, string>)['language'] = language
  }

  // 检查是否跳过认证
  const headers = interceptedInit.headers as Record<string, string>
  const skipAuth = headers['x-skip-auth'] === 'true'

  // 移除内部标记头
  if (skipAuth) {
    delete headers['x-skip-auth']
  }

  // 添加认证头（除非明确跳过）
  if (!skipAuth) {
    const token = getAuthToken()
    if (token) {
      headers['authorization'] = token
    }
  }

  // 调试日志
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔄 [Fetch拦截器] ${interceptedInit.method || 'GET'} ${url}`, {
      language,
      hasToken: !!token,
      headers: interceptedInit.headers
    })
  }

  // 调用原始fetch
  return originalFetch(input, interceptedInit)
}

/**
 * 安装全局fetch拦截器
 */
export function installFetchInterceptor(): void {
  if (isInterceptorInstalled) {
    console.warn('Fetch拦截器已经安装，跳过重复安装')
    return
  }

  if (typeof globalThis !== 'undefined') {
    // 重写全局fetch函数
    globalThis.fetch = interceptedFetch
    isInterceptorInstalled = true
    
    console.log('✅ 全局Fetch拦截器已安装')
  } else {
    console.warn('⚠️ globalThis不可用，无法安装Fetch拦截器')
  }
}

/**
 * 卸载全局fetch拦截器
 */
export function uninstallFetchInterceptor(): void {
  if (!isInterceptorInstalled) {
    return
  }

  if (typeof globalThis !== 'undefined') {
    // 恢复原始fetch函数
    globalThis.fetch = originalFetch
    isInterceptorInstalled = false
    
    console.log('✅ 全局Fetch拦截器已卸载')
  }
}

/**
 * 检查拦截器是否已安装
 */
export function isInterceptorActive(): boolean {
  return isInterceptorInstalled
}

/**
 * 手动调用拦截器（用于测试）
 */
export function manualIntercept(
  url: string,
  options: RequestInit = {}
): { url: string; options: RequestInit } {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...options.headers as Record<string, string>,
  }

  // 添加语言头
  headers['language'] = getCurrentLanguage()

  // 添加认证头
  const token = getAuthToken()
  if (token) {
    headers['authorization'] = token
  }

  return {
    url,
    options: {
      ...options,
      headers,
    }
  }
}

/**
 * 获取当前拦截器状态（用于调试）
 */
export function getInterceptorStatus() {
  return {
    installed: isInterceptorInstalled,
    currentLanguage: getCurrentLanguage(),
    hasToken: !!getAuthToken(),
    originalFetchExists: typeof originalFetch === 'function'
  }
}

// 在模块加载时自动安装拦截器（仅在浏览器环境）
if (typeof window !== 'undefined') {
  // 延迟安装，确保其他模块已加载
  setTimeout(() => {
    installFetchInterceptor()
  }, 0)
}
