'use client'

/**
 * 错误边界组件
 * 处理[locale]路由段中的错误
 */

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'

interface ErrorProps {
  error: Error & { digest?: string }
  reset: () => void
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // 记录错误到错误报告服务
    console.error('Route error:', error)
  }, [error])

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center space-y-6 max-w-md mx-auto p-6">
        <div className="text-6xl">😵</div>
        <div className="space-y-2">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            出现了一些问题
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            页面加载时发生错误，请稍后重试。
          </p>
        </div>
        <div className="space-y-3">
          <Button 
            onClick={reset}
            className="w-full"
          >
            重试
          </Button>
          <Button 
            variant="outline"
            onClick={() => window.location.href = '/'}
            className="w-full"
          >
            返回首页
          </Button>
        </div>
        {process.env.NODE_ENV === 'development' && (
          <details className="text-left text-sm text-gray-500 mt-4">
            <summary className="cursor-pointer">错误详情</summary>
            <pre className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs overflow-auto">
              {error.message}
            </pre>
          </details>
        )}
      </div>
    </div>
  )
}
