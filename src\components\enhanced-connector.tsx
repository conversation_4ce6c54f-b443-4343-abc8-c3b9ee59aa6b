"use client"

import { motion } from "framer-motion"

interface Position {
  row: number
  col: number
  isEvenRow: boolean
  isLastInRow: boolean
  isFirstInRow: boolean
}

interface EnhancedConnectorProps {
  isActive: boolean
  currentPosition: Position
  nextPosition: Position
  columnsPerRow: number
}

export function EnhancedConnector({ isActive, currentPosition, nextPosition, columnsPerRow }: EnhancedConnectorProps) {
  // Mobile vertical connector
  if (columnsPerRow === 1) {
    return (
      <div className="flex justify-center">
        <motion.div className="relative">
          <svg width="4" height="48" viewBox="0 0 4 48" className="overflow-visible">
            <defs>
              <linearGradient id="verticalGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor={isActive ? "#10b981" : "#cbd5e1"} />
                <stop offset="100%" stopColor={isActive ? "#059669" : "#94a3b8"} />
              </linearGradient>
              <filter id="glow">
                <feGaussianBlur stdDeviation="2" result="coloredBlur" />
                <feMerge>
                  <feMergeNode in="coloredBlur" />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
              </filter>
            </defs>

            <motion.path
              d="M 2 0 L 2 48"
              stroke="url(#verticalGradient)"
              strokeWidth="3"
              fill="none"
              filter={isActive ? "url(#glow)" : "none"}
              initial={{ pathLength: 0, opacity: 0.3 }}
              animate={{
                pathLength: isActive ? 1 : 0.5,
                opacity: isActive ? 1 : 0.4,
              }}
              transition={{ duration: 1.2, ease: "easeInOut" }}
            />

            {isActive && (
              <motion.circle
                cx="2"
                cy="48"
                r="4"
                fill="#10b981"
                filter="url(#glow)"
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 1.2, duration: 0.4, ease: "easeOut" }}
              />
            )}
          </svg>
        </motion.div>
      </div>
    )
  }

  // Same row horizontal connector
  if (currentPosition.row === nextPosition.row) {
    const isRightToLeft = currentPosition.isEvenRow && currentPosition.col > nextPosition.col

    return (
      <div className={`absolute top-1/2 transform -translate-y-1/2 ${isRightToLeft ? "-left-12" : "-right-12"}`}>
        <svg width="96" height="8" viewBox="0 0 96 8" className="overflow-visible">
          <defs>
            <linearGradient id="horizontalGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor={isActive ? "#10b981" : "#cbd5e1"} />
              <stop offset="100%" stopColor={isActive ? "#059669" : "#94a3b8"} />
            </linearGradient>
            <filter id="horizontalGlow">
              <feGaussianBlur stdDeviation="2" result="coloredBlur" />
              <feMerge>
                <feMergeNode in="coloredBlur" />
                <feMergeNode in="SourceGraphic" />
              </feMerge>
            </filter>
          </defs>

          <motion.path
            d="M 0 4 L 96 4"
            stroke="url(#horizontalGradient)"
            strokeWidth="3"
            fill="none"
            filter={isActive ? "url(#horizontalGlow)" : "none"}
            initial={{ pathLength: 0, opacity: 0.3 }}
            animate={{
              pathLength: isActive ? 1 : 0.5,
              opacity: isActive ? 1 : 0.4,
            }}
            transition={{ duration: 1, ease: "easeInOut" }}
          />

          {isActive && (
            <motion.circle
              cx="96"
              cy="4"
              r="4"
              fill="#10b981"
              filter="url(#horizontalGlow)"
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 1, duration: 0.4, ease: "easeOut" }}
            />
          )}
        </svg>
      </div>
    )
  }

  // Row transition connector (S-shaped curve)
  const isEndOfRow =
    currentPosition.isLastInRow || currentPosition.col === columnsPerRow - 1 || nextPosition.row > currentPosition.row

  if (isEndOfRow) {
    const curveDirection = currentPosition.isEvenRow ? "right" : "left"

    return (
      <div
        className={`absolute -bottom-16 ${
          curveDirection === "right" ? "right-0" : "left-0"
        } transform ${curveDirection === "left" ? "scale-x-[-1]" : ""}`}
      >
        <svg width="160" height="64" viewBox="0 0 160 64" className="overflow-visible">
          <defs>
            <linearGradient id="curveGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor={isActive ? "#10b981" : "#cbd5e1"} />
              <stop offset="50%" stopColor={isActive ? "#059669" : "#94a3b8"} />
              <stop offset="100%" stopColor={isActive ? "#047857" : "#64748b"} />
            </linearGradient>
            <filter id="curveGlow">
              <feGaussianBlur stdDeviation="3" result="coloredBlur" />
              <feMerge>
                <feMergeNode in="coloredBlur" />
                <feMergeNode in="SourceGraphic" />
              </feMerge>
            </filter>
          </defs>

          <motion.path
            d="M 40 0 Q 120 20 120 64"
            stroke="url(#curveGradient)"
            strokeWidth="3"
            fill="none"
            filter={isActive ? "url(#curveGlow)" : "none"}
            strokeLinecap="round"
            initial={{ pathLength: 0, opacity: 0.3 }}
            animate={{
              pathLength: isActive ? 1 : 0.5,
              opacity: isActive ? 1 : 0.4,
            }}
            transition={{ duration: 1.5, ease: "easeInOut" }}
          />

          {isActive && (
            <>
              <motion.circle
                cx="120"
                cy="64"
                r="5"
                fill="#10b981"
                filter="url(#curveGlow)"
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 1.5, duration: 0.4, ease: "easeOut" }}
              />

              {/* Animated pulse effect */}
              <motion.circle
                cx="120"
                cy="64"
                r="5"
                fill="none"
                stroke="#10b981"
                strokeWidth="2"
                initial={{ scale: 1, opacity: 1 }}
                animate={{ scale: 2, opacity: 0 }}
                transition={{
                  delay: 1.9,
                  duration: 1,
                  repeat: Number.POSITIVE_INFINITY,
                  ease: "easeOut",
                }}
              />
            </>
          )}
        </svg>
      </div>
    )
  }

  return null
}
