'use client'

import { VerticalPipelineDashboard } from '@/components/vertical-pipeline-dashboard'
import { useSearchParams } from 'next/navigation'

export default function TestPipelineStatusPage() {
  // 模拟用户数据用于测试
  const mockUser = {
    user_name: '张三',
    email: 'z<PERSON><PERSON>@example.com'
  }

  const searchParams = useSearchParams()
  const title = searchParams.get('title') || '招投标项目'

  return (
    <div className="h-full w-full overflow-hidden bg-gray-50">
      <div className="p-4 bg-blue-50 border-b border-blue-200">
        <h2 className="text-lg font-semibold text-blue-800 mb-2">Pipeline Status 测试页面</h2>
        <p className="text-sm text-blue-600">
          测试URL参数: title={title}
        </p>
        <p className="text-sm text-blue-600">
          模拟用户: {mockUser.user_name} ({mockUser.email})
        </p>
        <p className="text-sm text-blue-600 mt-2">
          预期显示: "正在为{mockUser.user_name}生成[{title}]的详细背景信息，当前正在执行XXX，请耐心等待或检查操作"
        </p>
      </div>
      <VerticalPipelineDashboard user={mockUser} />
    </div>
  )
}
