# Next.js iframe 嵌入系统环境变量配置示例

# 应用配置
NODE_ENV=development
PORT=3000
HOSTNAME=localhost

# API 配置
API_BASE_URL=http://localhost:8080
NEXT_PUBLIC_APP_URL=http://localhost:3000

# 嵌入系统配置
ALLOWED_EMBED_DOMAINS=https://www.goglobalsp.com,https://dev.goglobalsp.com
EMBED_TOKEN_SECRET=your-super-secret-key-here

# 认证配置
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000

# 开发环境配置
DEBUG=false
LOG_LEVEL=info

# 测试配置
TEST_BASE_URL=http://localhost:3000
TEST_TOKEN=your-test-jwt-token
