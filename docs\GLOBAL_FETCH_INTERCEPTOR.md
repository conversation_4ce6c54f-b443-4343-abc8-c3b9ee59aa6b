# 全局Fetch拦截器使用指南

## 概述

全局Fetch拦截器通过重写`globalThis.fetch`函数，在最底层统一处理所有HTTP请求，自动添加认证头和语言头。这确保了项目中所有的HTTP请求都会包含必要的请求头，无需在每个API调用处手动添加。

## 🔧 核心特性

### 1. 自动请求头注入
- **language头**: 智能检测当前语言环境
- **authorization头**: 自动获取JWT token
- **Content-Type**: 默认设置为`application/json`

### 2. 智能语言检测
```typescript
// 优先级顺序：
// 1. URL路径语言 (/en/dashboard → 'english')
// 2. localStorage用户语言偏好
// 3. 默认中文 ('chinese')
```

### 3. 选择性拦截
- 只拦截API请求 (`/api/*`)
- 跳过静态资源请求
- 支持skipAuth机制

## 🚀 安装和使用

### 自动安装
拦截器会在应用启动时自动安装：

```typescript
// src/components/providers/auth-provider.tsx
import { FetchInterceptorProvider } from './fetch-interceptor-provider'

export function AuthProvider({ children }) {
  return (
    <FetchInterceptorProvider>
      {children}
    </FetchInterceptorProvider>
  )
}
```

### 手动控制
```typescript
import { 
  installFetchInterceptor, 
  uninstallFetchInterceptor, 
  isInterceptorActive 
} from '@/lib/fetch-interceptor'

// 安装拦截器
installFetchInterceptor()

// 检查状态
console.log('拦截器已安装:', isInterceptorActive())

// 卸载拦截器
uninstallFetchInterceptor()
```

## 📚 使用示例

### 1. 普通API请求
```typescript
// 自动添加language和authorization头
const response = await fetch('/api/user/profile')
const data = await response.json()
```

### 2. 跳过认证的请求
```typescript
// 登录接口不需要authorization头
const response = await fetch('/api/user/login', {
  method: 'POST',
  headers: {
    'x-skip-auth': 'true'  // 特殊标记，拦截器会移除authorization头
  },
  body: JSON.stringify({ username, password })
})
```

### 3. 自定义请求头
```typescript
// 保留现有请求头，同时添加拦截器头
const response = await fetch('/api/data', {
  headers: {
    'Custom-Header': 'custom-value',
    'Another-Header': 'another-value'
  }
})
// 最终请求头包含：Custom-Header, Another-Header, language, authorization
```

### 4. 非API请求（不会被拦截）
```typescript
// 静态资源请求不会被拦截
const response = await fetch('/static/image.png')
const response2 = await fetch('https://external-api.com/data')
```

## 🔄 与现有代码的兼容性

### 自动简化现有API函数
拦截器安装后，现有的API函数会自动简化：

```typescript
// 旧代码（手动添加请求头）
async function apiRequest(url, options = {}) {
  const token = tokenManager.getToken()
  const language = getCurrentLanguage()
  
  return fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'authorization': token,
      'language': language,
      ...options.headers
    }
  })
}

// 新代码（拦截器自动处理）
async function apiRequest(url, options = {}) {
  return fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    }
  })
}
```

### skipAuth支持
```typescript
// 旧代码
async function login(credentials) {
  return fetch('/api/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'language': getCurrentLanguage()
      // 不添加authorization头
    },
    body: JSON.stringify(credentials)
  })
}

// 新代码
async function login(credentials) {
  return fetch('/api/login', {
    method: 'POST',
    headers: {
      'x-skip-auth': 'true'  // 拦截器会跳过authorization头
    },
    body: JSON.stringify(credentials)
  })
}
```

## 🌍 语言头处理

### 自动语言检测
```typescript
// URL: /en/dashboard → language: 'english'
// URL: /zh/dashboard → language: 'chinese'  
// URL: /ja/dashboard → language: 'japanese'
// URL: /dashboard → 使用localStorage或默认中文
```

### 语言映射
```typescript
const languageMapping = {
  'en': 'english',
  'zh': 'chinese',
  'ja': 'japanese'
}
```

## 🔐 认证头处理

### 自动Token管理
```typescript
// 拦截器会自动：
// 1. 从tokenManager获取当前token
// 2. 添加到authorization头
// 3. 处理token不存在的情况
```

### skipAuth机制
```typescript
// 通过特殊请求头跳过认证
fetch('/api/public/data', {
  headers: {
    'x-skip-auth': 'true'
  }
})
```

## 🐛 调试和监控

### 开发环境日志
```typescript
// 拦截器会在开发环境输出详细日志
🔄 [Fetch拦截器] GET /api/user/profile {
  language: 'english',
  hasToken: true,
  headers: { ... }
}
```

### 状态检查
```typescript
import { getInterceptorStatus } from '@/lib/fetch-interceptor'

const status = getInterceptorStatus()
console.log('拦截器状态:', status)
// 输出: {
//   installed: true,
//   currentLanguage: 'english',
//   hasToken: true,
//   originalFetchExists: true
// }
```

### 手动测试
```typescript
import { manualIntercept } from '@/lib/fetch-interceptor'

const result = manualIntercept('/api/test', { method: 'POST' })
console.log('拦截结果:', result)
// 查看拦截器会如何处理这个请求
```

## ⚠️ 注意事项

### 1. 拦截范围
- 只拦截包含`/api/`的URL
- 不拦截静态资源和外部API
- 支持相对和绝对URL

### 2. 请求头优先级
```typescript
// 优先级：用户自定义 > 拦截器默认 > 系统默认
const finalHeaders = {
  'Content-Type': 'application/json',  // 拦截器默认
  'language': 'english',               // 拦截器自动
  'authorization': 'Bearer ...',       // 拦截器自动
  'Custom-Header': 'user-value'        // 用户自定义（优先级最高）
}
```

### 3. 性能考虑
- 拦截器开销极小，只在API请求时执行
- 语言检测结果会在单次请求中缓存
- Token获取使用懒加载模式

## 🔄 迁移指南

### 从手动请求头迁移
1. 移除手动添加的`language`和`authorization`头
2. 保留`Content-Type`和自定义头
3. 使用`x-skip-auth`标记替代skipAuth参数

### 测试验证
```typescript
// 验证拦截器是否正常工作
const testRequest = async () => {
  const response = await fetch('/api/test')
  // 检查Network面板中的请求头
}
```

---

*全局Fetch拦截器确保了项目中所有HTTP请求的一致性，简化了API调用代码，提高了开发效率。*
