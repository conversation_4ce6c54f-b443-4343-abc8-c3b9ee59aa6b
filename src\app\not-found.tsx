'use client'

import { But<PERSON> } from '@/components/ui/button'
import Link from 'next/link'
import { Home, ArrowLeft } from 'lucide-react'
import { FuzzyText } from '@/components/ui/fuzzy-text'
import { useState } from 'react'
export default function NotFound() {
  const [enableHover] = useState(true)
  const [hoverIntensity] = useState(0.4)
  return (
    <div className="h-full w-full bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <div className="w-full h-full text-center flex flex-col items-center justify-center">
        {/* 404 大标题 */}
        <div className="mb-8">
          <FuzzyText baseIntensity={0.2} hoverIntensity={hoverIntensity} enableHover={enableHover} className='mb-4'>
            404
          </FuzzyText>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">页面未找到</h2>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            抱歉，您访问的页面不存在或已被移动
          </p>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild size="lg">
            <Link href="/">
              <Home className="mr-2 h-4 w-4" />
              返回首页
            </Link>
          </Button>

          <Button variant="outline" size="lg" onClick={() => window.history.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回上页
          </Button>
        </div>

        {/* 页脚信息 */}
        <div className="mt-8 text-sm text-gray-500 dark:text-gray-400">
          <p>错误代码: 404 | 页面未找到</p>
          <p className="mt-1">如果问题持续存在，请记录此错误代码并联系支持团队</p>
        </div>
      </div>
    </div>
  )
}
