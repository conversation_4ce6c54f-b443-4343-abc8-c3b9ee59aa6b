# Next.js iframe 嵌入系统实现指南

## 实现步骤概览

本指南将按照以下步骤实现完整的 iframe 嵌入系统：

1. [创建嵌入系统核心库](#1-创建嵌入系统核心库)
2. [实现嵌入页面中间件](#2-实现嵌入页面中间件)
3. [创建嵌入页面布局](#3-创建嵌入页面布局)
4. [实现具体嵌入页面](#4-实现具体嵌入页面)
5. [添加通信组件](#5-添加通信组件)
6. [配置路由和中间件](#6-配置路由和中间件)
7. [创建API端点](#7-创建api端点)
8. [添加样式和优化](#8-添加样式和优化)

## 1. 创建嵌入系统核心库

### 1.1 类型定义

创建 `src/lib/embed/types.ts`：

```typescript
/**
 * 嵌入系统类型定义
 */

export interface EmbedConfig {
  allowedDomains: string[]
  tokenParamName: string
  enableCommunication: boolean
  securityHeaders: boolean
}

export interface EmbedAuthResult {
  success: boolean
  user?: any
  error?: string
}

export interface IframeMessage {
  type: string
  data?: any
  timestamp: number
}

export interface EmbedContext {
  isEmbedded: boolean
  parentOrigin?: string
  token?: string
  user?: any
}

export type MessageType = 
  | 'RESIZE_REQUEST'
  | 'RESIZE_RESPONSE'
  | 'NAVIGATION_REQUEST'
  | 'NAVIGATION_RESPONSE'
  | 'AUTH_STATUS'
  | 'ERROR'

export interface ResizeMessage extends IframeMessage {
  type: 'RESIZE_RESPONSE'
  data: {
    height: number
    width?: number
  }
}

export interface NavigationMessage extends IframeMessage {
  type: 'NAVIGATION_REQUEST' | 'NAVIGATION_RESPONSE'
  data: {
    path: string
    query?: Record<string, string>
  }
}
```

### 1.2 域名验证器

创建 `src/lib/embed/domain-validator.ts`：

```typescript
/**
 * 域名验证器
 * 验证请求来源是否在允许的域名列表中
 */

const DEFAULT_ALLOWED_DOMAINS = [
  'https://www.goglobalsp.com',
  'https://dev.goglobalsp.com'
]

export class DomainValidator {
  private allowedDomains: string[]

  constructor(domains?: string[]) {
    this.allowedDomains = domains || this.getDomainsFromEnv()
  }

  /**
   * 从环境变量获取允许的域名列表
   */
  private getDomainsFromEnv(): string[] {
    const envDomains = process.env.ALLOWED_EMBED_DOMAINS
    if (envDomains) {
      return envDomains.split(',').map(domain => domain.trim())
    }
    return DEFAULT_ALLOWED_DOMAINS
  }

  /**
   * 验证域名是否被允许
   */
  isAllowed(origin: string | null): boolean {
    if (!origin) return false

    try {
      const url = new URL(origin)
      const fullOrigin = `${url.protocol}//${url.hostname}`
      return this.allowedDomains.includes(fullOrigin)
    } catch {
      return false
    }
  }

  /**
   * 验证 Referer 头
   */
  isRefererAllowed(referer: string | null): boolean {
    if (!referer) return false

    try {
      const url = new URL(referer)
      const origin = `${url.protocol}//${url.hostname}`
      return this.allowedDomains.includes(origin)
    } catch {
      return false
    }
  }

  /**
   * 获取允许的域名列表
   */
  getAllowedDomains(): string[] {
    return [...this.allowedDomains]
  }

  /**
   * 添加允许的域名
   */
  addAllowedDomain(domain: string): void {
    if (!this.allowedDomains.includes(domain)) {
      this.allowedDomains.push(domain)
    }
  }

  /**
   * 移除允许的域名
   */
  removeAllowedDomain(domain: string): void {
    this.allowedDomains = this.allowedDomains.filter(d => d !== domain)
  }
}

// 单例实例
export const domainValidator = new DomainValidator()
```

### 1.3 安全策略

创建 `src/lib/embed/security.ts`：

```typescript
/**
 * 嵌入页面安全策略
 */

import { NextResponse } from 'next/server'
import { domainValidator } from './domain-validator'

export interface SecurityHeaders {
  'X-Frame-Options'?: string
  'Content-Security-Policy'?: string
  'Access-Control-Allow-Origin'?: string
  'Access-Control-Allow-Credentials'?: string
  'X-Content-Type-Options'?: string
  'Referrer-Policy'?: string
}

export class EmbedSecurity {
  /**
   * 设置 iframe 安全头部
   */
  static setIframeHeaders(response: NextResponse, allowedOrigin: string): void {
    const headers: SecurityHeaders = {
      // 允许特定域名嵌入
      'X-Frame-Options': `ALLOW-FROM ${allowedOrigin}`,
      
      // CSP 策略
      'Content-Security-Policy': [
        `frame-ancestors ${allowedOrigin}`,
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
        "style-src 'self' 'unsafe-inline'",
        "img-src 'self' data: https:",
        "font-src 'self' data:",
        "connect-src 'self' https:"
      ].join('; '),
      
      // CORS 设置
      'Access-Control-Allow-Origin': allowedOrigin,
      'Access-Control-Allow-Credentials': 'true',
      
      // 其他安全头部
      'X-Content-Type-Options': 'nosniff',
      'Referrer-Policy': 'strict-origin-when-cross-origin'
    }

    Object.entries(headers).forEach(([key, value]) => {
      if (value) {
        response.headers.set(key, value)
      }
    })
  }

  /**
   * 验证请求安全性
   */
  static validateRequest(request: Request): {
    isValid: boolean
    origin?: string
    error?: string
  } {
    const referer = request.headers.get('referer')
    const origin = request.headers.get('origin')
    
    // 检查 Referer
    if (!domainValidator.isRefererAllowed(referer)) {
      return {
        isValid: false,
        error: 'Invalid referer domain'
      }
    }

    // 获取允许的源
    const allowedOrigin = referer ? new URL(referer).origin : origin

    return {
      isValid: true,
      origin: allowedOrigin || undefined
    }
  }

  /**
   * 创建安全的响应
   */
  static createSecureResponse(
    content: string | Response,
    allowedOrigin: string,
    options?: ResponseInit
  ): Response {
    const response = typeof content === 'string' 
      ? new Response(content, options)
      : content

    const nextResponse = NextResponse.next({ request: { headers: new Headers() } })
    this.setIframeHeaders(nextResponse, allowedOrigin)

    // 复制安全头部到响应
    nextResponse.headers.forEach((value, key) => {
      response.headers.set(key, value)
    })

    return response
  }

  /**
   * 生成 CSP nonce
   */
  static generateNonce(): string {
    const array = new Uint8Array(16)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }

  /**
   * 验证 token 格式
   */
  static isValidTokenFormat(token: string): boolean {
    if (!token || typeof token !== 'string') {
      return false
    }

    // JWT 格式验证
    const parts = token.split('.')
    if (parts.length !== 3) {
      return false
    }

    try {
      // 尝试解析 header 和 payload
      JSON.parse(atob(parts[0]))
      JSON.parse(atob(parts[1]))
      return true
    } catch {
      return false
    }
  }

  /**
   * 清理和验证 URL 参数
   */
  static sanitizeUrlParams(params: URLSearchParams): URLSearchParams {
    const sanitized = new URLSearchParams()
    
    params.forEach((value, key) => {
      // 移除潜在的危险字符
      const cleanKey = key.replace(/[<>\"'&]/g, '')
      const cleanValue = value.replace(/[<>\"'&]/g, '')
      
      if (cleanKey && cleanValue) {
        sanitized.set(cleanKey, cleanValue)
      }
    })
    
    return sanitized
  }
}
```

### 1.4 嵌入认证逻辑

创建 `src/lib/embed/auth.ts`：

```typescript
/**
 * 嵌入页面认证逻辑
 */

import { NextRequest } from 'next/server'
import { validateTokenOnServer } from '../auth/token-validator'
import { EmbedSecurity } from './security'
import type { EmbedAuthResult, EmbedContext } from './types'

export class EmbedAuth {
  /**
   * 从请求中提取 token
   */
  static extractToken(request: NextRequest): string | null {
    // 优先从 URL 参数获取 token（用于嵌入页面）
    const urlToken = request.nextUrl.searchParams.get('token')
    if (urlToken && EmbedSecurity.isValidTokenFormat(urlToken)) {
      return urlToken
    }

    // 备选：从 cookie 获取 token
    const cookieToken = request.cookies.get('auth-token')?.value
    if (cookieToken && EmbedSecurity.isValidTokenFormat(cookieToken)) {
      return cookieToken
    }

    // 备选：从 Authorization 头获取 token
    const authHeader = request.headers.get('authorization')
    if (authHeader?.startsWith('Bearer ')) {
      const headerToken = authHeader.slice(7)
      if (EmbedSecurity.isValidTokenFormat(headerToken)) {
        return headerToken
      }
    }

    return null
  }

  /**
   * 验证嵌入页面认证
   */
  static async validateEmbedAuth(request: NextRequest): Promise<EmbedAuthResult> {
    try {
      // 提取 token
      const token = this.extractToken(request)
      if (!token) {
        return {
          success: false,
          error: 'No valid token found'
        }
      }

      // 验证 token
      const authData = await validateTokenOnServer(token)
      if (!authData) {
        return {
          success: false,
          error: 'Token validation failed'
        }
      }

      return {
        success: true,
        user: authData.user
      }
    } catch (error) {
      console.error('Embed auth validation error:', error)
      return {
        success: false,
        error: 'Authentication error'
      }
    }
  }

  /**
   * 构建嵌入上下文
   */
  static async buildEmbedContext(request: NextRequest): Promise<EmbedContext> {
    const referer = request.headers.get('referer')
    const isEmbedded = !!referer && request.nextUrl.pathname.startsWith('/embed')
    
    let parentOrigin: string | undefined
    let token: string | undefined
    let user: any

    if (isEmbedded) {
      // 获取父页面源
      if (referer) {
        try {
          parentOrigin = new URL(referer).origin
        } catch {
          // 忽略无效的 referer
        }
      }

      // 提取 token 和用户信息
      token = this.extractToken(request)
      if (token) {
        const authResult = await this.validateEmbedAuth(request)
        if (authResult.success) {
          user = authResult.user
        }
      }
    }

    return {
      isEmbedded,
      parentOrigin,
      token,
      user
    }
  }

  /**
   * 检查是否为嵌入页面请求
   */
  static isEmbedRequest(request: NextRequest): boolean {
    return request.nextUrl.pathname.startsWith('/embed')
  }

  /**
   * 生成嵌入页面 URL
   */
  static generateEmbedUrl(
    basePath: string,
    token: string,
    additionalParams?: Record<string, string>
  ): string {
    const url = new URL(basePath, process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000')
    
    // 添加 token
    url.searchParams.set('token', token)
    
    // 添加其他参数
    if (additionalParams) {
      Object.entries(additionalParams).forEach(([key, value]) => {
        url.searchParams.set(key, value)
      })
    }
    
    return url.toString()
  }
}
```

## 2. 实现嵌入页面中间件

创建 `src/lib/middleware/embed-guard.ts`：

```typescript
/**
 * 嵌入页面守卫中间件
 * 专门处理 /embed 路径的认证和安全验证
 */

import { NextRequest, NextResponse } from 'next/server'
import { EmbedAuth } from '../embed/auth'
import { EmbedSecurity } from '../embed/security'
import { domainValidator } from '../embed/domain-validator'
import type { MiddlewareFunction } from './types'

export const embedGuard: MiddlewareFunction = async (
  request: NextRequest,
  next: () => Promise<NextResponse>
): Promise<NextResponse> => {
  const { pathname } = request.nextUrl

  // 只处理 /embed 路径
  if (!pathname.startsWith('/embed')) {
    return await next()
  }

  console.log(`[EmbedGuard] Processing embed request: ${pathname}`)

  try {
    // 1. 验证请求安全性
    const securityCheck = EmbedSecurity.validateRequest(request)
    if (!securityCheck.isValid) {
      console.warn(`[EmbedGuard] Security validation failed: ${securityCheck.error}`)
      return new NextResponse('Forbidden', { status: 403 })
    }

    // 2. 验证认证
    const authResult = await EmbedAuth.validateEmbedAuth(request)
    if (!authResult.success) {
      console.warn(`[EmbedGuard] Authentication failed: ${authResult.error}`)
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // 3. 继续处理请求
    const response = await next()

    // 4. 设置安全头部
    if (securityCheck.origin) {
      EmbedSecurity.setIframeHeaders(response, securityCheck.origin)
    }

    console.log(`[EmbedGuard] Successfully processed embed request: ${pathname}`)
    return response

  } catch (error) {
    console.error('[EmbedGuard] Error processing embed request:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}

/**
 * 嵌入页面专用的上下文构建器
 */
export async function buildEmbedGuardContext(request: NextRequest) {
  const pathname = request.nextUrl.pathname
  const searchParams = request.nextUrl.searchParams
  
  // 构建嵌入上下文
  const embedContext = await EmbedAuth.buildEmbedContext(request)
  
  return {
    request,
    pathname,
    searchParams,
    embedContext,
    isEmbedded: embedContext.isEmbedded,
    user: embedContext.user
  }
}
```

## 3. 创建嵌入页面布局

创建 `src/app/(embed)/layout.tsx`：

```typescript
/**
 * 嵌入页面专用布局
 * 提供精简的布局，适合在 iframe 中显示
 */

import { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { IframeCommunicator } from '@/components/embed/IframeCommunicator'
import { EmbedProvider } from '@/components/embed/EmbedProvider'
import '@/styles/embed.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Embedded Page',
  description: 'Page embedded in iframe',
  robots: 'noindex, nofollow', // 防止搜索引擎索引嵌入页面
}

export default function EmbedLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" className="embed-html">
      <body className={`${inter.className} embed-body`}>
        <EmbedProvider>
          <div className="embed-container">
            <IframeCommunicator />
            <main className="embed-main">
              {children}
            </main>
          </div>
        </EmbedProvider>
      </body>
    </html>
  )
}
```

## 4. 实现具体嵌入页面

### 4.1 仪表板嵌入页面

创建 `src/app/(embed)/dashboard/page.tsx`：

```typescript
/**
 * 嵌入式仪表板页面
 */

import { Suspense } from 'react'
import { EmbedPageWrapper } from '@/components/embed/EmbedPageWrapper'
import { DashboardContent } from '@/components/dashboard/DashboardContent'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

export default function EmbedDashboard() {
  return (
    <EmbedPageWrapper title="仪表板">
      <div className="embed-dashboard">
        <Suspense fallback={<LoadingSpinner />}>
          <DashboardContent embedded={true} />
        </Suspense>
      </div>
    </EmbedPageWrapper>
  )
}
```

### 4.2 报告嵌入页面

创建 `src/app/(embed)/reports/page.tsx`：

```typescript
/**
 * 嵌入式报告页面
 */

import { Suspense } from 'react'
import { EmbedPageWrapper } from '@/components/embed/EmbedPageWrapper'
import { ReportsContent } from '@/components/reports/ReportsContent'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

export default function EmbedReports() {
  return (
    <EmbedPageWrapper title="报告">
      <div className="embed-reports">
        <Suspense fallback={<LoadingSpinner />}>
          <ReportsContent embedded={true} />
        </Suspense>
      </div>
    </EmbedPageWrapper>
  )
}
```

### 4.3 工作区嵌入页面

创建 `src/app/(embed)/workspace/page.tsx`：

```typescript
/**
 * 嵌入式工作区页面
 */

import { Suspense } from 'react'
import { EmbedPageWrapper } from '@/components/embed/EmbedPageWrapper'
import { WorkspaceContent } from '@/components/workspace/WorkspaceContent'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

export default function EmbedWorkspace() {
  return (
    <EmbedPageWrapper title="工作区">
      <div className="embed-workspace">
        <Suspense fallback={<LoadingSpinner />}>
          <WorkspaceContent embedded={true} />
        </Suspense>
      </div>
    </EmbedPageWrapper>
  )
}
```

## 5. 添加通信组件

### 5.1 iframe 通信器

创建 `src/components/embed/IframeCommunicator.tsx`：

```typescript
/**
 * iframe 通信组件
 * 处理与父页面的双向通信
 */

'use client'

import { useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { domainValidator } from '@/lib/embed/domain-validator'
import type { IframeMessage, MessageType } from '@/lib/embed/types'

export function IframeCommunicator() {
  const router = useRouter()
  const heightObserverRef = useRef<ResizeObserver | null>(null)
  const lastHeightRef = useRef<number>(0)

  useEffect(() => {
    // 处理来自父页面的消息
    const handleMessage = (event: MessageEvent) => {
      // 验证来源域名
      if (!domainValidator.isAllowed(event.origin)) {
        console.warn('Received message from unauthorized origin:', event.origin)
        return
      }

      const message: IframeMessage = event.data

      switch (message.type) {
        case 'RESIZE_REQUEST':
          sendHeightToParent(event.origin)
          break

        case 'NAVIGATION_REQUEST':
          if (message.data?.path) {
            router.push(message.data.path)
          }
          break

        case 'PING':
          // 响应心跳检测
          sendMessage(event.origin, {
            type: 'PONG',
            data: { timestamp: Date.now() },
            timestamp: Date.now()
          })
          break

        default:
          console.log('Received unknown message type:', message.type)
      }
    }

    // 发送消息到父页面
    const sendMessage = (origin: string, message: IframeMessage) => {
      if (window.parent && window.parent !== window) {
        window.parent.postMessage(message, origin)
      }
    }

    // 发送当前页面高度到父页面
    const sendHeightToParent = (origin: string) => {
      const height = Math.max(
        document.body.scrollHeight,
        document.body.offsetHeight,
        document.documentElement.clientHeight,
        document.documentElement.scrollHeight,
        document.documentElement.offsetHeight
      )

      // 只有高度变化时才发送消息
      if (Math.abs(height - lastHeightRef.current) > 10) {
        lastHeightRef.current = height
        sendMessage(origin, {
          type: 'RESIZE_RESPONSE',
          data: { height },
          timestamp: Date.now()
        })
      }
    }

    // 监听页面高度变化
    const observeHeightChanges = () => {
      if (heightObserverRef.current) {
        heightObserverRef.current.disconnect()
      }

      heightObserverRef.current = new ResizeObserver(() => {
        // 获取父页面的 origin
        const referer = document.referrer
        if (referer) {
          try {
            const origin = new URL(referer).origin
            if (domainValidator.isAllowed(origin)) {
              sendHeightToParent(origin)
            }
          } catch {
            // 忽略无效的 referrer
          }
        }
      })

      // 观察 body 元素的大小变化
      if (document.body) {
        heightObserverRef.current.observe(document.body)
      }
    }

    // 初始化
    window.addEventListener('message', handleMessage)
    observeHeightChanges()

    // 页面加载完成后发送初始高度
    const sendInitialHeight = () => {
      const referer = document.referrer
      if (referer) {
        try {
          const origin = new URL(referer).origin
          if (domainValidator.isAllowed(origin)) {
            sendHeightToParent(origin)
          }
        } catch {
          // 忽略无效的 referrer
        }
      }
    }

    // 延迟发送初始高度，确保页面完全渲染
    const timer = setTimeout(sendInitialHeight, 100)

    // 清理函数
    return () => {
      window.removeEventListener('message', handleMessage)
      if (heightObserverRef.current) {
        heightObserverRef.current.disconnect()
      }
      clearTimeout(timer)
    }
  }, [router])

  return null // 这是一个无渲染组件
}
```

### 5.2 嵌入上下文提供者

创建 `src/components/embed/EmbedProvider.tsx`：

```typescript
/**
 * 嵌入上下文提供者
 * 为嵌入页面提供上下文信息
 */

'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import type { EmbedContext } from '@/lib/embed/types'

interface EmbedContextType extends EmbedContext {
  sendMessageToParent: (message: any) => void
  isParentConnected: boolean
}

const EmbedContext = createContext<EmbedContextType | null>(null)

interface EmbedProviderProps {
  children: ReactNode
}

export function EmbedProvider({ children }: EmbedProviderProps) {
  const [embedContext, setEmbedContext] = useState<EmbedContext>({
    isEmbedded: false
  })
  const [isParentConnected, setIsParentConnected] = useState(false)

  useEffect(() => {
    // 检测是否在 iframe 中
    const isEmbedded = window.self !== window.top
    const parentOrigin = document.referrer ? new URL(document.referrer).origin : undefined

    // 从 URL 参数获取 token
    const urlParams = new URLSearchParams(window.location.search)
    const token = urlParams.get('token') || undefined

    setEmbedContext({
      isEmbedded,
      parentOrigin,
      token
    })

    // 如果在 iframe 中，尝试与父页面建立连接
    if (isEmbedded && parentOrigin) {
      const pingInterval = setInterval(() => {
        if (window.parent) {
          window.parent.postMessage({
            type: 'PING',
            timestamp: Date.now()
          }, parentOrigin)
        }
      }, 5000) // 每5秒发送一次心跳

      // 监听父页面的响应
      const handlePong = (event: MessageEvent) => {
        if (event.origin === parentOrigin && event.data.type === 'PONG') {
          setIsParentConnected(true)
        }
      }

      window.addEventListener('message', handlePong)

      return () => {
        clearInterval(pingInterval)
        window.removeEventListener('message', handlePong)
      }
    }
  }, [])

  const sendMessageToParent = (message: any) => {
    if (embedContext.isEmbedded && embedContext.parentOrigin && window.parent) {
      window.parent.postMessage({
        ...message,
        timestamp: Date.now()
      }, embedContext.parentOrigin)
    }
  }

  const contextValue: EmbedContextType = {
    ...embedContext,
    sendMessageToParent,
    isParentConnected
  }

  return (
    <EmbedContext.Provider value={contextValue}>
      {children}
    </EmbedContext.Provider>
  )
}

export function useEmbed() {
  const context = useContext(EmbedContext)
  if (!context) {
    throw new Error('useEmbed must be used within an EmbedProvider')
  }
  return context
}
```

### 5.3 嵌入页面包装器

创建 `src/components/embed/EmbedPageWrapper.tsx`：

```typescript
/**
 * 嵌入页面包装器
 * 为嵌入页面提供统一的结构和样式
 */

'use client'

import { ReactNode, useEffect } from 'react'
import { useEmbed } from './EmbedProvider'

interface EmbedPageWrapperProps {
  title: string
  children: ReactNode
  className?: string
}

export function EmbedPageWrapper({ title, children, className = '' }: EmbedPageWrapperProps) {
  const { sendMessageToParent, isEmbedded } = useEmbed()

  useEffect(() => {
    // 设置页面标题
    document.title = `${title} - 嵌入模式`

    // 通知父页面页面已加载
    if (isEmbedded) {
      sendMessageToParent({
        type: 'PAGE_LOADED',
        data: {
          title,
          path: window.location.pathname
        }
      })
    }
  }, [title, isEmbedded, sendMessageToParent])

  return (
    <div className={`embed-page-wrapper ${className}`}>
      <div className="embed-page-header">
        <h1 className="embed-page-title">{title}</h1>
      </div>
      <div className="embed-page-content">
        {children}
      </div>
    </div>
  )
}
```

## 6. 配置路由和中间件

### 6.1 更新主中间件

修改 `middleware.ts`，添加嵌入守卫：

```typescript
/**
 * 更新主中间件，添加嵌入守卫
 */

import { NextRequest, NextResponse } from 'next/server'
import { createMiddlewareChain } from './src/lib/middleware/chain'
import { embedGuard } from './src/lib/middleware/embed-guard'  // 新增
import { authGuard } from './src/lib/middleware/auth-guard'
import { accessGuard } from './src/lib/middleware/access-guard'
import { pollGuard } from './src/lib/middleware/poll-guard'
import { menuGuard } from './src/lib/middleware/menu-guard'
import { feedbackGuard } from './src/lib/middleware/feedback-guard'

/**
 * 创建中间件链，嵌入守卫需要在认证守卫之前执行
 */
const middlewareChain = createMiddlewareChain([
  embedGuard,     // 第-1层：嵌入守卫（新增，优先级最高）
  authGuard,      // 第0层：认证守卫
  accessGuard,    // 第1层：访问控制守卫
  pollGuard,      // 第2层：业务流程守卫
  menuGuard,      // 第3层：菜单守卫
  feedbackGuard,  // 第4层：反馈守卫
])

export async function middleware(request: NextRequest): Promise<NextResponse> {
  const { pathname } = request.nextUrl

  // 跳过静态资源和API路由
  if (shouldSkipMiddleware(pathname)) {
    return NextResponse.next()
  }

  // 开发环境日志
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Middleware] Processing: ${pathname}`)
  }

  try {
    const response = await middlewareChain(request)

    // 为非嵌入页面添加标准安全头
    if (!pathname.startsWith('/embed')) {
      addSecurityHeaders(response)
    }

    return response
  } catch (error) {
    console.error('[Middleware] Error:', error)
    return NextResponse.next()
  }
}

// 其余代码保持不变...
```

### 6.2 更新路由配置

如果项目中有路由配置文件，需要添加嵌入页面的配置。创建或更新 `src/lib/route-config.ts`：

```typescript
/**
 * 路由配置，添加嵌入页面配置
 */

import type { RouteConfig } from './middleware/types'

const embedRoutes: Record<string, RouteConfig> = {
  '/embed/dashboard': {
    path: '/embed/dashboard',
    requiresAuth: true,
    access: {
      features: ['dashboard'],
      permissions: ['read']
    }
  },
  '/embed/reports': {
    path: '/embed/reports',
    requiresAuth: true,
    access: {
      features: ['reports'],
      permissions: ['read']
    }
  },
  '/embed/workspace': {
    path: '/embed/workspace',
    requiresAuth: true,
    access: {
      features: ['workspace'],
      permissions: ['read', 'write']
    }
  },
  '/embed/api-demo': {
    path: '/embed/api-demo',
    requiresAuth: true,
    access: {
      features: ['api'],
      permissions: ['read']
    }
  }
}

// 将嵌入路由添加到主路由配置中
export function getRouteConfig(pathname: string): RouteConfig | null {
  // 首先检查嵌入路由
  if (pathname.startsWith('/embed')) {
    return embedRoutes[pathname] || null
  }

  // 其他路由配置...
  return null
}
```

## 7. 创建API端点

### 7.1 Token验证API

创建 `src/app/api/embed/validate-token/route.ts`：

```typescript
/**
 * Token验证API端点
 * 用于验证嵌入页面的token
 */

import { NextRequest, NextResponse } from 'next/server'
import { validateTokenOnServer } from '@/lib/auth/token-validator'
import { EmbedSecurity } from '@/lib/embed/security'
import { domainValidator } from '@/lib/embed/domain-validator'

export async function POST(request: NextRequest) {
  try {
    // 验证请求来源
    const referer = request.headers.get('referer')
    if (!domainValidator.isRefererAllowed(referer)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized domain' },
        { status: 403 }
      )
    }

    // 解析请求体
    const body = await request.json()
    const { token } = body

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Token is required' },
        { status: 400 }
      )
    }

    // 验证token格式
    if (!EmbedSecurity.isValidTokenFormat(token)) {
      return NextResponse.json(
        { success: false, error: 'Invalid token format' },
        { status: 400 }
      )
    }

    // 验证token
    const authData = await validateTokenOnServer(token)
    if (!authData) {
      return NextResponse.json(
        { success: false, error: 'Token validation failed' },
        { status: 401 }
      )
    }

    // 返回验证结果（不包含敏感信息）
    return NextResponse.json({
      success: true,
      data: {
        userId: authData.user.id,
        userName: authData.user.name,
        userType: authData.user.user_type,
        permissions: authData.permissions.map(p => p.code),
        roles: authData.roles.map(r => r.roleCode)
      }
    })

  } catch (error) {
    console.error('Token validation API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  // GET方法用于健康检查
  return NextResponse.json({
    success: true,
    message: 'Token validation API is running',
    timestamp: new Date().toISOString()
  })
}
```

### 7.2 健康检查API

创建 `src/app/api/embed/health/route.ts`：

```typescript
/**
 * 嵌入系统健康检查API
 */

import { NextRequest, NextResponse } from 'next/server'
import { domainValidator } from '@/lib/embed/domain-validator'

export async function GET(request: NextRequest) {
  try {
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      embed: {
        allowedDomains: domainValidator.getAllowedDomains(),
        securityEnabled: true,
        communicationEnabled: true
      },
      services: {
        tokenValidation: 'operational',
        domainValidation: 'operational',
        middleware: 'operational'
      }
    }

    return NextResponse.json(healthData)
  } catch (error) {
    console.error('Health check error:', error)
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed'
      },
      { status: 500 }
    )
  }
}
```

### 7.3 嵌入配置API

创建 `src/app/api/embed/config/route.ts`：

```typescript
/**
 * 嵌入配置API
 * 返回嵌入系统的配置信息
 */

import { NextRequest, NextResponse } from 'next/server'
import { domainValidator } from '@/lib/embed/domain-validator'

export async function GET(request: NextRequest) {
  try {
    // 验证请求来源
    const referer = request.headers.get('referer')
    if (!domainValidator.isRefererAllowed(referer)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized domain' },
        { status: 403 }
      )
    }

    const config = {
      success: true,
      data: {
        allowedDomains: domainValidator.getAllowedDomains(),
        availablePages: [
          {
            path: '/embed/dashboard',
            title: '仪表板',
            description: '数据仪表板页面',
            requiredPermissions: ['read']
          },
          {
            path: '/embed/reports',
            title: '报告',
            description: '报告查看页面',
            requiredPermissions: ['read']
          },
          {
            path: '/embed/workspace',
            title: '工作区',
            description: '工作区页面',
            requiredPermissions: ['read', 'write']
          },
          {
            path: '/embed/api-demo',
            title: 'API演示',
            description: 'API功能演示页面',
            requiredPermissions: ['read']
          }
        ],
        communication: {
          supportedMessages: [
            'RESIZE_REQUEST',
            'RESIZE_RESPONSE',
            'NAVIGATION_REQUEST',
            'PAGE_LOADED',
            'PING',
            'PONG'
          ]
        },
        security: {
          tokenRequired: true,
          domainValidation: true,
          secureHeaders: true
        }
      }
    }

    return NextResponse.json(config)
  } catch (error) {
    console.error('Config API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
```

## 8. 添加样式和优化

### 8.1 嵌入页面专用样式

创建 `src/styles/embed.css`：

```css
/**
 * 嵌入页面专用样式
 * 优化iframe显示效果
 */

/* 基础样式重置 */
.embed-html {
  height: 100%;
  margin: 0;
  padding: 0;
}

.embed-body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333333;
}

/* 嵌入容器 */
.embed-container {
  width: 100%;
  min-height: 100vh;
  padding: 1rem;
  box-sizing: border-box;
}

.embed-main {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 0;
}

/* 页面包装器 */
.embed-page-wrapper {
  width: 100%;
  margin: 0;
  padding: 0;
}

.embed-page-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e1e5e9;
}

.embed-page-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
}

.embed-page-content {
  width: 100%;
}

/* 隐藏不必要的元素 */
.embed-container .sidebar,
.embed-container .header-nav,
.embed-container .footer,
.embed-container .breadcrumb,
.embed-container .main-navigation {
  display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .embed-container {
    padding: 0.5rem;
  }

  .embed-page-title {
    font-size: 1.25rem;
  }
}

/* 特定页面样式 */
.embed-dashboard {
  width: 100%;
}

.embed-dashboard .dashboard-grid {
  gap: 1rem;
  margin: 0;
}

.embed-reports {
  width: 100%;
}

.embed-reports .reports-container {
  margin: 0;
  padding: 0;
}

.embed-workspace {
  width: 100%;
}

.embed-workspace .workspace-content {
  margin: 0;
  padding: 0;
}

/* 加载状态 */
.embed-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: #666;
}

.embed-loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: embed-spin 1s linear infinite;
}

@keyframes embed-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.embed-error {
  padding: 2rem;
  text-align: center;
  color: #dc3545;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
}

.embed-error-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.embed-error-message {
  color: #6c757d;
}

/* 优化滚动条 */
.embed-container::-webkit-scrollbar {
  width: 6px;
}

.embed-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.embed-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.embed-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 打印样式 */
@media print {
  .embed-container {
    padding: 0;
  }

  .embed-page-header {
    border-bottom: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .embed-body {
    background: #ffffff;
    color: #000000;
  }

  .embed-page-header {
    border-bottom-color: #000000;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .embed-loading-spinner {
    animation: none;
  }
}
```

### 8.2 性能优化配置

更新 `next.config.ts`，添加嵌入页面优化：

```typescript
/**
 * Next.js 配置，添加嵌入页面优化
 */

import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  // 现有配置...

  // 嵌入页面优化
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons']
  },

  // 安全头部配置
  async headers() {
    return [
      {
        // 为嵌入页面设置特殊的安全头部
        source: '/embed/:path*',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow'
          },
          {
            key: 'Cache-Control',
            value: 'private, no-cache, no-store, must-revalidate'
          }
        ]
      }
    ]
  },

  // 重定向配置
  async redirects() {
    return [
      {
        // 防止直接访问嵌入页面（没有token）
        source: '/embed/:path*',
        has: [
          {
            type: 'query',
            key: 'token',
            value: undefined
          }
        ],
        destination: '/login?error=token_required',
        permanent: false
      }
    ]
  }
}

export default nextConfig
```

## 9. 测试和验证

### 9.1 本地测试脚本

创建测试脚本 `scripts/test-embed.js`：

```javascript
/**
 * 嵌入系统测试脚本
 */

const https = require('https')
const http = require('http')

const TEST_CONFIG = {
  baseUrl: process.env.TEST_BASE_URL || 'http://localhost:3000',
  testToken: process.env.TEST_TOKEN || 'your-test-token',
  allowedDomains: [
    'https://www.goglobalsp.com',
    'https://dev.goglobalsp.com'
  ]
}

async function testEmbedEndpoints() {
  console.log('🧪 开始测试嵌入系统...\n')

  // 测试健康检查
  await testHealthCheck()

  // 测试配置API
  await testConfigAPI()

  // 测试Token验证
  await testTokenValidation()

  // 测试嵌入页面
  await testEmbedPages()

  console.log('\n✅ 所有测试完成')
}

async function testHealthCheck() {
  console.log('📋 测试健康检查API...')
  try {
    const response = await fetch(`${TEST_CONFIG.baseUrl}/api/embed/health`)
    const data = await response.json()

    if (data.status === 'healthy') {
      console.log('✅ 健康检查通过')
    } else {
      console.log('❌ 健康检查失败:', data)
    }
  } catch (error) {
    console.log('❌ 健康检查错误:', error.message)
  }
}

async function testConfigAPI() {
  console.log('⚙️  测试配置API...')
  try {
    const response = await fetch(`${TEST_CONFIG.baseUrl}/api/embed/config`, {
      headers: {
        'referer': TEST_CONFIG.allowedDomains[0]
      }
    })
    const data = await response.json()

    if (data.success && data.data.availablePages) {
      console.log('✅ 配置API正常，可用页面:', data.data.availablePages.length)
    } else {
      console.log('❌ 配置API失败:', data)
    }
  } catch (error) {
    console.log('❌ 配置API错误:', error.message)
  }
}

async function testTokenValidation() {
  console.log('🔐 测试Token验证...')
  try {
    const response = await fetch(`${TEST_CONFIG.baseUrl}/api/embed/validate-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'referer': TEST_CONFIG.allowedDomains[0]
      },
      body: JSON.stringify({
        token: TEST_CONFIG.testToken
      })
    })
    const data = await response.json()

    if (data.success) {
      console.log('✅ Token验证通过')
    } else {
      console.log('❌ Token验证失败:', data.error)
    }
  } catch (error) {
    console.log('❌ Token验证错误:', error.message)
  }
}

async function testEmbedPages() {
  console.log('📄 测试嵌入页面...')
  const pages = [
    '/embed/dashboard',
    '/embed/reports',
    '/embed/workspace',
    '/embed/api-demo'
  ]

  for (const page of pages) {
    try {
      const url = `${TEST_CONFIG.baseUrl}${page}?token=${TEST_CONFIG.testToken}`
      const response = await fetch(url, {
        headers: {
          'referer': TEST_CONFIG.allowedDomains[0]
        }
      })

      if (response.ok) {
        console.log(`✅ ${page} 页面正常`)
      } else {
        console.log(`❌ ${page} 页面错误: ${response.status}`)
      }
    } catch (error) {
      console.log(`❌ ${page} 页面错误:`, error.message)
    }
  }
}

// 运行测试
if (require.main === module) {
  testEmbedEndpoints().catch(console.error)
}

module.exports = { testEmbedEndpoints }
```

### 9.2 添加测试命令

在 `package.json` 中添加测试命令：

```json
{
  "scripts": {
    "test:embed": "node scripts/test-embed.js",
    "test:embed:prod": "TEST_BASE_URL=https://your-production-url.com node scripts/test-embed.js"
  }
}
```

---

*本实现指南提供了完整的代码实现步骤，包括核心库、中间件、组件、API端点、样式和测试脚本。按照这些步骤可以完整实现 Next.js iframe 嵌入系统。*
